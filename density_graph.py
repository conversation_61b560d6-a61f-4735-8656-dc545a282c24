from PyQt6 import QtWidgets, QtCore, QtGui
import pyqtgraph as pg
import pandas as pd
import numpy as np
import logging
import math
import yfinance as yf
from scipy import interpolate
from scipy.interpolate import CubicSpline, BSpline, splrep, splev
from scipy.stats import norm
from scipy.optimize import minimize_scalar
from crosshair_utility import add_price_only_crosshair

logger = logging.getLogger(__name__)

# SABR Model Implementation
class SABRModel:
    """
    SABR (Stochastic Alpha Beta Rho) volatility model implementation.

    The SABR model is used for modeling the evolution of forward rates and their volatilities.
    It provides a more sophisticated approach than Black-Scholes for options pricing,
    particularly for interest rate derivatives and equity options with volatility smile.

    SABR model parameters:
    - alpha: Initial volatility level
    - beta: Elasticity parameter (0 <= beta <= 1)
    - rho: Correlation between forward rate and volatility (-1 <= rho <= 1)
    - nu: Volatility of volatility (vol-of-vol)
    """

    def __init__(self, alpha=0.2, beta=0.5, rho=-0.3, nu=0.4):
        """
        Initialize SABR model with default parameters.

        Args:
            alpha: Initial volatility level (default: 0.2)
            beta: Elasticity parameter (default: 0.5)
            rho: Correlation parameter (default: -0.3)
            nu: Volatility of volatility (default: 0.4)
        """
        self.alpha = alpha
        self.beta = beta
        self.rho = rho
        self.nu = nu

    def sabr_volatility(self, F, K, T):
        """
        Calculate SABR implied volatility.

        Args:
            F: Forward price
            K: Strike price
            T: Time to expiration (in years)

        Returns:
            float: SABR implied volatility
        """
        try:
            # Handle edge cases
            if T <= 0:
                return 0.0
            if abs(F - K) < 1e-10:  # ATM case
                return self._sabr_atm_volatility(F, T)

            # SABR volatility formula
            alpha, beta, rho, nu = self.alpha, self.beta, self.rho, self.nu

            # Calculate intermediate values
            FK = F * K
            logFK = math.log(F / K)

            # Calculate z and x(z)
            z = (nu / alpha) * (FK ** ((1 - beta) / 2)) * logFK

            if abs(z) < 1e-10:
                x_z = 1.0
            else:
                sqrt_term = 1 - 2 * rho * z + z * z
                if sqrt_term <= 0:
                    sqrt_term = 1e-10
                x_z = z / math.log((math.sqrt(sqrt_term) - rho + z) / (1 - rho))

            # Calculate the main volatility components
            numerator = alpha

            # First denominator term
            denom1 = (FK ** ((1 - beta) / 2))

            # Second denominator term (volatility smile adjustment)
            beta_term = (1 - beta) ** 2 / 24 * (logFK ** 2)
            rho_term = rho * beta * nu * alpha / (4 * (FK ** ((1 - beta) / 2)))
            nu_term = (2 - 3 * rho ** 2) * nu ** 2 / 24

            denom2 = 1 + (beta_term + rho_term + nu_term) * T

            # Final SABR volatility
            sabr_vol = (numerator / denom1) * x_z / denom2

            # Ensure positive volatility
            return max(sabr_vol, 1e-6)

        except Exception as e:
            logger.warning(f"Error calculating SABR volatility: {e}")
            return 0.2  # Fallback to 20% volatility

    def _sabr_atm_volatility(self, F, T):
        """Calculate SABR volatility at-the-money (F = K)."""
        try:
            alpha, beta, nu = self.alpha, self.beta, self.nu

            # ATM volatility formula
            atm_vol = alpha / (F ** (1 - beta))

            # Time-dependent adjustment
            time_adj = 1 + ((2 - 3 * self.rho ** 2) * nu ** 2 / 24) * T

            return atm_vol / time_adj

        except Exception as e:
            logger.warning(f"Error calculating SABR ATM volatility: {e}")
            return 0.2

    def calibrate_to_market_data(self, market_data):
        """
        Calibrate SABR parameters to market implied volatilities.

        Args:
            market_data: List of tuples (strike, market_iv, time_to_expiry, forward_price)

        Returns:
            dict: Calibrated SABR parameters
        """
        try:
            if not market_data:
                return {'alpha': self.alpha, 'beta': self.beta, 'rho': self.rho, 'nu': self.nu}

            def objective_function(params):
                alpha, beta, rho, nu = params
                # Ensure parameter bounds
                if not (0 < alpha < 2 and 0 <= beta <= 1 and -1 <= rho <= 1 and 0 < nu < 2):
                    return 1e6

                # Temporarily set parameters
                old_params = (self.alpha, self.beta, self.rho, self.nu)
                self.alpha, self.beta, self.rho, self.nu = alpha, beta, rho, nu

                total_error = 0
                for strike, market_iv, T, F in market_data:
                    try:
                        sabr_iv = self.sabr_volatility(F, strike, T)
                        error = (sabr_iv - market_iv) ** 2
                        total_error += error
                    except:
                        total_error += 1e6

                # Restore old parameters
                self.alpha, self.beta, self.rho, self.nu = old_params
                return total_error

            # Initial guess
            initial_params = [self.alpha, self.beta, self.rho, self.nu]

            # Simple grid search for robustness
            best_params = initial_params
            best_error = objective_function(initial_params)

            # Try different parameter combinations
            for alpha in [0.1, 0.2, 0.3]:
                for beta in [0.3, 0.5, 0.7]:
                    for rho in [-0.5, -0.3, 0.0, 0.3]:
                        for nu in [0.2, 0.4, 0.6]:
                            params = [alpha, beta, rho, nu]
                            error = objective_function(params)
                            if error < best_error:
                                best_error = error
                                best_params = params

            # Update parameters
            self.alpha, self.beta, self.rho, self.nu = best_params

            logger.info(f"SABR calibration completed. Parameters: alpha={self.alpha:.3f}, beta={self.beta:.3f}, rho={self.rho:.3f}, nu={self.nu:.3f}")

            return {
                'alpha': self.alpha,
                'beta': self.beta,
                'rho': self.rho,
                'nu': self.nu,
                'calibration_error': best_error
            }

        except Exception as e:
            logger.error(f"Error calibrating SABR model: {e}")
            return {'alpha': self.alpha, 'beta': self.beta, 'rho': self.rho, 'nu': self.nu}

def calculate_sabr_option_price(flag, S, K, T, r=0.05, sabr_model=None):
    """
    Calculate option price using SABR model within Black-Scholes framework.

    Args:
        flag: 'c' for call, 'p' for put
        S: Current stock price (used as forward price)
        K: Strike price
        T: Time to expiration (in years)
        r: Risk-free rate (default: 0.05)
        sabr_model: SABRModel instance (if None, creates default)

    Returns:
        tuple: (option_price, sabr_implied_volatility)
    """
    try:
        if sabr_model is None:
            sabr_model = SABRModel()

        # Use current price as forward price approximation
        F = S * math.exp(r * T)

        # Get SABR implied volatility
        sabr_iv = sabr_model.sabr_volatility(F, K, T)

        # Use SABR volatility in Black-Scholes framework
        d1 = (math.log(S / K) + (r + 0.5 * sabr_iv ** 2) * T) / (sabr_iv * math.sqrt(T))
        d2 = d1 - sabr_iv * math.sqrt(T)

        if flag.lower() == 'c':
            # Call option
            price = S * norm.cdf(d1) - K * math.exp(-r * T) * norm.cdf(d2)
        else:
            # Put option
            price = K * math.exp(-r * T) * norm.cdf(-d2) - S * norm.cdf(-d1)

        return max(price, 0.0), sabr_iv

    except Exception as e:
        logger.error(f"Error calculating SABR option price: {e}")
        # Fallback to simple Black-Scholes with 20% volatility
        sigma = 0.2
        d1 = (math.log(S / K) + (r + 0.5 * sigma ** 2) * T) / (sigma * math.sqrt(T))
        d2 = d1 - sigma * math.sqrt(T)

        if flag.lower() == 'c':
            price = S * norm.cdf(d1) - K * math.exp(-r * T) * norm.cdf(d2)
        else:
            price = K * math.exp(-r * T) * norm.cdf(-d2) - S * norm.cdf(-d1)

        return max(price, 0.0), sigma

# Import theme colors
try:
    import theme
    THEME_COLORS = theme.DEFAULT
except ImportError:
    # Fallback theme colors if theme module is not available
    THEME_COLORS = {
        'background': '#1e1e1e',           # Dark gray background
        'control_panel': '#2d2d2d',        # Lighter gray control panels
        'borders': '#3e3e3e',              # Border color
        'text': '#e0e0e0',                 # Light gray text
        'primary_accent': '#007acc',       # Primary blue accent
        'secondary_accent': '#0098ff',     # Secondary blue accent (lighter)
        'pressed_accent': '#005c99',       # Pressed state blue (darker)
        'highlight': '#FFC107',            # Material Design Amber
        'selection': '#2979FF',            # Selection highlight color
        'button_radius': '4px',            # Button corner radius
        'button_shadow': '0 4px 6px rgba(0, 122, 204, 0.3)',  # Button shadow
        'bullish': '#4CAF50',              # Material Design Green
        'bearish': '#F44336',              # Material Design Red
    }

class DensityGraphTab(QtWidgets.QWidget):
    """
    Tab for visualizing price density distributions.

    IV Zone Pricing Logic:
    - IV Peak lines are ALWAYS derived from BID prices and BID-based breakthrough points
    - Max Fear, IV Walls, IV Inner Walls, and IV Overflow are ALWAYS derived from ASK prices and ASK-based breakthrough points
    - Each price type (bid/ask) uses its own breakthrough reference to determine which zones to draw
    - Ask-based zones only appear where they don't touch the ask-based breakthrough point
    - Bid-based zones only appear where they don't touch the bid-based breakthrough point
    - The price type toggle (Use Bid/Ask Prices) only affects DISPLAYED curves, NOT the IV zone calculations
    - IV zones maintain their fixed pricing logic regardless of the display toggle setting
    """
    def __init__(self, parent=None, data_tab=None):
        """
        Initialize the density graph tab.

        Args:
            parent: Parent widget
            data_tab: Reference to the Data tab
        """
        super().__init__(parent)
        self.parent = parent
        self.data_tab = data_tab

        # Initialize SABR model for options pricing
        self.sabr_model = SABRModel()
        self.chart_colors = {
            'background': THEME_COLORS['control_panel'],  # Use theme control panel color
            'bullish': THEME_COLORS['bullish'],           # Material Design Green
            'bearish': THEME_COLORS['bearish'],           # Material Design Red
            'text': THEME_COLORS['text'],                 # Light gray text
            'line': '#FFFFFF',                            # White lines
            'histogram': '#3A539B',                       # Blue for histogram
            'density': '#FFC107',                         # Amber for density curve
            'high': '#FFFFFF',                            # White for high prices
            'low': '#FFFFFF',                             # White for low prices
            'long': '#00FF00',                            # Green for long theoretical prices
            'short': '#FF00FF'                            # Magenta for short theoretical prices
        }

        # Initialize theoretical prices as empty list for data subtab
        self.theoretical_prices = []

        # Initialize UI
        self.init_ui()

        # Connect to data tab's calculation mode change signal if available
        if self.data_tab is not None:
            # Check if the data tab has the necessary signals
            if hasattr(self.data_tab, 'percentage_based_btn'):
                self.data_tab.percentage_based_btn.toggled.connect(self.on_calculation_mode_changed)
                self.data_tab.current_price_btn.toggled.connect(self.on_calculation_mode_changed)
                self.data_tab.pivot_price_btn.toggled.connect(self.on_calculation_mode_changed)
                if hasattr(self.data_tab, 'cycle_pivot_btn'):
                    self.data_tab.cycle_pivot_btn.toggled.connect(self.on_calculation_mode_changed)
                logger.info("Connected to data tab calculation mode change signals")

    def init_ui(self):
        """Initialize the user interface."""
        # Main layout
        main_layout = QtWidgets.QVBoxLayout(self)
        main_layout.setContentsMargins(10, 10, 10, 10)

        # Status label
        self.status_label = QtWidgets.QLabel("Density Graph - Ready")
        self.status_label.setStyleSheet(f"color: {THEME_COLORS['text']};")
        main_layout.addWidget(self.status_label)

        # Create plot widget
        self.plot_widget = pg.PlotWidget()
        self.plot_widget.setBackground(self.chart_colors['background'])
        # Empty axis labels to remove confusion
        self.plot_widget.setLabel('left', '', color=self.chart_colors['text'])
        self.plot_widget.setLabel('bottom', '', color=self.chart_colors['text'])

        # Set initial axis ranges
        self.plot_widget.setXRange(-1.5, 6)  # X-axis range from -1.5 to 6
        self.plot_widget.setYRange(-10, 10)  # Initial y-axis range, will be updated later

        # Store the initial view state for reset functionality
        self.initial_view_state = None

        # Enable mouse interaction for zooming
        self.plot_widget.setMouseEnabled(x=True, y=True)
        self.plot_widget.setMenuEnabled(False)  # Still disable context menu

        # Enable mouse wheel zooming but disable rectangular region selection
        view_box = self.plot_widget.getPlotItem().getViewBox()
        view_box.enableAutoRange(enable=False)  # Disable auto-range to maintain manual zoom control
        view_box.setMouseMode(pg.ViewBox.PanMode)  # Use pan mode instead of rect mode to disable left-click zoom

        # Add control buttons (zoom and density toggle)
        controls_layout = QtWidgets.QHBoxLayout()

        # Zoom in button
        self.zoom_in_btn = QtWidgets.QPushButton("Zoom In")
        self.zoom_in_btn.setStyleSheet(f"""
            background-color: {THEME_COLORS['control_panel']};
            color: {THEME_COLORS['text']};
            border: 1px solid {THEME_COLORS['borders']};
            border-radius: {THEME_COLORS['button_radius']};
            padding: 5px;
        """)
        self.zoom_in_btn.clicked.connect(self.zoom_in)
        controls_layout.addWidget(self.zoom_in_btn)

        # Zoom out button
        self.zoom_out_btn = QtWidgets.QPushButton("Zoom Out")
        self.zoom_out_btn.setStyleSheet(f"""
            background-color: {THEME_COLORS['control_panel']};
            color: {THEME_COLORS['text']};
            border: 1px solid {THEME_COLORS['borders']};
            border-radius: {THEME_COLORS['button_radius']};
            padding: 5px;
        """)
        self.zoom_out_btn.clicked.connect(self.zoom_out)
        controls_layout.addWidget(self.zoom_out_btn)

        # Reset zoom button
        self.reset_zoom_btn = QtWidgets.QPushButton("Reset Zoom")
        self.reset_zoom_btn.setStyleSheet(f"""
            background-color: {THEME_COLORS['control_panel']};
            color: {THEME_COLORS['text']};
            border: 1px solid {THEME_COLORS['borders']};
            border-radius: {THEME_COLORS['button_radius']};
            padding: 5px;
        """)
        self.reset_zoom_btn.clicked.connect(self.reset_zoom)
        controls_layout.addWidget(self.reset_zoom_btn)

        # Add a small spacer between zoom and density controls
        controls_layout.addSpacing(20)

        # Create custom style for checkbox controls
        checkbox_style = f"""
            QCheckBox {{
                color: {THEME_COLORS['text']};
                spacing: 8px;
                padding: 2px;
                font-size: 12px;
                font-weight: bold;
                background: transparent;
                border: none;
            }}
            QCheckBox:hover {{
                background: transparent;
            }}
            QCheckBox:checked {{
                background: transparent;
                color: {THEME_COLORS['text']};
            }}
            QCheckBox::indicator {{
                width: 14px;
                height: 14px;
                border-radius: 3px;
            }}
            QCheckBox::indicator:hover {{
                border: 2px solid white;
            }}
            QCheckBox::indicator:checked {{
                background-color: white;
                border: 2px solid black;
            }}
            QCheckBox::indicator:unchecked {{
                background-color: {THEME_COLORS['control_panel']};
                border: 2px solid black;
            }}
        """

        # Create vertical layout for density buttons (stacked like bid/ask buttons)
        density_layout = QtWidgets.QVBoxLayout()
        density_layout.setSpacing(2)  # Small spacing between stacked buttons

        # Toggle density gradient button (top)
        self.show_density_btn = QtWidgets.QCheckBox("Show Density")
        self.show_density_btn.setChecked(True)  # Enabled by default
        self.show_density_btn.setStyleSheet(checkbox_style)
        self.show_density_btn.setMinimumWidth(120)  # Set minimum width to prevent text cutoff
        self.show_density_btn.toggled.connect(self.on_density_toggle)
        density_layout.addWidget(self.show_density_btn)

        # Toggle volume profile button (bottom)
        self.show_volume_profile_btn = QtWidgets.QCheckBox("Show Density Profile")
        self.show_volume_profile_btn.setChecked(False)  # Disabled by default
        self.show_volume_profile_btn.setStyleSheet(checkbox_style)
        self.show_volume_profile_btn.setMinimumWidth(160)  # Set minimum width to prevent text cutoff
        self.show_volume_profile_btn.toggled.connect(self.on_volume_profile_toggle)
        density_layout.addWidget(self.show_volume_profile_btn)

        # Add the vertical density layout to the main horizontal controls layout
        controls_layout.addLayout(density_layout)

        # Add a small spacer between density buttons and option data
        controls_layout.addSpacing(20)

        # Toggle option data (strike, put IV, call IV) button
        self.show_option_data_btn = QtWidgets.QCheckBox("Show Option Data")
        self.show_option_data_btn.setChecked(True)  # Enabled by default
        self.show_option_data_btn.setStyleSheet(checkbox_style)
        self.show_option_data_btn.setMinimumWidth(140)  # Set minimum width to prevent text cutoff
        self.show_option_data_btn.toggled.connect(self.on_option_data_toggle)
        controls_layout.addWidget(self.show_option_data_btn)

        # Add a small spacer between toggles
        controls_layout.addSpacing(20)

        # Toggle Eth's Option Zones button (IV Peak, IV Wall, IV Inner Wall, Max Fear, IV Overflow)
        self.show_eth_zones_btn = QtWidgets.QCheckBox("Eth's Option Zones")
        self.show_eth_zones_btn.setChecked(True)  # Enabled by default
        self.show_eth_zones_btn.setStyleSheet(checkbox_style)
        self.show_eth_zones_btn.setMinimumWidth(160)  # Set minimum width to prevent text cutoff
        self.show_eth_zones_btn.toggled.connect(self.on_eth_zones_toggle)
        controls_layout.addWidget(self.show_eth_zones_btn)

        # Add a small spacer between toggles
        controls_layout.addSpacing(20)

        # Expiration date selector
        expiry_label = QtWidgets.QLabel("Expiry:")
        expiry_label.setStyleSheet(f"color: {THEME_COLORS['text']}; font-weight: bold; font-size: 12px;")
        controls_layout.addWidget(expiry_label)

        self.expiry_selector = QtWidgets.QComboBox()
        self.expiry_selector.setMinimumWidth(120)
        self.expiry_selector.setStyleSheet(f"""
            QComboBox {{
                background-color: {THEME_COLORS['control_panel']};
                color: {THEME_COLORS['text']};
                border: 1px solid {THEME_COLORS['borders']};
                border-radius: 4px;
                padding: 4px 8px;
                font-size: 11px;
            }}
            QComboBox:hover {{
                border: 1px solid {THEME_COLORS['primary_accent']};
            }}
            QComboBox::drop-down {{
                subcontrol-origin: padding;
                subcontrol-position: top right;
                width: 20px;
                border-left: 1px solid {THEME_COLORS['borders']};
            }}
            QComboBox QAbstractItemView {{
                background-color: {THEME_COLORS['control_panel']};
                color: {THEME_COLORS['text']};
                border: 1px solid {THEME_COLORS['borders']};
                selection-background-color: {THEME_COLORS['primary_accent']};
            }}
        """)
        self.expiry_selector.currentIndexChanged.connect(self.on_expiry_changed)
        controls_layout.addWidget(self.expiry_selector)

        # Add a small spacer between expiry and price type controls
        controls_layout.addSpacing(20)

        # Price type selection radio buttons (stacked vertically)
        # Create a vertical layout for the radio buttons
        price_type_layout = QtWidgets.QVBoxLayout()
        price_type_layout.setSpacing(2)  # Small spacing between stacked buttons
        price_type_layout.setContentsMargins(0, 0, 0, 0)  # No margins

        # Create a button group to ensure only one can be selected
        self.price_type_group = QtWidgets.QButtonGroup()

        # Create custom style for price type radio buttons
        price_type_radio_style = f"""
            QRadioButton {{
                color: {THEME_COLORS['text']};
                spacing: 8px;
                padding: 2px;
                font-size: 12px;
                font-weight: bold;
                background: transparent;
                border: none;
            }}
            QRadioButton:hover {{
                background: transparent;
            }}
            QRadioButton:checked {{
                background: transparent;
                color: {THEME_COLORS['text']};
            }}
            QRadioButton::indicator {{
                width: 14px;
                height: 14px;
                border-radius: 7px;
            }}
            QRadioButton::indicator:hover {{
                border: 2px solid white;
            }}
            QRadioButton::indicator:checked {{
                background-color: white;
                border: 2px solid black;
            }}
            QRadioButton::indicator:unchecked {{
                background-color: {THEME_COLORS['control_panel']};
                border: 2px solid black;
            }}
        """

        # Use Bid Prices radio button (default, on top)
        self.use_bid_prices_btn = QtWidgets.QRadioButton("Use Bid Prices")
        self.use_bid_prices_btn.setChecked(True)  # Default to bid prices
        self.use_bid_prices_btn.setStyleSheet(price_type_radio_style)
        self.use_bid_prices_btn.toggled.connect(self.on_price_type_toggle)
        self.price_type_group.addButton(self.use_bid_prices_btn)
        price_type_layout.addWidget(self.use_bid_prices_btn)

        # Use Ask Prices radio button (below bid prices)
        self.use_ask_prices_btn = QtWidgets.QRadioButton("Use Ask Prices")
        self.use_ask_prices_btn.setChecked(False)  # Not default
        self.use_ask_prices_btn.setStyleSheet(price_type_radio_style)
        self.use_ask_prices_btn.toggled.connect(self.on_price_type_toggle)
        self.price_type_group.addButton(self.use_ask_prices_btn)
        price_type_layout.addWidget(self.use_ask_prices_btn)

        # Add the vertical layout to the main horizontal controls layout
        controls_layout.addLayout(price_type_layout)



        # Add spacer to push buttons to the left
        controls_layout.addStretch()

        # Add controls to main layout
        main_layout.addLayout(controls_layout)

        # Add plot widget to main layout
        main_layout.addWidget(self.plot_widget)

        # Initialize crosshair (will be properly set up in generate_density_graph)
        self.crosshair = None

        # Initialize visualization states (enabled by default)
        self.show_density = True
        self.show_option_data = True
        self.show_volume_profile = False
        self.show_eth_zones = True  # Eth's Option Zones enabled by default
        self.use_ask_prices = False  # Use bid prices by default

        # Initialize expiration date selector state
        self.available_expiry_dates = []
        self.selected_expiry_date = None



        # Set background color
        self.setStyleSheet(f"background-color: {THEME_COLORS['background']};")

    def get_calculation_mode(self):
        """
        Get the current calculation mode from the data tab.

        Returns:
            str: The current calculation mode ("percentage_based", "current_price", "pivot_price", or "cycle_pivot")
        """
        if self.data_tab is None:
            logger.warning("No data tab reference available")
            return "percentage_based"  # Default

        if hasattr(self.data_tab, 'calculation_mode'):
            logger.info(f"Using calculation mode from data tab: {self.data_tab.calculation_mode}")
            return self.data_tab.calculation_mode

        # Fallback: determine mode from radio buttons
        if hasattr(self.data_tab, 'percentage_based_btn') and self.data_tab.percentage_based_btn.isChecked():
            return "percentage_based"
        elif hasattr(self.data_tab, 'current_price_btn') and self.data_tab.current_price_btn.isChecked():
            return "current_price"
        elif hasattr(self.data_tab, 'pivot_price_btn') and self.data_tab.pivot_price_btn.isChecked():
            return "pivot_price"
        elif hasattr(self.data_tab, 'cycle_pivot_btn') and self.data_tab.cycle_pivot_btn.isChecked():
            return "cycle_pivot"

        logger.warning("Could not determine calculation mode, using default")
        return "percentage_based"  # Default

    def get_reference_price(self):
        """
        Get the appropriate reference price based on the calculation mode.

        Returns:
            tuple: (price, label, color) where:
                - price is the reference price value
                - label is the description of the price
                - color is the color to use for the infinity line
        """
        try:
            # Get the calculation mode
            calculation_mode = self.get_calculation_mode()

            # If percentage based, use 0 as the reference
            if calculation_mode == "percentage_based":
                logger.info("Using 0 as reference price for percentage based mode")
                return (0, "Percentage Base (0%)", "white")

            # Try to get the market odds tab from the parent
            market_odds_tab = None

            # First check if data_tab has a reference to market_odds_tab
            if self.data_tab is not None and hasattr(self.data_tab, 'market_odds_tab'):
                market_odds_tab = self.data_tab.market_odds_tab
                logger.info("Retrieved market odds tab from data tab")

            # If not found, try to get it from the parent hierarchy
            if market_odds_tab is None and hasattr(self, 'parent') and self.parent is not None:
                parent = self.parent
                while parent is not None:
                    if hasattr(parent, 'market_odds_tab'):
                        market_odds_tab = parent.market_odds_tab
                        logger.info("Retrieved market odds tab from parent hierarchy")
                        break
                    if hasattr(parent, 'parent'):
                        parent = parent.parent()
                    else:
                        break

            # If still not found, try to get it from the data tab's get_market_odds_tab method
            if market_odds_tab is None and self.data_tab is not None and hasattr(self.data_tab, 'get_market_odds_tab'):
                market_odds_tab = self.data_tab.get_market_odds_tab()
                logger.info("Retrieved market odds tab from data tab's get_market_odds_tab method")

            # If we found the market odds tab, get the appropriate price
            if market_odds_tab is not None:
                # For pivot price mode, use the pivot price
                if calculation_mode == "pivot_price":
                    if hasattr(market_odds_tab, 'current_pivot') and market_odds_tab.current_pivot is not None:
                        pivot_price = market_odds_tab.current_pivot
                        logger.info(f"Using pivot price as reference: {pivot_price}")
                        return (pivot_price, f"Extrema Price: {pivot_price:.2f}", "green")

                # For cycle pivot mode, use the latest cycle's pivot price
                elif calculation_mode == "cycle_pivot":
                    if hasattr(market_odds_tab, 'current_pivot') and market_odds_tab.current_pivot is not None:
                        pivot_price = market_odds_tab.current_pivot
                        logger.info(f"Using latest cycle pivot price as reference: {pivot_price}")
                        return (pivot_price, f"Cycle Extrema: {pivot_price:.2f}", "green")

                # For current price mode or default, use the current price
                if hasattr(market_odds_tab, 'data') and market_odds_tab.data is not None and not market_odds_tab.data.empty:
                    # Check if we're viewing historical data and should use historical cutoff
                    if hasattr(market_odds_tab, 'historical_cutoff_index'):
                        historical_cutoff_index = market_odds_tab.historical_cutoff_index
                        current_price = market_odds_tab.data['Close'].iloc[historical_cutoff_index]
                        logger.info(f"Using historical reference price (index {historical_cutoff_index}): {current_price}")
                        return (current_price, f"Historical Price: {current_price:.2f}", "orange")
                    else:
                        current_price = market_odds_tab.data['Close'].iloc[-1]
                        logger.info(f"Using current price as reference: {current_price}")
                        return (current_price, f"Current Price: {current_price:.2f}", "yellow")

            logger.warning("Could not determine reference price, using 0")
            return (0, "Reference (0)", "white")  # Default fallback

        except Exception as e:
            logger.error(f"Error getting reference price: {str(e)}", exc_info=True)
            return (0, "Error", "white")  # Default fallback on error

    def get_data_from_data_tab(self):
        """
        Get data from the Data tab.

        Returns:
            pandas.DataFrame: The data from the Data tab, or None if not available
        """
        if self.data_tab is None:
            logger.warning("No data tab reference available")
            return None

        # Check if the data tab has a table model with data
        if hasattr(self.data_tab, 'table_model') and hasattr(self.data_tab.table_model, '_data'):
            data = self.data_tab.table_model._data
            if data is not None and not data.empty:
                logger.info(f"Retrieved data from data tab: {len(data)} rows")
                return data

        logger.warning("No data available from data tab")
        return None

    def get_latest_category(self, data):
        """
        Get the category from the latest row in the data.

        Args:
            data: DataFrame with the data

        Returns:
            str: The category from the latest row, or None if not available
        """
        if data is None or data.empty or 'Category' not in data.columns:
            logger.warning("No category column in data")
            return None

        # Get the category from the latest row
        latest_category = data['Category'].iloc[-1]
        logger.info(f"Latest category: {latest_category}")
        return latest_category

    def get_rows_with_same_weekday(self, data):
        """
        Get all rows with the same weekday as the latest row.

        Args:
            data: DataFrame with the data

        Returns:
            DataFrame: Subset of data with matching weekday, or empty DataFrame if no match
        """
        if data is None or data.empty:
            logger.warning("Invalid data for weekday matching")
            return pd.DataFrame()

        try:
            # Log all columns for debugging
            logger.info(f"Available columns in data: {data.columns.tolist()}")

            # Create a copy of the data to avoid modifying the original
            data_copy = data.copy()

            # Try to extract date information from the data
            # Method 1: Look for date-related columns
            date_column = None
            for col in data.columns:
                if 'date' in col.lower() or 'time' in col.lower() or 'day' in col.lower():
                    date_column = col
                    logger.info(f"Found potential date column: {col}")
                    break

            # Method 2: Check if the index is a DatetimeIndex
            if date_column is None and isinstance(data.index, pd.DatetimeIndex):
                logger.info("Using DatetimeIndex for weekday matching")
                data_copy['_temp_date'] = data.index
                date_column = '_temp_date'

            # Method 3: Try to parse the first column as a date
            if date_column is None:
                logger.info(f"Trying to parse first column as date: {data.columns[0]}")
                try:
                    data_copy['_temp_date'] = pd.to_datetime(data.iloc[:, 0])
                    date_column = '_temp_date'
                    logger.info("Successfully parsed first column as date")
                except:
                    logger.warning("Could not parse first column as date")

            # Method 4: If all else fails, create a date column from the row index
            if date_column is None:
                logger.info("Creating date column from row index")
                # Get the current date
                import datetime
                today = datetime.datetime.now()

                # Create dates going back from today based on row index
                # This assumes the data is in chronological order with the most recent at the end
                dates = []
                for i in range(len(data)):
                    # Go back i days from today
                    date = today - datetime.timedelta(days=i)
                    dates.append(date)

                # Reverse the list so the oldest date is first
                dates.reverse()

                # Add the dates as a new column
                data_copy['_temp_date'] = dates
                date_column = '_temp_date'
                logger.info("Created date column from row index")

            # Convert date column to datetime if it's not already
            if not pd.api.types.is_datetime64_any_dtype(data_copy[date_column]):
                try:
                    data_copy[date_column] = pd.to_datetime(data_copy[date_column])
                    logger.info(f"Converted {date_column} to datetime")
                except Exception as e:
                    logger.error(f"Error converting date column to datetime: {str(e)}")
                    return pd.DataFrame()

            # Get the weekday of the latest row
            latest_date = data_copy[date_column].iloc[-1]
            latest_weekday = latest_date.weekday()  # 0=Monday, 1=Tuesday, ..., 6=Sunday
            weekday_name = latest_date.strftime('%A')  # Full weekday name

            logger.info(f"Latest date: {latest_date}, weekday: {weekday_name} ({latest_weekday})")

            # Filter rows with the same weekday
            matching_rows = data_copy[data_copy[date_column].dt.weekday == latest_weekday]

            if matching_rows.empty:
                logger.warning(f"No rows found with weekday '{weekday_name}'")
            else:
                logger.info(f"Found {len(matching_rows)} rows with weekday '{weekday_name}'")

            # Exclude the latest row to avoid using the latest high/low prices
            if len(matching_rows) > 1:
                # Find the index of the latest row in the matching_rows DataFrame
                latest_row_index = matching_rows.index.max()
                # Drop the latest row
                matching_rows = matching_rows.drop(latest_row_index)
                logger.info(f"Excluded latest row (index {latest_row_index}) from display")

            # Check if we need to limit the number of occurrences
            occurrence_count = 0
            if hasattr(self.parent, 'get_occurrence_count'):
                occurrence_count = self.parent.get_occurrence_count()

            if occurrence_count > 0 and len(matching_rows) > occurrence_count:
                # Sort by date in descending order to get the most recent occurrences
                matching_rows = matching_rows.sort_values(by=date_column, ascending=False)
                # Take only the specified number of occurrences
                matching_rows = matching_rows.head(occurrence_count)
                logger.info(f"Limited to {occurrence_count} most recent occurrences")

            # Drop the temporary date column if we added it
            if date_column == '_temp_date':
                matching_rows = matching_rows.drop('_temp_date', axis=1)

            return matching_rows

        except Exception as e:
            logger.error(f"Error in get_rows_with_same_weekday: {str(e)}", exc_info=True)
            return pd.DataFrame()

    def get_rows_with_same_category(self, data, category):
        """
        Get rows with the same category as the specified category.

        Args:
            data: DataFrame with the data
            category: The category to match

        Returns:
            pandas.DataFrame: Rows with the same category, or an empty DataFrame if none found
        """
        if data is None or data.empty or 'Category' not in data.columns:
            logger.warning("No category column in data")
            return pd.DataFrame()

        # Get rows with the same category
        matching_rows = data[data['Category'] == category]
        logger.info(f"Found {len(matching_rows)} rows with category '{category}'")

        # Exclude the latest row to avoid using the latest high/low prices
        if len(matching_rows) > 1:
            # Find the index of the latest row in the matching_rows DataFrame
            latest_row_index = matching_rows.index.max()
            # Drop the latest row
            matching_rows = matching_rows.drop(latest_row_index)
            logger.info(f"Excluded latest row (index {latest_row_index}) from display")

        # Check if we need to limit the number of occurrences
        occurrence_count = 0
        if hasattr(self.parent, 'get_occurrence_count'):
            occurrence_count = self.parent.get_occurrence_count()

        if occurrence_count > 0 and len(matching_rows) > occurrence_count:
            # Try to find a date column for sorting
            date_column = None
            for col in matching_rows.columns:
                if 'date' in col.lower() or 'time' in col.lower() or 'day' in col.lower():
                    date_column = col
                    break

            if date_column is not None:
                # Convert to datetime if needed
                if not pd.api.types.is_datetime64_any_dtype(matching_rows[date_column]):
                    try:
                        matching_rows[date_column] = pd.to_datetime(matching_rows[date_column])
                    except:
                        date_column = None

            if date_column is not None:
                # Sort by date in descending order to get the most recent occurrences
                matching_rows = matching_rows.sort_values(by=date_column, ascending=False)
            else:
                # If no date column, sort by index in descending order (assuming higher index = more recent)
                matching_rows = matching_rows.sort_index(ascending=False)

            # Take only the specified number of occurrences
            matching_rows = matching_rows.head(occurrence_count)
            logger.info(f"Limited to {occurrence_count} most recent occurrences")

        return matching_rows

    def generate_density_graph(self):
        """Generate density graph with current price line and category dots."""
        try:
            # Clear the plot and reset crosshair
            self.plot_widget.clear()
            self.crosshair = None

            # Get data from the Data tab
            data = self.get_data_from_data_tab()
            if data is None:
                logger.warning("No data available from data tab")
                self.status_label.setText("Density Graph - No data available")
                return

            # Get the matching mode from the parent (Volatility_Statistics_tab)
            matching_mode = 'hl'  # Default to H/L matching
            if hasattr(self.parent, 'get_matching_mode'):
                matching_mode = self.parent.get_matching_mode()
                logger.info(f"Using matching mode: {matching_mode}")

            # Get matching rows based on the selected mode
            if matching_mode == 'weekday':
                # Use weekday matching
                matching_rows = self.get_rows_with_same_weekday(data)
                if matching_rows.empty:
                    logger.warning("No rows found with the same weekday")
                    self.status_label.setText("Density Graph - No rows found with the same weekday")
                    return

                # Get the weekday name for display
                # Find the date column or use the first column as a fallback
                date_column = None
                for col in data.columns:
                    if 'date' in col.lower() or 'time' in col.lower() or 'day' in col.lower():
                        date_column = col
                        break

                if date_column is None:
                    # Use the index if it's a DatetimeIndex
                    if isinstance(data.index, pd.DatetimeIndex):
                        latest_date = data.index[-1]
                    else:
                        # Use the first column as a last resort
                        try:
                            latest_date = pd.to_datetime(data.iloc[-1, 0])
                        except:
                            # If all else fails, use current date
                            import datetime
                            latest_date = datetime.datetime.now()
                else:
                    # Use the identified date column
                    try:
                        latest_date = pd.to_datetime(data[date_column].iloc[-1])
                    except:
                        # If conversion fails, use current date
                        import datetime
                        latest_date = datetime.datetime.now()

                weekday_name = latest_date.strftime('%A')
                latest_category = weekday_name  # Use weekday name as the category for display

                logger.info(f"Using weekday matching for {weekday_name}")
            else:
                # Use H/L category matching (default)
                # Get the latest category
                latest_category = self.get_latest_category(data)
                if latest_category is None:
                    logger.warning("No category found in latest row")
                    self.status_label.setText("Density Graph - No category found in latest row")
                    return

                logger.info(f"Latest category: {latest_category}")

                # Get rows with the same category
                matching_rows = self.get_rows_with_same_category(data, latest_category)
                if matching_rows.empty:
                    logger.warning(f"No rows found with category '{latest_category}'")
                    self.status_label.setText(f"Density Graph - No rows found with category '{latest_category}'")
                    return

            # Get the reference price (current price or pivot price)
            reference_price_info = self.get_reference_price()
            if reference_price_info is None:
                logger.warning("Could not determine reference price")
                self.status_label.setText("Density Graph - Could not determine reference price")
                return

            reference_price, ref_label_text, _ = reference_price_info
            logger.info(f"Using reference price: {reference_price} ({ref_label_text})")

            # Extract high and low values from matching rows
            # Also get the dates to match highs and lows from the same date
            high_values = []
            low_values = []
            dates = []

            # Check if we're in current price mode and should use theoretical values
            is_current_price_mode = False
            if self.data_tab and hasattr(self.data_tab, 'calculation_mode'):
                is_current_price_mode = self.data_tab.calculation_mode == "current_price"
                logger.info(f"Current calculation mode: {self.data_tab.calculation_mode}")

            # Determine which columns to use for high and low values
            high_column = 'Projected High' if is_current_price_mode and 'Projected High' in matching_rows.columns else 'High'
            low_column = 'Projected Low' if is_current_price_mode and 'Projected Low' in matching_rows.columns else 'Low'

            logger.info(f"Using {high_column}/{low_column} values for density graph")

            # Check if the required columns exist
            if high_column not in matching_rows.columns or low_column not in matching_rows.columns:
                logger.warning(f"Required columns {high_column}/{low_column} not found in matching rows")
                self.status_label.setText(f"Density Graph - Required columns {high_column}/{low_column} not found")
                return

            # Log the column types for debugging
            logger.info(f"Column types: {matching_rows.dtypes.to_dict()}")

            # Extract high and low values and convert to numeric
            try:
                # If 'Time' column exists for date matching
                if 'Time' in matching_rows.columns:
                    for _, row in matching_rows.iterrows():
                        try:
                            high_val = pd.to_numeric(row[high_column])
                            low_val = pd.to_numeric(row[low_column])
                            high_values.append(high_val)
                            low_values.append(low_val)
                            dates.append(row['Time'])
                        except (ValueError, TypeError) as e:
                            logger.warning(f"Error converting values to numeric: {e}")
                else:
                    # If no Time column, just use the values
                    high_series = pd.to_numeric(matching_rows[high_column], errors='coerce')
                    low_series = pd.to_numeric(matching_rows[low_column], errors='coerce')

                    # Drop NaN values
                    high_series = high_series.dropna()
                    low_series = low_series.dropna()

                    high_values = high_series.tolist()
                    low_values = low_series.tolist()

                    # Create dummy dates
                    dates = [f"Date_{i}" for i in range(len(high_values))]

                # Make sure we have valid data
                if not high_values or not low_values:
                    logger.warning("No valid high/low values found after conversion")
                    self.status_label.setText("Density Graph - No valid high/low values found")
                    return

                logger.info(f"Extracted {len(high_values)} high values and {len(low_values)} low values")
                logger.info(f"Sample high values: {high_values[:5]}")
                logger.info(f"Sample low values: {low_values[:5]}")

            except Exception as e:
                logger.error(f"Error extracting high/low values: {str(e)}", exc_info=True)
                self.status_label.setText(f"Density Graph - Error extracting values: {str(e)}")
                return

            # Calculate min and max values for y-axis early for padding calculation
            all_values = list(high_values) + list(low_values) + [reference_price]
            min_val = min(all_values)
            max_val = max(all_values)

            # Calculate padding - fixed at 0.5% above highest high and 0.5% below lowest low
            y_range = max_val - min_val
            padding_percentage = 0.005  # 0.5%
            padding_high = max_val * padding_percentage
            padding_low = min_val * padding_percentage
            # For display purposes, we'll still use a padding variable for label positioning
            padding = y_range * 0.005 if y_range > 0 else 0.5

            # Add vertical lines at x=2 and x=3 (keeping only the ones you didn't ask to remove)
            # Vertical line at x=2 for lows
            low_line = pg.InfiniteLine(pos=2, angle=90, pen=pg.mkPen(color='white', width=2))
            self.plot_widget.addItem(low_line)

            # Vertical line at x=3 for highs
            high_line = pg.InfiniteLine(pos=3, angle=90, pen=pg.mkPen(color='white', width=2))
            self.plot_widget.addItem(high_line)

            # We're not adding dots for current price, low values, or high values
            # This creates a cleaner visualization with only smooth curves
            # The data points are still used to generate the curves, but not displayed as dots

            # Log that we're skipping the dots for a cleaner visualization
            logger.info("Skipping data point dots for cleaner visualization with only smooth curves")

            # Create smooth spline connections between points
            # Use B-spline interpolation for smoother, more natural curves

            # Create professional-grade smooth spline connections between points
            # Use advanced B-spline interpolation with micro-step simulation
            logger.info(f"Creating professional-grade curves with micro-step IV simulation")

            # Use the continuous ribbon approach with enhanced rendering
            # This creates a fluid, ribbon-like effect similar to professional trading platforms
            try:
                # Group data points by their characteristics to create natural clusters
                # This simulates how professional systems group similar market behaviors

                # First, calculate the price range to help with normalization
                price_min = min(min(low_values), reference_price)
                price_max = max(max(high_values), reference_price)
                price_range = price_max - price_min

                # Create a list to store all curves for rendering
                all_curves = []

                # For each pair of low-high values, create a continuous path
                for i in range(len(low_values)):
                    try:
                        # Get the low and high values for this date
                        low_val = low_values[i]
                        high_val = high_values[i]

                        # Apply advanced normalization for deep OTM data
                        # This prevents flattening of wings while maintaining natural curve shapes

                        # Calculate relative distances as percentages of the full price range
                        # This is more robust than simple percentage differences
                        rel_distance_low = abs(low_val - reference_price) / price_range if price_range != 0 else 0
                        rel_distance_high = abs(high_val - reference_price) / price_range if price_range != 0 else 0

                        # Check for extreme outliers but preserve important market data points
                        # We'll keep the lowest low and highest high regardless of distance
                        is_lowest_low = low_val == min(low_values)
                        is_highest_high = high_val == max(high_values)

                        # Only skip if it's not an important market point and is an extreme outlier
                        if (rel_distance_low > 0.6 or rel_distance_high > 0.6) and not (is_lowest_low or is_highest_high):
                            logger.info(f"Skipping outlier point with relative distances: low={rel_distance_low:.2f}, high={rel_distance_high:.2f}")
                            continue

                        # Log when we're keeping important market points despite being outliers
                        if (rel_distance_low > 0.4 or rel_distance_high > 0.4) and (is_lowest_low or is_highest_high):
                            logger.info(f"Keeping {'lowest low' if is_lowest_low else 'highest high'} despite being an outlier: low={rel_distance_low:.2f}, high={rel_distance_high:.2f}")

                        # Apply sophisticated weighting factor based on market behavior patterns
                        # Points closer to ATM should have more natural curves
                        # Points far OTM should have more controlled curves to prevent distortion

                        # Base weight starts at 1.0 (full effect)
                        weight_factor = 1.0

                        # Calculate combined distance metric
                        combined_distance = (rel_distance_low + rel_distance_high) / 2

                        # Apply non-linear scaling for more natural falloff
                        # This creates a more gradual transition for mid-range points

                        # Special handling for lowest low and highest high to ensure they're properly displayed
                        if is_lowest_low or is_highest_high:
                            # Use a higher minimum weight for important market points
                            # This ensures they're rendered with sufficient curvature
                            base_weight = 0.5  # Higher base weight for important points

                            # Still apply some distance-based adjustment but less aggressively
                            if combined_distance > 0.05:
                                # Gentler falloff for important market points
                                weight_factor = base_weight + (1.0 - base_weight) / (1.0 + (combined_distance - 0.05))
                                weight_factor = max(0.5, min(0.9, weight_factor))  # Higher minimum for important points
                        else:
                            # Standard handling for regular points
                            if combined_distance > 0.05:  # More than 5% away from reference
                                # Use sigmoid-like function for smooth transition
                                # This creates more natural weighting than linear scaling
                                weight_factor = 1.0 / (1.0 + 2.0 * (combined_distance - 0.05))
                                weight_factor = max(0.3, min(1.0, weight_factor))  # Clamp between 0.3 and 1.0

                        # Create enhanced path with additional control points
                        # Professional systems use more control points for better curve shaping

                        # Basic path: reference → low → high → reference
                        x_path = [1.5, 2, 3, 3.5]
                        y_path = [reference_price, low_val, high_val, reference_price]

                        # Create a continuous spline through all points with micro-step simulation
                        # This creates the smooth, flowing curves seen in professional platforms
                        x_smooth, y_smooth = self.create_continuous_spline(
                            x_path, y_path,
                            num_output_points=500,  # Increased for sub-pixel resolution
                            smoothness=0.4 * weight_factor  # Apply sophisticated weight factor
                        )

                        if len(x_smooth) > 0 and len(y_smooth) > 0:
                            # Store curve data for rendering
                            # Include flags for lowest low and highest high to control rendering order
                            all_curves.append((x_smooth, y_smooth, combined_distance, is_lowest_low, is_highest_high))
                    except Exception as e:
                        logger.warning(f"Failed to create professional curve for data point {i}: {str(e)}")

                # First, separate important market points (lowest low, highest high) from regular points
                important_curves = [curve for curve in all_curves if curve[3] or curve[4]]  # is_lowest_low or is_highest_high
                regular_curves = [curve for curve in all_curves if not (curve[3] or curve[4])]

                # Sort regular curves by distance so closer curves render on top
                # This creates better visual layering like in professional platforms
                regular_curves.sort(key=lambda x: x[2], reverse=True)

                # Calculate density data regardless of whether we'll display it
                # This ensures we have the data ready if the user toggles the visualization on
                logger.info("Calculating price density data")

                # Use the theoretical price range for the density visualization
                # First check if we have theoretical prices available
                theoretical_min = None
                theoretical_max = None

                # Check if we're using theoretical values and they're available in the data
                is_using_theoretical = False
                if self.data_tab and hasattr(self.data_tab, 'calculation_mode'):
                    is_using_theoretical = (self.data_tab.calculation_mode == "current_price" and
                                           'Projected High' in matching_rows.columns and
                                           'Projected Low' in matching_rows.columns)

                if is_using_theoretical:
                    # Extract theoretical highs and lows
                    try:
                        theoretical_highs = pd.to_numeric(matching_rows['Projected High'], errors='coerce').dropna()
                        theoretical_lows = pd.to_numeric(matching_rows['Projected Low'], errors='coerce').dropna()

                        if not theoretical_highs.empty and not theoretical_lows.empty:
                            theoretical_min = theoretical_lows.min()
                            theoretical_max = theoretical_highs.max()
                            logger.info(f"Using theoretical price range: {theoretical_min:.2f} to {theoretical_max:.2f}")
                    except Exception as e:
                        logger.warning(f"Error extracting theoretical prices: {str(e)}")

                # If theoretical prices aren't available, fall back to lowest low and highest high
                if theoretical_min is None or theoretical_max is None:
                    if 'lowest_low' in locals() and 'highest_high' in locals():
                        density_y_min = lowest_low
                        density_y_max = highest_high
                        logger.info(f"Falling back to lowest low ({lowest_low:.2f}) and highest high ({highest_high:.2f}) for density range")
                    else:
                        # Final fallback to min/max values
                        density_y_min = min_val - padding_low
                        density_y_max = max_val + padding_high
                        logger.info(f"Using fallback range for density: {density_y_min:.2f} to {density_y_max:.2f}")
                else:
                    # Use the theoretical range
                    density_y_min = theoretical_min
                    density_y_max = theoretical_max

                # Create a histogram to track price density across the vertical range
                # Use more bins for finer granularity
                num_bins = 100
                bin_edges = np.linspace(density_y_min, density_y_max, num_bins + 1)
                bin_centers = (bin_edges[:-1] + bin_edges[1:]) / 2
                density_counts = np.zeros(num_bins)

                # Count points in each price bin across all curves
                for curves_list in [regular_curves, important_curves]:
                    for curve_data in curves_list:
                        x_smooth, y_smooth = curve_data[0], curve_data[1]

                        # Sample points along each curve
                        sample_indices = np.linspace(0, len(y_smooth) - 1, min(500, len(y_smooth))).astype(int)
                        sampled_y = y_smooth[sample_indices]

                        # Count points in each bin
                        for y_val in sampled_y:
                            if density_y_min <= y_val <= density_y_max:
                                bin_idx = int((y_val - density_y_min) / (density_y_max - density_y_min) * num_bins)
                                bin_idx = min(bin_idx, num_bins - 1)  # Ensure within bounds
                                density_counts[bin_idx] += 1

                # Normalize the counts to get relative density
                if density_counts.max() > 0:
                    density_counts = density_counts / density_counts.max()

                # Determine the x-range for the sine waves
                # Sine waves go from x=1.5 to x=3.5 based on the code
                bar_x_start = 1.5  # Starting point of sine waves
                bar_x_end = 3.5    # Ending point of sine waves
                bar_width = bar_x_end - bar_x_start  # Full width of sine wave range

                # Only render density visualization if enabled
                if hasattr(self, 'show_density') and self.show_density:
                    logger.info("Rendering density visualization (enabled)")

                    # Use grey color for the density gradient
                    grey_color = QtGui.QColor('#808080')  # Standard grey color

                    # Create horizontal bars with opacity based on density (grey gradient)
                    for i, (bin_center, density) in enumerate(zip(bin_centers, density_counts)):
                        # No minimum density threshold - show all bins
                        # (removed the 0.05 minimum density filter)

                        # Create a grey rectangle spanning the full width of sine waves
                        # with opacity based on density
                        rect = QtWidgets.QGraphicsRectItem(
                            bar_x_start,
                            bin_center - (bin_edges[i+1] - bin_edges[i]) / 2,  # Center on bin
                            bar_width,
                            bin_edges[i+1] - bin_edges[i]  # Height is bin size
                        )

                        # Set the brush to grey with adjusted opacity based on density
                        # Apply a non-linear transformation to make lower densities appear brighter
                        min_opacity = 25  # Minimum opacity for very low density
                        max_opacity = 220  # Maximum opacity for high density

                        # Apply a power function to boost lower density values
                        # Using a square root function to boost lower values (power of 0.5)
                        boosted_density = np.sqrt(density)  # This makes lower densities appear brighter

                        # Scale the boosted density to our opacity range
                        opacity = int(min_opacity + boosted_density * (max_opacity - min_opacity))

                        # Create a QColor with the grey color and our calculated opacity
                        gradient_color = QtGui.QColor(grey_color)
                        gradient_color.setAlpha(opacity)

                        # Apply the grey color with opacity to the rectangle
                        rect.setBrush(QtGui.QBrush(gradient_color))

                        # No border for cleaner look with overlapping bars
                        rect.setPen(pg.mkPen(None))

                        # Add the rectangle to the plot
                        self.plot_widget.addItem(rect)

                    # Add volume profile if enabled
                    if hasattr(self, 'show_volume_profile') and self.show_volume_profile:
                        # Now add the volume profile-like visualization on the left side
                        # This creates a variable-width bar chart based on density
                        volume_x_start = -3.0  # Position further to the left as requested (changed from -0.2)
                        volume_max_width = 2.5  # Increased maximum width for better visibility at the new position

                        # Create volume profile bars with width based on density
                        for i, (bin_center, density) in enumerate(zip(bin_centers, density_counts)):
                            # Skip bins with very low density for cleaner visualization
                            if density < 0.02:  # Lower threshold for volume profile
                                continue

                            # Calculate bar width based on density
                            volume_width = density * volume_max_width

                            # Create a white rectangle with width based on density
                            volume_rect = QtWidgets.QGraphicsRectItem(
                                volume_x_start,
                                bin_center - (bin_edges[i+1] - bin_edges[i]) / 2,  # Center on bin
                                volume_width,
                                bin_edges[i+1] - bin_edges[i]  # Height is bin size
                            )

                            # Set the brush to white with opacity based on density
                            # Higher density = more opaque
                            volume_opacity = int(min(200, 80 + density * 120))  # Scale from 80-200 (out of 255)
                            volume_rect.setBrush(QtGui.QBrush(QtGui.QColor(255, 255, 255, volume_opacity)))

                            # Light gray border for definition
                            volume_rect.setPen(pg.mkPen(QtGui.QColor(200, 200, 200, 50), width=0.5))

                            # Add the volume profile rectangle to the plot
                            self.plot_widget.addItem(volume_rect)

                    logger.info(f"Added horizontal price density visualization with {num_bins} bins")
                else:
                    logger.info("Density visualization is disabled - skipping rendering")

                # Render regular curves on top of the density gradient
                for x_smooth, y_smooth, _, _, _ in regular_curves:
                    # Create a continuous ribbon-like curve with solid white lines
                    # Increase line width to ensure visibility over the gradient
                    ribbon = pg.PlotDataItem(
                        x=x_smooth,
                        y=y_smooth,
                        pen=pg.mkPen(color='white', width=2.0, cosmetic=True),  # Back to original width for better visibility
                        symbolPen=None,
                        symbolBrush=None,
                        symbol=None,
                        antialias=True  # Enable anti-aliasing for smoother curves
                    )
                    self.plot_widget.addItem(ribbon)

                # Render important curves last (they'll be on top) with slightly thicker lines
                for x_smooth, y_smooth, _, is_lowest_low, is_highest_high in important_curves:
                    # Use slightly thicker lines for important market points to ensure visibility
                    line_width = 3.0  # Increased from 2.5 to 3.0 for better visibility

                    # Create a continuous ribbon-like curve with solid white lines
                    ribbon = pg.PlotDataItem(
                        x=x_smooth,
                        y=y_smooth,
                        pen=pg.mkPen(color='white', width=line_width, cosmetic=True),  # Thicker for better visibility
                        symbolPen=None,
                        symbolBrush=None,
                        symbol=None,
                        antialias=True  # Enable anti-aliasing for smoother curves
                    )
                    self.plot_widget.addItem(ribbon)

                    # Log which important curves we're rendering
                    if is_lowest_low:
                        logger.info("Rendered lowest low curve with enhanced visibility")
                    if is_highest_high:
                        logger.info("Rendered highest high curve with enhanced visibility")

                logger.info(f"Successfully rendered {len(all_curves)} professional-grade curves with density gradient")

            except Exception as e:
                logger.warning(f"Failed to create professional-grade curves: {str(e)}")

            # Define colors for the percentile lines
            # Very light colors for 1% levels (highest high and lowest low)
            very_light_green = '#CCFFCC'  # Very light green
            very_light_red = '#FFCCCC'    # Very light red
            # Light colors for 25% levels
            light_green = '#90EE90'  # Light green
            light_red = '#FFA07A'    # Light red/salmon
            # Dark colors for 50% levels
            dark_green = '#006400'   # Dark green
            dark_red = '#8B0000'     # Dark red

            # Calculate percentiles for high values
            if len(high_values) > 0:
                # Find the highest high (1% level)
                highest_high = max(high_values)

                # Add highest high line (1% level) - very light green
                highest_high_line = pg.InfiniteLine(
                    pos=highest_high,
                    angle=0,
                    pen=pg.mkPen(color=very_light_green, width=1, style=QtCore.Qt.PenStyle.DashLine)
                )
                self.plot_widget.addItem(highest_high_line)
                logger.info(f"Added highest high line at {highest_high:.2f}")

                # Add label for the highest high line at x=5.35
                highest_high_label = pg.TextItem(
                    text=f"{highest_high:.2f} 1%",
                    color=very_light_green,
                    anchor=(0, 1)  # Anchor to bottom-left
                )
                highest_high_label.setPos(5.35, highest_high - (padding * 0.5))  # Position at x=5.35
                self.plot_widget.addItem(highest_high_label)

                # Get MaxAvg High from volatility graph for 25% level
                maxavg_high = None
                if hasattr(self.parent, 'volatility_graph_tab'):
                    volatility_levels = self.parent.volatility_graph_tab.get_volatility_levels()
                    maxavg_high = volatility_levels.get('maxavg_high')
                    logger.info(f"Using MaxAvg High from volatility graph: {maxavg_high}")

                # Fallback to local calculation if volatility graph value not available
                if maxavg_high is None:
                    sorted_high_values = np.sort(high_values)[::-1]  # Sort in descending order
                    top_half_count = max(1, len(sorted_high_values) // 2)
                    top_half_highs = sorted_high_values[:top_half_count]
                    maxavg_high = np.mean(top_half_highs)
                    logger.info(f"Using fallback MaxAvg High calculation: {maxavg_high}")

                # Add MaxAvg High line for high values (25% level) - light green
                maxavg_high_line = pg.InfiniteLine(
                    pos=maxavg_high,
                    angle=0,
                    pen=pg.mkPen(color=light_green, width=1, style=QtCore.Qt.PenStyle.DashLine)
                )
                self.plot_widget.addItem(maxavg_high_line)
                logger.info(f"Added MaxAvg High line for high values at {maxavg_high:.2f}")

                # Add label for the MaxAvg High line at x=5.35
                maxavg_high_label = pg.TextItem(
                    text=f"25% {maxavg_high:.2f}",
                    color=light_green,
                    anchor=(0, 1)  # Anchor to bottom-left
                )
                maxavg_high_label.setPos(5.35, maxavg_high - (padding * 0.5))  # Position at x=5.35
                self.plot_widget.addItem(maxavg_high_label)

                # Get High Median from volatility graph for 50% level
                high_median = None
                if hasattr(self.parent, 'volatility_graph_tab'):
                    volatility_levels = self.parent.volatility_graph_tab.get_volatility_levels()
                    high_median = volatility_levels.get('avg_high_lowest_high')
                    logger.info(f"Using High Median from volatility graph: {high_median}")

                # Fallback to local calculation if volatility graph value not available
                if high_median is None:
                    high_series = pd.Series(high_values)
                    high_series = pd.to_numeric(high_series, errors='coerce').dropna()
                    if len(high_series) > 0:
                        sorted_high_values_median = high_series.sort_values().values
                        median_index = (len(sorted_high_values_median) - 1) // 2
                        high_median = sorted_high_values_median[median_index]
                    else:
                        high_median = np.median(high_values)  # Fallback
                    logger.info(f"Using fallback High Median calculation: {high_median}")

                # Add High Median line for high values (50% level) - dark green
                high_median_line = pg.InfiniteLine(
                    pos=high_median,
                    angle=0,
                    pen=pg.mkPen(color=dark_green, width=1, style=QtCore.Qt.PenStyle.DashLine)
                )
                self.plot_widget.addItem(high_median_line)
                logger.info(f"Added High Median line for high values at {high_median:.2f}")

                # Add label for the High Median line at x=5.35
                high_median_label = pg.TextItem(
                    text=f"50% {high_median:.2f}",
                    color=dark_green,
                    anchor=(0, 1)  # Anchor to bottom-left
                )
                high_median_label.setPos(5.35, high_median - (padding * 0.5))  # Position at x=5.35
                self.plot_widget.addItem(high_median_label)

            # Calculate percentiles for low values
            if len(low_values) > 0:
                # Find the lowest low (1% level)
                lowest_low = min(low_values)

                # Add lowest low line (1% level) - very light red
                lowest_low_line = pg.InfiniteLine(
                    pos=lowest_low,
                    angle=0,
                    pen=pg.mkPen(color=very_light_red, width=1, style=QtCore.Qt.PenStyle.DashLine)
                )
                self.plot_widget.addItem(lowest_low_line)
                logger.info(f"Added lowest low line at {lowest_low:.2f}")

                # Add label for the lowest low line at x=5.35
                lowest_low_label = pg.TextItem(
                    text=f"{lowest_low:.2f} 1%",
                    color=very_light_red,
                    anchor=(0, 0)  # Anchor to top-left
                )
                lowest_low_label.setPos(5.35, lowest_low + (padding * 0.5))  # Position at x=5.35
                self.plot_widget.addItem(lowest_low_label)

                # Get MaxAvg Low from volatility graph for 25% level
                maxavg_low = None
                if hasattr(self.parent, 'volatility_graph_tab'):
                    volatility_levels = self.parent.volatility_graph_tab.get_volatility_levels()
                    maxavg_low = volatility_levels.get('maxavg_low')
                    logger.info(f"Using MaxAvg Low from volatility graph: {maxavg_low}")

                # Fallback to local calculation if volatility graph value not available
                if maxavg_low is None:
                    sorted_low_values = np.sort(low_values)  # Sort in ascending order (lowest first)
                    bottom_half_count = max(1, len(sorted_low_values) // 2)
                    bottom_half_lows = sorted_low_values[:bottom_half_count]
                    maxavg_low = np.mean(bottom_half_lows)
                    logger.info(f"Using fallback MaxAvg Low calculation: {maxavg_low}")

                # Add MaxAvg Low line for low values (25% level) - light red
                maxavg_low_line = pg.InfiniteLine(
                    pos=maxavg_low,
                    angle=0,
                    pen=pg.mkPen(color=light_red, width=1, style=QtCore.Qt.PenStyle.DashLine)
                )
                self.plot_widget.addItem(maxavg_low_line)
                logger.info(f"Added MaxAvg Low line for low values at {maxavg_low:.2f}")

                # Add label for the MaxAvg Low line at x=5.35
                maxavg_low_label = pg.TextItem(
                    text=f"25% {maxavg_low:.2f}",
                    color=light_red,
                    anchor=(0, 0)  # Anchor to top-left
                )
                maxavg_low_label.setPos(5.35, maxavg_low + (padding * 0.5))  # Position at x=5.35
                self.plot_widget.addItem(maxavg_low_label)

                # Get Low Median from volatility graph for 50% level
                low_median = None
                if hasattr(self.parent, 'volatility_graph_tab'):
                    volatility_levels = self.parent.volatility_graph_tab.get_volatility_levels()
                    low_median = volatility_levels.get('avg_low_highest_low')
                    logger.info(f"Using Low Median from volatility graph: {low_median}")

                # Fallback to local calculation if volatility graph value not available
                if low_median is None:
                    low_series = pd.Series(low_values)
                    low_series = pd.to_numeric(low_series, errors='coerce').dropna()
                    if len(low_series) > 0:
                        sorted_low_values_median = low_series.sort_values().values
                        median_index = (len(sorted_low_values_median) - 1) // 2
                        low_median = sorted_low_values_median[median_index]
                    else:
                        low_median = np.median(low_values)  # Fallback
                    logger.info(f"Using fallback Low Median calculation: {low_median}")

                # Add Low Median line for low values (50% level) - dark red
                low_median_line = pg.InfiniteLine(
                    pos=low_median,
                    angle=0,
                    pen=pg.mkPen(color=dark_red, width=1, style=QtCore.Qt.PenStyle.DashLine)
                )
                self.plot_widget.addItem(low_median_line)
                logger.info(f"Added Low Median line for low values at {low_median:.2f}")

                # Add label for the Low Median line at x=5.35
                low_median_label = pg.TextItem(
                    text=f"50% {low_median:.2f}",
                    color=dark_red,
                    anchor=(0, 0)  # Anchor to top-left
                )
                low_median_label.setPos(5.35, low_median + (padding * 0.5))  # Position at x=5.35
                self.plot_widget.addItem(low_median_label)

            # Add real current price line (finite line) - white, stops before label area
            # Create a line that goes from x=-0.5 to the right edge, leaving space for the label at x=-1.5
            current_price_line_x = [-0.5, 5.5]  # Start after label area, extend to right edge
            current_price_line_y = [reference_price, reference_price]  # Horizontal line at current price
            current_price_line = pg.PlotDataItem(
                x=current_price_line_x,
                y=current_price_line_y,
                pen=pg.mkPen(color='white', width=2),
                symbolPen=None,
                symbolBrush=None,
                symbol=None
            )
            self.plot_widget.addItem(current_price_line)



            # Calculate position for headers - 0.25% above highest high
            header_y_position = highest_high * 1.0025 if 'highest_high' in locals() else reference_price * 1.0025

            # Only add option data if enabled
            if hasattr(self, 'show_option_data') and self.show_option_data:
                # Add column headers for call and put bids and IVs
                # Call price header at x=0 (dynamic label based on price type)
                call_price_header_text = "Call Ask" if self.use_ask_prices else "Call Bid"
                call_price_header = pg.TextItem(
                    text=call_price_header_text,
                    color='white',
                    anchor=(0, 1)  # Bottom-left aligned
                )
                call_price_header.setPos(0, header_y_position)
                self.plot_widget.addItem(call_price_header)

                # Call IV header at x=0.25
                call_iv_header = pg.TextItem(
                    text="Call IV",
                    color='white',
                    anchor=(0, 1)  # Bottom-left aligned
                )
                call_iv_header.setPos(0.25, header_y_position)
                self.plot_widget.addItem(call_iv_header)

                # Strike header at x=-0.5
                strike_header = pg.TextItem(
                    text="Strike",
                    color='white',
                    anchor=(0, 1)  # Bottom-left aligned
                )
                strike_header.setPos(-0.5, header_y_position)
                self.plot_widget.addItem(strike_header)

                # Put price header at x=4.75 (dynamic label based on price type)
                put_price_header_text = "Put Ask" if self.use_ask_prices else "Put Bid"
                put_price_header = pg.TextItem(
                    text=put_price_header_text,
                    color='white',
                    anchor=(0, 1)  # Bottom-left aligned
                )
                put_price_header.setPos(4.75, header_y_position)
                self.plot_widget.addItem(put_price_header)

                # Put IV header at x=5
                put_iv_header = pg.TextItem(
                    text="Put IV",
                    color='white',
                    anchor=(0, 1)  # Bottom-left aligned
                )
                put_iv_header.setPos(5, header_y_position)
                self.plot_widget.addItem(put_iv_header)

            # Get real options data from the option_greeks tab
            try:
                calls_df, puts_df, _ = self.get_options_data()  # Ignore the current price as we already have reference_price
                price_type = "ask" if self.use_ask_prices else "bid"
                logger.info(f"Successfully retrieved options data: {len(calls_df) if calls_df is not None else 0} calls, {len(puts_df) if puts_df is not None else 0} puts (using {price_type} prices)")
            except Exception as e:
                logger.warning(f"Error retrieving options data: {str(e)}. Using mock data instead.")
                calls_df, puts_df = None, None

            # Add strike prices at 0x axis from lowest low to highest high
            # First, determine the range and step size
            if 'lowest_low' in locals() and 'highest_high' in locals():
                price_range = highest_high - lowest_low

                # Always use step size of 1.0 as requested
                step_size = 1.0

                # Round the lowest_low down and highest_high up to the nearest step
                start_price = math.floor(lowest_low / step_size) * step_size
                end_price = math.ceil(highest_high / step_size) * step_size

                # Limit the number of strike prices to display if the range is very large
                # to avoid performance issues
                max_strikes = 100  # Maximum number of strikes to display
                num_strikes = int((end_price - start_price) / step_size) + 1

                if num_strikes > max_strikes:
                    # Calculate a new step size to limit the number of strikes
                    # while still using increments of 1.0 or multiples of it
                    required_step = math.ceil((end_price - start_price) / max_strikes)
                    # Round up to the nearest multiple of 1.0
                    step_size = math.ceil(required_step / 1.0) * 1.0
                    logger.info(f"Adjusted step size to {step_size} to limit the number of strikes")

                    # Recalculate start and end prices with the new step size
                    start_price = math.floor(lowest_low / step_size) * step_size
                    end_price = math.ceil(highest_high / step_size) * step_size

                # Create dictionaries for quick lookup of option data by strike
                call_data_by_strike = {}
                put_data_by_strike = {}

                # If we have real options data, populate the dictionaries and calibrate SABR model
                market_data_for_calibration = []

                if calls_df is not None and not calls_df.empty:
                    for _, row in calls_df.iterrows():
                        if 'strike' in row and 'bid' in row and 'impliedVolatility' in row:
                            strike = row['strike']
                            call_data_by_strike[strike] = {
                                'bid': row['bid'],
                                'ask': row.get('ask', row['bid']),  # Fallback to bid if ask not available
                                'iv': row['impliedVolatility']
                            }

                            # Collect data for SABR calibration
                            if row['impliedVolatility'] > 0:
                                # Calculate time to expiration
                                time_to_expiry = 30.0 / 365.0  # Default
                                if self.selected_expiry_date:
                                    try:
                                        from datetime import datetime
                                        expiry_date = datetime.strptime(self.selected_expiry_date, '%Y-%m-%d')
                                        today = datetime.now()
                                        days_to_expiry = max((expiry_date - today).days, 1)
                                        time_to_expiry = days_to_expiry / 365.0
                                    except:
                                        pass

                                market_data_for_calibration.append((strike, row['impliedVolatility'], time_to_expiry, reference_price))

                if puts_df is not None and not puts_df.empty:
                    for _, row in puts_df.iterrows():
                        if 'strike' in row and 'bid' in row and 'impliedVolatility' in row:
                            strike = row['strike']
                            put_data_by_strike[strike] = {
                                'bid': row['bid'],
                                'ask': row.get('ask', row['bid']),  # Fallback to bid if ask not available
                                'iv': row['impliedVolatility']
                            }

                            # Collect data for SABR calibration (avoid duplicates from same strike)
                            if row['impliedVolatility'] > 0 and not any(data[0] == strike for data in market_data_for_calibration):
                                # Calculate time to expiration
                                time_to_expiry = 30.0 / 365.0  # Default
                                if self.selected_expiry_date:
                                    try:
                                        from datetime import datetime
                                        expiry_date = datetime.strptime(self.selected_expiry_date, '%Y-%m-%d')
                                        today = datetime.now()
                                        days_to_expiry = max((expiry_date - today).days, 1)
                                        time_to_expiry = days_to_expiry / 365.0
                                    except:
                                        pass

                                market_data_for_calibration.append((strike, row['impliedVolatility'], time_to_expiry, reference_price))

                # Calibrate SABR model to market data if we have sufficient data points
                if len(market_data_for_calibration) >= 3:
                    try:
                        logger.info(f"Calibrating SABR model with {len(market_data_for_calibration)} market data points")
                        calibration_result = self.sabr_model.calibrate_to_market_data(market_data_for_calibration)
                        logger.info(f"SABR calibration completed: {calibration_result}")
                    except Exception as e:
                        logger.warning(f"SABR calibration failed: {e}, using default parameters")

                # Validate bid/ask ordering and create sets of valid strikes for sine wave rendering
                # CRITICAL FIX: Generate separate validation sets for bid and ask prices
                # The display toggle should only affect which curves are shown, not which IV zones are calculated
                valid_call_strikes_display = self.validate_call_bid_ask_ordering(call_data_by_strike, self.use_ask_prices)
                valid_put_strikes_display = self.validate_put_bid_ask_ordering(put_data_by_strike, self.use_ask_prices)

                # Always validate both bid and ask prices for IV zone calculations (independent of display toggle)
                valid_call_strikes_bid = self.validate_call_bid_ask_ordering(call_data_by_strike, False)  # Always use bid for validation
                valid_put_strikes_bid = self.validate_put_bid_ask_ordering(put_data_by_strike, False)    # Always use bid for validation
                valid_call_strikes_ask = self.validate_call_bid_ask_ordering(call_data_by_strike, True)   # Always use ask for validation
                valid_put_strikes_ask = self.validate_put_bid_ask_ordering(put_data_by_strike, True)     # Always use ask for validation

                # Combine all valid strikes for comprehensive IV zone calculation
                all_valid_call_strikes = valid_call_strikes_bid.union(valid_call_strikes_ask)
                all_valid_put_strikes = valid_put_strikes_bid.union(valid_put_strikes_ask)

                logger.info(f"Valid call strikes for display: {len(valid_call_strikes_display)} out of {len(call_data_by_strike)}")
                logger.info(f"Valid put strikes for display: {len(valid_put_strikes_display)} out of {len(put_data_by_strike)}")
                logger.info(f"Valid call strikes for bid IV zones: {len(valid_call_strikes_bid)} out of {len(call_data_by_strike)}")
                logger.info(f"Valid put strikes for bid IV zones: {len(valid_put_strikes_bid)} out of {len(put_data_by_strike)}")
                logger.info(f"Valid call strikes for ask IV zones: {len(valid_call_strikes_ask)} out of {len(call_data_by_strike)}")
                logger.info(f"Valid put strikes for ask IV zones: {len(valid_put_strikes_ask)} out of {len(put_data_by_strike)}")
                logger.info(f"All valid call strikes for IV zones: {len(all_valid_call_strikes)} out of {len(call_data_by_strike)}")
                logger.info(f"All valid put strikes for IV zones: {len(all_valid_put_strikes)} out of {len(put_data_by_strike)}")

                # Initialize lists to store call and put curve data for breakthrough detection
                call_curves_data = []  # List of (strike, peak_y, curve_y_values, x_values)
                put_curves_data = []   # List of (strike, trough_y, curve_y_values, x_values)

                # Initialize separate lists for bid price curves (used for IV Peak determination)
                bid_call_curves_data = []
                bid_put_curves_data = []

                # Initialize separate lists for ask price curves (used for IV Walls, Overflow, Max Fear)
                ask_call_curves_data = []
                ask_put_curves_data = []



                # Get the 1% green and red levels for filtering strikes
                # Green 1% level is the highest high, Red 1% level is the lowest low
                green_1_percent_level = max(high_values) if len(high_values) > 0 else float('inf')
                red_1_percent_level = min(low_values) if len(low_values) > 0 else float('-inf')

                logger.info(f"Using 1% levels for strike filtering: Green (highest high) = {green_1_percent_level:.2f}, Red (lowest low) = {red_1_percent_level:.2f}")

                # Add strike price labels at regular intervals
                current_price = start_price
                while current_price <= end_price:
                    # Only show strikes between the 1% green and red levels
                    # Strikes above green 1% level or below red 1% level will be hidden
                    if red_1_percent_level <= current_price <= green_1_percent_level:
                        # Always add a horizontal grid line (very faint)
                        grid_line = pg.InfiniteLine(
                            pos=current_price,
                            angle=0,
                            pen=pg.mkPen(color='#333333', width=1, style=QtCore.Qt.PenStyle.DotLine)
                        )
                        self.plot_widget.addItem(grid_line)

                        # Only add option data if enabled
                        if hasattr(self, 'show_option_data') and self.show_option_data:
                            # Create a strike price label at x=-0.5
                            strike_label = pg.TextItem(
                                text=f"{current_price:.2f}",
                                color='white',
                                anchor=(0, 0.5)  # Anchor to middle-left
                            )
                            # Position the label at x=-0.5 and at the current price level
                            strike_label.setPos(-0.5, current_price)
                            self.plot_widget.addItem(strike_label)

                            # Get real call data for this strike if available
                            call_price = None
                            call_iv = None
                            if current_price in call_data_by_strike:
                                # Use ask price if enabled, otherwise use bid price
                                if self.use_ask_prices:
                                    call_price = call_data_by_strike[current_price]['ask']
                                else:
                                    call_price = call_data_by_strike[current_price]['bid']
                                call_iv = call_data_by_strike[current_price]['iv']

                            # Get real put data for this strike if available
                            put_price = None
                            put_iv = None
                            if current_price in put_data_by_strike:
                                # Use ask price if enabled, otherwise use bid price
                                if self.use_ask_prices:
                                    put_price = put_data_by_strike[current_price]['ask']
                                else:
                                    put_price = put_data_by_strike[current_price]['bid']
                                put_iv = put_data_by_strike[current_price]['iv']

                            # If we don't have real data for this strike, generate SABR-based values
                            if call_price is None or call_iv is None or put_price is None or put_iv is None:
                                # Calculate time to expiration (assume 30 days if not available)
                                time_to_expiry = 30.0 / 365.0  # Default to 30 days
                                if self.selected_expiry_date:
                                    try:
                                        from datetime import datetime
                                        expiry_date = datetime.strptime(self.selected_expiry_date, '%Y-%m-%d')
                                        today = datetime.now()
                                        days_to_expiry = max((expiry_date - today).days, 1)
                                        time_to_expiry = days_to_expiry / 365.0
                                    except:
                                        pass  # Use default

                                # Generate SABR-based implied volatilities and prices
                                if call_iv is None:
                                    # Calculate SABR implied volatility for call
                                    call_iv = self.sabr_model.sabr_volatility(reference_price, current_price, time_to_expiry)
                                    call_iv = round(call_iv, 3)  # Round to 3 decimal places for display

                                if put_iv is None:
                                    # Calculate SABR implied volatility for put
                                    put_iv = self.sabr_model.sabr_volatility(reference_price, current_price, time_to_expiry)
                                    put_iv = round(put_iv, 3)  # Round to 3 decimal places for display

                                # Generate option prices using SABR model if missing
                                if call_price is None:
                                    try:
                                        sabr_call_price, _ = calculate_sabr_option_price('c', reference_price, current_price, time_to_expiry, sabr_model=self.sabr_model)
                                        call_price = round(max(0.01, sabr_call_price), 2)
                                    except:
                                        # Fallback calculation
                                        price_diff = abs(current_price - reference_price)
                                        atm_factor = max(0.01, 1.0 - (price_diff / (price_range / 2)))
                                        call_price = round(max(0.01, 1.0 * atm_factor * (1.5 if current_price < reference_price else 0.5)), 2)

                                if put_price is None:
                                    try:
                                        sabr_put_price, _ = calculate_sabr_option_price('p', reference_price, current_price, time_to_expiry, sabr_model=self.sabr_model)
                                        put_price = round(max(0.01, sabr_put_price), 2)
                                    except:
                                        # Fallback calculation
                                        price_diff = abs(current_price - reference_price)
                                        atm_factor = max(0.01, 1.0 - (price_diff / (price_range / 2)))
                                        put_price = round(max(0.01, 1.0 * atm_factor * (1.5 if current_price > reference_price else 0.5)), 2)

                            # Add call price (bid or ask) at x=0
                            call_price_label = pg.TextItem(
                                text=f"{call_price:.2f}",
                                color='white',
                                anchor=(0, 0.5)  # Left aligned
                            )
                            call_price_label.setPos(0, current_price)
                            self.plot_widget.addItem(call_price_label)

                            # Add call IV at x=0.25
                            call_iv_label = pg.TextItem(
                                text=f"{call_iv:.2f}",  # Display as decimal without percentage sign
                                color='white',
                                anchor=(0, 0.5)  # Left aligned
                            )
                            call_iv_label.setPos(0.25, current_price)
                            self.plot_widget.addItem(call_iv_label)

                            # Add put price (bid or ask) at x=4.75
                            put_price_label = pg.TextItem(
                                text=f"{put_price:.2f}",
                                color='white',
                                anchor=(0, 0.5)  # Left aligned
                            )
                            put_price_label.setPos(4.75, current_price)
                            self.plot_widget.addItem(put_price_label)

                            # Add put IV at x=5
                            put_iv_label = pg.TextItem(
                                text=f"{put_iv:.2f}",  # Display as decimal without percentage sign
                                color='white',
                                anchor=(0, 0.5)  # Left aligned
                            )
                            put_iv_label.setPos(5, current_price)
                            self.plot_widget.addItem(put_iv_label)

                            # Initialize variables to avoid UnboundLocalError
                            call_x_smooth, call_y_smooth = [], []

                            # Only create call sine wave if this strike has properly ordered bid/ask values for display
                            if current_price in valid_call_strikes_display:
                                # Create call line with high and low points
                                # Calculate offset for call line (call_price / 10)
                                call_offset = call_price / 10
                                call_starting_point = current_price + call_price

                                # Call line coordinates: strike price → trough → peak → new price (+10 formula)
                                # Start at x=0.5 with y = strike price
                                # Go to x=0.7 with y = strike - offset (trough)
                                # Go to x=1.3 with y = starting high point + offset (peak)
                                # End at x=1.5 with y = strike + call_price (new price)
                                call_x_coords = [0.5, 0.7, 1.3, 1.5]
                                call_y_coords = [
                                    current_price,                          # Strike price at x=0.5
                                    current_price - call_offset,           # Trough (strike - offset) at x=0.7
                                    call_starting_point + call_offset,     # Peak (starting high point + offset) at x=1.3
                                    call_starting_point                     # New price (strike + call_price) at x=1.5
                                ]

                                # Create B-spline curved line optimized for smooth call peaks
                                # Call constraint: NO peak at new price location (x=1.5)
                                call_x_smooth, call_y_smooth = self.create_constrained_curve(
                                    call_x_coords, call_y_coords,
                                    curve_type='call',  # B-spline optimized for smooth peaks
                                    num_points=100
                                )

                                call_connection_line = pg.PlotDataItem(
                                    x=call_x_smooth,
                                    y=call_y_smooth,
                                    pen=pg.mkPen(color='white', width=1),
                                    symbolPen=None,
                                    symbolBrush=None,
                                    symbol=None
                                )
                                self.plot_widget.addItem(call_connection_line)

                                # Store call curve data for breakthrough detection
                                if len(call_y_smooth) > 0:
                                    call_peak_y = np.max(call_y_smooth)
                                    call_curves_data.append((current_price, call_peak_y, call_y_smooth, call_x_smooth))



                            # Initialize put variables to avoid UnboundLocalError
                            put_x_smooth, put_y_smooth = [], []

                            # Only create put sine wave if this strike has properly ordered bid/ask values for display
                            if current_price in valid_put_strikes_display:
                                # Create put line with high and low points
                                # Calculate offset for put line (put_price / 10)
                                put_offset = put_price / 10
                                put_starting_point = current_price - put_price

                                # Put line coordinates: new price (+10 formula) → trough → peak → strike price
                                # Start at x=3.5 with y = new price (strike - put_price)
                                # Go to x=3.7 with y = trough (new price - offset)
                                # Go to x=4.3 with y = peak (strike + offset)
                                # End at x=4.5 with y = strike price
                                put_x_coords = [3.5, 3.7, 4.3, 4.5]
                                put_y_coords = [
                                    put_starting_point,                     # New price (strike - put_price) at x=3.5
                                    put_starting_point - put_offset,       # Trough (new price - offset) at x=3.7
                                    current_price + put_offset,            # Peak (strike + offset) at x=4.3
                                    current_price                          # Strike price at x=4.5
                                ]

                                # Create B-spline curved line optimized for smooth put troughs
                                # Put constraint: NO trough at new price location (x=3.5)
                                put_x_smooth, put_y_smooth = self.create_constrained_curve(
                                    put_x_coords, put_y_coords,
                                    curve_type='put',  # B-spline optimized for smooth troughs
                                    num_points=100
                                )

                                put_connection_line = pg.PlotDataItem(
                                    x=put_x_smooth,
                                    y=put_y_smooth,
                                    pen=pg.mkPen(color='white', width=1),
                                    symbolPen=None,
                                    symbolBrush=None,
                                    symbol=None
                                )
                                self.plot_widget.addItem(put_connection_line)

                                # Store put curve data for breakthrough detection
                                if len(put_y_smooth) > 0:
                                    put_trough_y = np.min(put_y_smooth)
                                    put_curves_data.append((current_price, put_trough_y, put_y_smooth, put_x_smooth))



                            else:
                                logger.debug(f"Skipping put sine wave for strike {current_price:.2f} due to out-of-order bid/ask values")

                        else:
                            logger.debug(f"Skipping option data for strike {current_price:.2f} - option data disabled")

                        # CRITICAL FIX: Always calculate bid and ask curves for IV zones regardless of option data display toggle
                        # Generate bid call curves for IV Peak determination (independent of display toggle)
                        if current_price in call_data_by_strike and current_price in valid_call_strikes_bid:
                            bid_call_price = call_data_by_strike[current_price]['bid']
                            bid_call_offset = bid_call_price / 10
                            bid_call_starting_point = current_price + bid_call_price

                            # Calculate curve with bid prices for IV Peak determination
                            # Pattern: strike price → trough → peak → new price (+10 formula)
                            bid_call_x_coords = [0.5, 0.7, 1.3, 1.5]
                            bid_call_y_coords = [
                                current_price,                              # Strike price at x=0.5
                                current_price - bid_call_offset,           # Trough at x=0.7
                                bid_call_starting_point + bid_call_offset, # Peak at x=1.3
                                bid_call_starting_point                     # New price at x=1.5
                            ]

                            # Create curved line for bid prices (for IV Peak determination)
                            # Call constraint: NO peak at new price location (x=1.5)
                            bid_call_x_smooth, bid_call_y_smooth = self.create_constrained_curve(
                                bid_call_x_coords, bid_call_y_coords,
                                curve_type='call',  # Ensures no peak at new price location
                                num_points=100
                            )

                            # Store bid call curve data for IV Peak determination
                            if len(bid_call_y_smooth) > 0:
                                bid_call_peak_y = np.max(bid_call_y_smooth)
                                bid_call_curves_data.append((current_price, bid_call_peak_y, bid_call_y_smooth, bid_call_x_smooth))

                        # Generate ask call curves for IV Walls, Overflow, Max Fear determination (independent of display toggle)
                        if current_price in call_data_by_strike and current_price in valid_call_strikes_ask:
                            ask_call_price = call_data_by_strike[current_price]['ask']
                            ask_call_offset = ask_call_price / 10
                            ask_call_starting_point = current_price + ask_call_price

                            # Calculate curve with ask prices for IV Walls, Overflow, Max Fear determination
                            # Pattern: strike price → trough → peak → new price (+10 formula)
                            ask_call_x_coords = [0.5, 0.7, 1.3, 1.5]
                            ask_call_y_coords = [
                                current_price,                              # Strike price at x=0.5
                                current_price - ask_call_offset,           # Trough at x=0.7
                                ask_call_starting_point + ask_call_offset, # Peak at x=1.3
                                ask_call_starting_point                     # New price at x=1.5
                            ]

                            # Create curved line for ask prices (for IV Walls, Overflow, Max Fear determination)
                            # Call constraint: NO peak at new price location (x=1.5)
                            ask_call_x_smooth, ask_call_y_smooth = self.create_constrained_curve(
                                ask_call_x_coords, ask_call_y_coords,
                                curve_type='call',  # Ensures no peak at new price location
                                num_points=100
                            )

                            # Store ask call curve data for IV Walls, Overflow, Max Fear determination
                            if len(ask_call_y_smooth) > 0:
                                ask_call_peak_y = np.max(ask_call_y_smooth)
                                ask_call_curves_data.append((current_price, ask_call_peak_y, ask_call_y_smooth, ask_call_x_smooth))

                        # Generate bid put curves for IV Peak determination (independent of display toggle)
                        if current_price in put_data_by_strike and current_price in valid_put_strikes_bid:
                            bid_put_price = put_data_by_strike[current_price]['bid']
                            bid_put_offset = bid_put_price / 10
                            bid_put_starting_point = current_price - bid_put_price

                            # Calculate curve with bid prices for IV Peak determination
                            # Pattern: new price (+10 formula) → trough → peak → strike price
                            bid_put_x_coords = [3.5, 3.7, 4.3, 4.5]
                            bid_put_y_coords = [
                                bid_put_starting_point,                     # New price at x=3.5
                                bid_put_starting_point - bid_put_offset,   # Trough at x=3.7
                                current_price + bid_put_offset,            # Peak at x=4.3
                                current_price                              # Strike price at x=4.5
                            ]

                            # Create curved line for bid prices (for IV Peak determination)
                            # Put constraint: NO trough at new price location (x=3.5)
                            bid_put_x_smooth, bid_put_y_smooth = self.create_constrained_curve(
                                bid_put_x_coords, bid_put_y_coords,
                                curve_type='put',  # Ensures no trough at new price location
                                num_points=100
                            )

                            # Store bid put curve data for IV Peak determination
                            if len(bid_put_y_smooth) > 0:
                                bid_put_trough_y = np.min(bid_put_y_smooth)
                                bid_put_curves_data.append((current_price, bid_put_trough_y, bid_put_y_smooth, bid_put_x_smooth))

                        # Generate ask put curves for IV Walls, Overflow, Max Fear determination (independent of display toggle)
                        if current_price in put_data_by_strike and current_price in valid_put_strikes_ask:
                            ask_put_price = put_data_by_strike[current_price]['ask']
                            ask_put_offset = ask_put_price / 10
                            ask_put_starting_point = current_price - ask_put_price

                            # Calculate curve with ask prices for IV Walls, Overflow, Max Fear determination
                            # Pattern: new price (+10 formula) → trough → peak → strike price
                            ask_put_x_coords = [3.5, 3.7, 4.3, 4.5]
                            ask_put_y_coords = [
                                ask_put_starting_point,                     # New price at x=3.5
                                ask_put_starting_point - ask_put_offset,   # Trough at x=3.7
                                current_price + ask_put_offset,            # Peak at x=4.3
                                current_price                              # Strike price at x=4.5
                            ]

                            # Create curved line for ask prices (for IV Walls, Overflow, Max Fear determination)
                            # Put constraint: NO trough at new price location (x=3.5)
                            ask_put_x_smooth, ask_put_y_smooth = self.create_constrained_curve(
                                ask_put_x_coords, ask_put_y_coords,
                                curve_type='put',  # Ensures no trough at new price location
                                num_points=100
                            )

                            # Store ask put curve data for IV Walls, Overflow, Max Fear determination
                            if len(ask_put_y_smooth) > 0:
                                ask_put_trough_y = np.min(ask_put_y_smooth)
                                ask_put_curves_data.append((current_price, ask_put_trough_y, ask_put_y_smooth, ask_put_x_smooth))

                    # Move to the next price level
                    current_price += step_size

                # Perform breakthrough detection and add horizontal lines
                # CRITICAL: IV zones are calculated independently of the display price toggle
                # - IV Peaks use bid_call_curves_data and bid_put_curves_data (ALWAYS bid prices)
                # - Max Fear, IV Walls, Inner Walls, Overflow use ask_call_curves_data and ask_put_curves_data (ALWAYS ask prices)
                # - The displayed curves (call_curves_data, put_curves_data) change based on user toggle but don't affect IV zones
                self.detect_and_draw_breakthrough_lines(
                    call_curves_data, put_curves_data,
                    bid_call_curves_data, bid_put_curves_data,
                    ask_call_curves_data, ask_put_curves_data
                )

            # Add current price label at the leftmost part of the graph
            # Only if option data is enabled
            if hasattr(self, 'show_option_data') and self.show_option_data:
                current_price_label = pg.TextItem(
                    text=f"Current Price: {reference_price:.2f}",
                    color='white',
                    anchor=(0, 0.5)  # Anchor to middle-left
                )
                # Position the label at x=-1.5 (left edge) and on the current price line
                current_price_label.setPos(-1.5, reference_price)
                self.plot_widget.addItem(current_price_label)




            # Update min and max values to include level lines
            # Add level values to the list if they exist
            if 'highest_high' in locals():
                all_values.append(highest_high)
            if 'maxavg_high' in locals():
                all_values.append(maxavg_high)
            if 'high_median' in locals():
                all_values.append(high_median)
            if 'lowest_low' in locals():
                all_values.append(lowest_low)
            if 'maxavg_low' in locals():
                all_values.append(maxavg_low)
            if 'low_median' in locals():
                all_values.append(low_median)

            # Recalculate min and max with percentile values included
            min_val = min(all_values)
            max_val = max(all_values)

            # Padding was already calculated earlier

            # Set axis ranges
            self.plot_widget.setXRange(-1.5, 6)  # X-axis range from -1.5 to 6
            self.plot_widget.setYRange(min_val - padding_low, max_val + padding_high)  # y-axis with fixed 0.5% padding

            # Store the initial view state for reset functionality
            self.initial_view_state = (-1.5, 6, min_val - padding_low, max_val + padding_high)

            # Keep mouse interaction enabled for zooming
            self.plot_widget.setMouseEnabled(x=True, y=True)
            self.plot_widget.setMenuEnabled(False)  # Still disable context menu

            # Add price-only crosshair to the plot with mouse tracking enabled
            self.crosshair = add_price_only_crosshair(self.plot_widget, hide_cursor=True, parent=self.parent)

            # Connect viewbox range changed signal to update crosshair when zooming
            self.plot_widget.getPlotItem().vb.sigRangeChanged.connect(self.on_view_range_changed)

            # Update status label
            occurrences = len(matching_rows)
            total_rows = len(data) if data is not None else 0

            # Get the occurrence count limit if set
            occurrence_limit = 0
            if hasattr(self.parent, 'get_occurrence_count'):
                occurrence_limit = self.parent.get_occurrence_count()

            # Check if we're using projected values
            is_using_theoretical = False
            if self.data_tab and hasattr(self.data_tab, 'calculation_mode'):
                is_using_theoretical = (self.data_tab.calculation_mode == "current_price" and
                                       'Projected High' in matching_rows.columns and
                                       'Projected Low' in matching_rows.columns)

            # Get the matching mode for display
            matching_mode_display = "Weekday Matching" if (hasattr(self.parent, 'get_matching_mode') and self.parent.get_matching_mode() == 'weekday') else "H/L Matching"

            # Add indicators to status message
            theoretical_indicator = " (Using Projected High/Low)" if is_using_theoretical else ""
            price_type_indicator = f" (Using {'Ask' if self.use_ask_prices else 'Bid'} Prices)" if self.show_option_data else ""

            # Create status message based on whether occurrence limit is set
            if occurrence_limit > 0:
                # If occurrence limit is set, don't show days to load
                status_message = f"Occurrences: {occurrences} (Limited to {occurrence_limit}) Attribute: {latest_category} - {matching_mode_display}{theoretical_indicator}{price_type_indicator}"
            else:
                # If no occurrence limit, show days to load
                status_message = f"Occurrences: {occurrences} Attribute: {latest_category} days to load: {total_rows} - {matching_mode_display}{theoretical_indicator}{price_type_indicator}"

            self.status_label.setText(status_message)

            # Clear parent's statistics box if available
            if hasattr(self.parent, 'clear_statistics_box'):
                self.parent.clear_statistics_box()

            # Update the parent's statistics box if available
            if hasattr(self.parent, 'update_statistics_box'):
                # Calculate price levels for the statistics box
                price_levels = {
                    'highest_high': highest_high if 'highest_high' in locals() else None,
                    'lowest_low': lowest_low if 'lowest_low' in locals() else None,
                    'average_high': np.mean(high_values) if len(high_values) > 0 else None,
                    'average_low': np.mean(low_values) if len(low_values) > 0 else None,
                    'lowest_high': min(high_values) if len(high_values) > 0 else None,
                    'highest_low': max(low_values) if len(low_values) > 0 else None,
                    # Include the raw high and low values for count calculations
                    'high_values': high_values,
                    'low_values': low_values
                }

                # Calculate true average (mean) of all highs
                if len(high_values) > 0:
                    # Convert to pandas Series for easier handling
                    high_series = pd.Series(high_values)

                    # Convert to numeric values, coercing errors to NaN
                    high_series = pd.to_numeric(high_series, errors='coerce')

                    # Drop any NaN values
                    high_series = high_series.dropna()

                    if len(high_series) > 0:
                        # Calculate the true average (mean) of all highs
                        true_avg_high = high_series.mean()
                        logger.info(f"Calculated true average of all highs: {true_avg_high}")

                        # Store for use in the white line labels
                        price_levels['true_avg_high'] = true_avg_high
                    else:
                        price_levels['true_avg_high'] = None
                else:
                    price_levels['true_avg_high'] = None

                # Calculate true average (mean) of all lows
                if len(low_values) > 0:
                    # Convert to pandas Series for easier handling
                    low_series = pd.Series(low_values)

                    # Convert to numeric values, coercing errors to NaN
                    low_series = pd.to_numeric(low_series, errors='coerce')

                    # Drop any NaN values
                    low_series = low_series.dropna()

                    if len(low_series) > 0:
                        # Calculate the true average (mean) of all lows
                        true_avg_low = low_series.mean()
                        logger.info(f"Calculated true average of all lows: {true_avg_low}")

                        # Store for use in the white line labels
                        price_levels['true_avg_low'] = true_avg_low
                    else:
                        price_levels['true_avg_low'] = None
                else:
                    price_levels['true_avg_low'] = None

                # Calculate median of all highs (long median)
                if len(high_values) > 0:
                    # Convert to pandas Series for easier handling
                    high_series = pd.Series(high_values)

                    # Convert to numeric values, coercing errors to NaN
                    high_series = pd.to_numeric(high_series, errors='coerce')

                    # Drop any NaN values
                    high_series = high_series.dropna()

                    if len(high_series) > 0:
                        # Sort the values
                        sorted_high_values = high_series.sort_values().values

                        # Get the middle element (for even counts, take the lower middle element)
                        # For example, with 10 highs, take the 5th high (index 4)
                        median_index = (len(sorted_high_values) - 1) // 2

                        # Get the true median value
                        median_value = sorted_high_values[median_index]

                        # Store the median value
                        price_levels['avg_high_lowest_high'] = median_value

                        logger.info(f"Calculated long median (median of all highs) from {len(high_values)} values: {price_levels['avg_high_lowest_high']}")
                    else:
                        price_levels['avg_high_lowest_high'] = None
                else:
                    price_levels['avg_high_lowest_high'] = None

                # Calculate median of all lows (short median)
                if len(low_values) > 0:
                    # Convert to pandas Series for easier handling
                    low_series = pd.Series(low_values)

                    # Convert to numeric values, coercing errors to NaN
                    low_series = pd.to_numeric(low_series, errors='coerce')

                    # Drop any NaN values
                    low_series = low_series.dropna()

                    if len(low_series) > 0:
                        # Sort the values
                        sorted_low_values = low_series.sort_values().values

                        # Get the middle element (for even counts, take the lower middle element)
                        # For example, with 4 lows, take the 2nd low (index 1)
                        median_index = (len(sorted_low_values) - 1) // 2

                        # Get the true median value
                        median_value = sorted_low_values[median_index]

                        # Store the median value
                        price_levels['avg_low_highest_low'] = median_value

                        logger.info(f"Calculated short median (median of all lows) from {len(low_values)} values: {price_levels['avg_low_highest_low']}")
                    else:
                        price_levels['avg_low_highest_low'] = None
                else:
                    price_levels['avg_low_highest_low'] = None

                # Calculate apex (median between highest high and lowest low)
                if price_levels['highest_high'] is not None and price_levels['lowest_low'] is not None:
                    price_levels['apex'] = (price_levels['highest_high'] + price_levels['lowest_low']) / 2
                    logger.info(f"Calculated apex: {price_levels['apex']}")
                else:
                    price_levels['apex'] = None

                # Calculate maxavg for highs (average of top 50% highs/highest highs) - State-of-the-art
                if len(high_values) > 0:
                    # Convert to pandas Series for robust data validation
                    high_series = pd.Series(high_values)

                    # Convert to numeric values, coercing errors to NaN
                    high_series = pd.to_numeric(high_series, errors='coerce')

                    # Drop any NaN or infinite values
                    high_series = high_series.dropna()
                    high_series = high_series[np.isfinite(high_series)]

                    if len(high_series) > 0:
                        # Sort high values in descending order for top percentile calculation
                        sorted_high_values = high_series.sort_values(ascending=False).values
                        # Take the top 50% (use ceiling to ensure at least 1 value for small datasets)
                        top_half_count = max(1, int(np.ceil(len(sorted_high_values) / 2)))
                        top_half_highs = sorted_high_values[:top_half_count]
                        # Calculate robust average
                        price_levels['maxavg_high'] = np.mean(top_half_highs)
                        logger.info(f"Calculated maxavg_high from {top_half_count}/{len(high_values)} values: {price_levels['maxavg_high']:.6f}")
                    else:
                        price_levels['maxavg_high'] = None
                        logger.warning("No valid high values after data cleaning for maxavg_high")
                else:
                    price_levels['maxavg_high'] = None

                # Calculate maxavg for lows (average of bottom 50% lowest lows) - State-of-the-art
                if len(low_values) > 0:
                    # Convert to pandas Series for robust data validation
                    low_series = pd.Series(low_values)

                    # Convert to numeric values, coercing errors to NaN
                    low_series = pd.to_numeric(low_series, errors='coerce')

                    # Drop any NaN or infinite values
                    low_series = low_series.dropna()
                    low_series = low_series[np.isfinite(low_series)]

                    if len(low_series) > 0:
                        # Sort low values in ascending order for bottom percentile calculation
                        sorted_low_values = low_series.sort_values(ascending=True).values
                        # Take the bottom 50% (lowest lows) - use ceiling to ensure at least 1 value
                        bottom_half_count = max(1, int(np.ceil(len(sorted_low_values) / 2)))
                        bottom_half_lows = sorted_low_values[:bottom_half_count]
                        # Calculate robust average
                        price_levels['maxavg_low'] = np.mean(bottom_half_lows)
                        logger.info(f"Calculated maxavg_low from {bottom_half_count}/{len(low_values)} values: {price_levels['maxavg_low']:.6f}")
                    else:
                        price_levels['maxavg_low'] = None
                        logger.warning("No valid low values after data cleaning for maxavg_low")
                else:
                    price_levels['maxavg_low'] = None

                # Calculate minavg for highs (average of bottom 50% highs/lowest highs) - State-of-the-art
                if len(high_values) > 0:
                    # Convert to pandas Series for robust data validation
                    high_series = pd.Series(high_values)

                    # Convert to numeric values, coercing errors to NaN
                    high_series = pd.to_numeric(high_series, errors='coerce')

                    # Drop any NaN or infinite values
                    high_series = high_series.dropna()
                    high_series = high_series[np.isfinite(high_series)]

                    if len(high_series) > 0:
                        # Sort high values in ascending order for bottom percentile calculation
                        sorted_high_values = high_series.sort_values(ascending=True).values
                        # Take the bottom 50% (lowest highs) - use ceiling to ensure at least 1 value
                        bottom_half_count = max(1, int(np.ceil(len(sorted_high_values) / 2)))
                        bottom_half_highs = sorted_high_values[:bottom_half_count]
                        # Calculate robust average
                        price_levels['minavg_high'] = np.mean(bottom_half_highs)
                        logger.info(f"Calculated minavg_high from {bottom_half_count}/{len(high_values)} values: {price_levels['minavg_high']:.6f}")
                    else:
                        price_levels['minavg_high'] = None
                        logger.warning("No valid high values after data cleaning for minavg_high")
                else:
                    price_levels['minavg_high'] = None

                # Calculate minavg for lows (average of top 50% of lows/highest lows) - State-of-the-art
                if len(low_values) > 0:
                    # Convert to pandas Series for robust data validation
                    low_series = pd.Series(low_values)

                    # Convert to numeric values, coercing errors to NaN
                    low_series = pd.to_numeric(low_series, errors='coerce')

                    # Drop any NaN or infinite values
                    low_series = low_series.dropna()
                    low_series = low_series[np.isfinite(low_series)]

                    if len(low_series) > 0:
                        # Sort low values in descending order for top percentile calculation
                        sorted_low_values = low_series.sort_values(ascending=False).values
                        # Take the top 50% (highest lows) - use ceiling to ensure at least 1 value
                        top_half_count = max(1, int(np.ceil(len(sorted_low_values) / 2)))
                        top_half_lows = sorted_low_values[:top_half_count]
                        # Calculate robust average
                        price_levels['minavg_low'] = np.mean(top_half_lows)
                        logger.info(f"Calculated minavg_low from {top_half_count}/{len(low_values)} values: {price_levels['minavg_low']:.6f}")
                    else:
                        price_levels['minavg_low'] = None
                        logger.warning("No valid low values after data cleaning for minavg_low")
                else:
                    price_levels['minavg_low'] = None

                # Add wall_long and wall_short values based on maxavg values
                if price_levels.get('maxavg_high') is not None:
                    price_levels['wall_long'] = price_levels['maxavg_high']
                    logger.info(f"Set wall_long to maxavg_high: {price_levels['wall_long']}")
                else:
                    price_levels['wall_long'] = None

                if price_levels.get('maxavg_low') is not None:
                    price_levels['wall_short'] = price_levels['maxavg_low']
                    logger.info(f"Set wall_short to maxavg_low: {price_levels['wall_short']}")
                else:
                    price_levels['wall_short'] = None

                # Extract theoretical price information if available
                theoretical_prices = []
                if is_using_theoretical and 'Projected High' in matching_rows.columns and 'Projected Low' in matching_rows.columns:
                    try:
                        # Get unique indices from the matching rows
                        indices = matching_rows.index.tolist()

                        # Add theoretical prices for each index
                        for idx in indices:
                            row = matching_rows.loc[idx]
                            if pd.notna(row.get('Projected High')):
                                theoretical_prices.append({
                                    'idx': idx,
                                    'type': 'high',
                                    'price': float(row['Projected High'])
                                })
                            if pd.notna(row.get('Projected Low')):
                                theoretical_prices.append({
                                    'idx': idx,
                                    'type': 'low',
                                    'price': float(row['Projected Low'])
                                })
                    except Exception as e:
                        logger.warning(f"Error extracting theoretical prices: {str(e)}")

                # Log the price levels for debugging
                logger.info(f"Calculated price levels for statistics box: {price_levels.keys()}")

                # Store theoretical prices as an attribute for the data subtab
                self.theoretical_prices = theoretical_prices
                # Also store as projected_prices for compatibility with data subtab
                self.projected_prices = theoretical_prices

                # Update the parent's statistics box
                self.parent.update_statistics_box(price_levels, latest_category, occurrences, theoretical_prices)

                # Add historical day range line if viewing historical data
                if hasattr(self.parent, 'is_viewing_historical_data') and self.parent.is_viewing_historical_data():
                    historical_high, historical_low = self.parent.get_historical_day_range()
                    if historical_high is not None and historical_low is not None:
                        try:
                            # Create white vertical line at x=2.35 from historical low to historical high
                            historical_range_line = pg.PlotDataItem(
                                x=[2.35, 2.35],
                                y=[historical_low, historical_high],
                                pen=pg.mkPen(color='white', width=3),
                                symbolPen=None,
                                symbolBrush=None,
                                symbol=None
                            )
                            self.plot_widget.addItem(historical_range_line)

                            # Add labels for the next day's high and low
                            next_day_high_label = pg.TextItem(
                                text=f"Next Day High: {historical_high:.2f}",
                                color='black',
                                fill='white',
                                anchor=(0, 0.5)  # Left-center aligned
                            )
                            next_day_high_label.setPos(2.4, historical_high)
                            self.plot_widget.addItem(next_day_high_label)

                            next_day_low_label = pg.TextItem(
                                text=f"Next Day Low: {historical_low:.2f}",
                                color='black',
                                fill='white',
                                anchor=(0, 0.5)  # Left-center aligned
                            )
                            next_day_low_label.setPos(2.4, historical_low)
                            self.plot_widget.addItem(next_day_low_label)

                            logger.info(f"Added next day range line at x=2.35 from {historical_low:.2f} to {historical_high:.2f}")
                        except Exception as e:
                            logger.warning(f"Failed to create historical day range line: {str(e)}")

                # Plot toggled statistics if available
                if hasattr(self.parent, 'get_toggled_stats_data'):
                    toggled_stats = self.parent.get_toggled_stats_data()
                    if toggled_stats:
                        self.plot_toggled_statistics(toggled_stats, reference_price)

        except Exception as e:
            logger.error(f"Error generating density graph: {str(e)}", exc_info=True)
            self.status_label.setText(f"Error generating density graph: {str(e)}")
            self.plot_widget.clear()

    def get_data_tab(self):
        """Get the Data tab reference."""
        return self.data_tab

    # Options data is now fetched using the unified system that respects Schwab API selection

    def get_options_data(self):
        """
        Get options data using Schwab API when available and selected, otherwise fallback to Yahoo Finance.

        Returns:
            tuple: (calls_df, puts_df, current_price) or (None, None, None) if not available
        """
        try:
            # Get reference price to determine which ticker to use
            reference_price_info = self.get_reference_price()
            if reference_price_info is None:
                logger.warning("Could not determine reference price for options data")
                return None, None, None

            reference_price, _, _ = reference_price_info

            # Try to get the market odds tab to get the ticker
            market_odds_tab = None
            ticker = None

            # First check if data_tab has a reference to market_odds_tab
            if self.data_tab is not None and hasattr(self.data_tab, 'market_odds_tab'):
                market_odds_tab = self.data_tab.market_odds_tab

            # If not found, try to get it from the parent hierarchy
            if market_odds_tab is None and hasattr(self, 'parent') and self.parent is not None:
                parent = self.parent
                while parent is not None:
                    if hasattr(parent, 'market_odds_tab'):
                        market_odds_tab = parent.market_odds_tab
                        break
                    if hasattr(parent, 'parent'):
                        parent = parent.parent()
                    else:
                        break

            # Try to get ticker from market_odds_tab
            if market_odds_tab is not None and hasattr(market_odds_tab, 'ticker'):
                ticker = market_odds_tab.ticker

            # If we couldn't get ticker, use SPY as default
            if ticker is None or ticker == "":
                ticker = "SPY"
                logger.info(f"No ticker found, using default: {ticker}")
            else:
                logger.info(f"Using ticker: {ticker}")

            # Format ticker for indices (remove ^ prefix for Schwab API compatibility)
            original_ticker = ticker
            if ticker.upper() == "SPX":
                ticker = "SPX"  # Schwab uses SPX, not ^SPX
            elif ticker.upper() == "NDX":
                ticker = "NDX"  # Schwab uses NDX, not ^NDX
            elif ticker.upper() == "VIX":
                ticker = "VIX"  # Schwab uses VIX, not ^VIX
            elif ticker.upper() == "DJI":
                ticker = "DJI"  # Schwab uses DJI, not ^DJI
            elif ticker.upper() == "RUT":
                ticker = "RUT"  # Schwab uses RUT, not ^RUT

            # Get current price using the appropriate data source
            current_price = self._get_current_price_for_options(ticker, reference_price)

            # Get available expiration dates and fetch options data
            try:
                # Import the unified options fetching function
                from combined_options_analysis import fetch_options_for_date

                # Get available expiration dates (use Yahoo Finance for this as it's more reliable)
                import yfinance as yf
                yahoo_ticker = f"^{ticker}" if ticker.upper() in ["SPX", "NDX", "VIX", "DJI", "RUT"] else ticker
                stock = yf.Ticker(yahoo_ticker)
                expiry_dates = stock.options

                if not expiry_dates:
                    logger.warning(f"No options data available for {ticker}")
                    return None, None, None

                # Update available expiry dates if they've changed
                if list(expiry_dates) != self.available_expiry_dates:
                    self.fetch_expiry_dates(ticker)

                # Use the selected expiration date, or the nearest one if none selected
                if self.selected_expiry_date and self.selected_expiry_date in expiry_dates:
                    expiry_date = self.selected_expiry_date
                    logger.info(f"Using selected expiration date: {expiry_date} for ticker: {ticker}")
                else:
                    expiry_date = expiry_dates[0]
                    self.selected_expiry_date = expiry_date
                    logger.info(f"Using nearest expiration date: {expiry_date} for ticker: {ticker}")

                    # Update the selector if it's not already set
                    if self.expiry_selector.currentText() != expiry_date:
                        index = self.expiry_selector.findText(expiry_date)
                        if index >= 0:
                            self.expiry_selector.setCurrentIndex(index)

                # Fetch options chain using the unified function (respects Schwab API selection)
                calls_df, puts_df = fetch_options_for_date(ticker, expiry_date, current_price)

                if calls_df is not None and not calls_df.empty and puts_df is not None and not puts_df.empty:
                    # Determine which data source was used
                    data_source = self._get_data_source_used()
                    logger.info(f"Retrieved options data from {data_source}: {len(calls_df)} calls, {len(puts_df)} puts")
                    return calls_df, puts_df, current_price
                else:
                    logger.warning(f"Empty options data returned for {ticker}")

            except Exception as e:
                logger.error(f"Error fetching options data: {str(e)}")

        except Exception as e:
            logger.error(f"Error in get_options_data: {str(e)}")

        logger.warning("No options data available")
        return None, None, None

    def _get_current_price_for_options(self, ticker, fallback_price):
        """
        Get current price using the appropriate data source.

        Args:
            ticker: Stock symbol
            fallback_price: Fallback price if fetching fails

        Returns:
            float: Current price
        """
        try:
            # Check if we should use Schwab API
            use_schwab = self._should_use_schwab_for_options()

            if use_schwab:
                try:
                    from schwab_api import schwab_api
                    quote_data = schwab_api.get_quote(ticker)
                    if quote_data and 'lastPrice' in quote_data:
                        current_price = round(float(quote_data['lastPrice']), 2)
                        logger.info(f"Retrieved current price from Schwab API: {current_price}")
                        return current_price
                except Exception as e:
                    logger.warning(f"Failed to get price from Schwab API: {e}, falling back to Yahoo Finance")

            # Fallback to Yahoo Finance
            import yfinance as yf
            yahoo_ticker = f"^{ticker}" if ticker.upper() in ["SPX", "NDX", "VIX", "DJI", "RUT"] else ticker
            stock = yf.Ticker(yahoo_ticker)

            try:
                price = stock.info.get("regularMarketPrice")
                if price is None:
                    price = stock.fast_info.get("lastPrice")
                if price is not None:
                    current_price = round(float(price), 2)
                    logger.info(f"Retrieved current price from Yahoo Finance: {current_price}")
                    return current_price
            except Exception as e:
                logger.warning(f"Error fetching current price from Yahoo Finance: {str(e)}")

        except Exception as e:
            logger.error(f"Error in _get_current_price_for_options: {str(e)}")

        # Return fallback price if all else fails
        logger.info(f"Using fallback price: {fallback_price}")
        return fallback_price

    def _should_use_schwab_for_options(self):
        """Determine if we should use Schwab API for options data"""
        try:
            # For live data (options and quotes), use Schwab API when available and connected
            # regardless of data source setting (since historical data is forced to Yahoo Finance)
            try:
                from schwab_api import schwab_api
                return schwab_api.is_connected()
            except:
                return False
        except:
            return False

    def _get_data_source_used(self):
        """Get a description of which data source was used"""
        if self._should_use_schwab_for_options():
            return "Schwab API"
        else:
            return "Yahoo Finance"

    def zoom_in(self):
        """Zoom in on the plot by a factor of 0.8 (reducing the visible range by 20%)."""
        # Get the current view range
        x_min, x_max = self.plot_widget.viewRange()[0]
        y_min, y_max = self.plot_widget.viewRange()[1]

        # Calculate the center points
        x_center = (x_min + x_max) / 2
        y_center = (y_min + y_max) / 2

        # Calculate the new ranges (80% of current range)
        x_range = (x_max - x_min) * 0.8
        y_range = (y_max - y_min) * 0.8

        # Set the new ranges centered on the current center
        self.plot_widget.setXRange(x_center - x_range/2, x_center + x_range/2)
        self.plot_widget.setYRange(y_center - y_range/2, y_center + y_range/2)

        # Update status
        self.status_label.setText(f"Density Graph - Zoomed in")

    def zoom_out(self):
        """Zoom out on the plot by a factor of 1.25 (increasing the visible range by 25%)."""
        # Get the current view range
        x_min, x_max = self.plot_widget.viewRange()[0]
        y_min, y_max = self.plot_widget.viewRange()[1]

        # Calculate the center points
        x_center = (x_min + x_max) / 2
        y_center = (y_min + y_max) / 2

        # Calculate the new ranges (125% of current range)
        x_range = (x_max - x_min) * 1.25
        y_range = (y_max - y_min) * 1.25

        # Set the new ranges centered on the current center
        self.plot_widget.setXRange(x_center - x_range/2, x_center + x_range/2)
        self.plot_widget.setYRange(y_center - y_range/2, y_center + y_range/2)

        # Update status
        self.status_label.setText(f"Density Graph - Zoomed out")

    def reset_zoom(self):
        """Reset the zoom to the initial view state."""
        if self.initial_view_state:
            x_min, x_max, y_min, y_max = self.initial_view_state
            self.plot_widget.setXRange(x_min, x_max)
            self.plot_widget.setYRange(y_min, y_max)

            # Update status
            self.status_label.setText(f"Density Graph - Zoom reset")
        else:
            # If initial view state is not set, use default values
            self.plot_widget.setXRange(-1.5, 6)  # X-axis range from -1.5 to 6
            self.plot_widget.setYRange(-10, 10)

            # Update status
            self.status_label.setText(f"Density Graph - Zoom reset (default view)")

    def on_density_toggle(self, checked):
        """
        Handle toggling of the density visualization.

        Args:
            checked: Whether the checkbox is checked
        """
        # Store the density visibility state
        self.show_density = checked

        # Update the status label
        if checked:
            self.status_label.setText("Density Graph - Density visualization enabled")
        else:
            self.status_label.setText("Density Graph - Density visualization disabled")

        # Regenerate the graph to apply the change
        self.generate_density_graph()

    def on_option_data_toggle(self, checked):
        """
        Handle toggling of the option data (strike, put IV, call IV) visualization.

        Args:
            checked: Whether the checkbox is checked
        """
        # Store the option data visibility state
        self.show_option_data = checked

        # Update the status label
        if checked:
            self.status_label.setText("Density Graph - Option data enabled")
        else:
            self.status_label.setText("Density Graph - Option data disabled")

        # Regenerate the graph to apply the change
        self.generate_density_graph()

    def on_volume_profile_toggle(self, checked):
        """
        Handle toggling of the volume profile visualization.

        Args:
            checked: Whether the checkbox is checked
        """
        # Store the volume profile visibility state
        self.show_volume_profile = checked

        # Update the status label
        if checked:
            self.status_label.setText("Density Graph - Volume profile enabled")
        else:
            self.status_label.setText("Density Graph - Volume profile disabled")

        # Regenerate the graph to apply the change
        self.generate_density_graph()

    def on_eth_zones_toggle(self, checked):
        """
        Handle toggling of Eth's Option Zones (IV Peak, IV Wall, IV Inner Wall, Max Fear, IV Overflow).

        Args:
            checked: Whether the checkbox is checked
        """
        self.show_eth_zones = checked
        if checked:
            self.status_label.setText("Density Graph - Eth's Option Zones enabled")
        else:
            self.status_label.setText("Density Graph - Eth's Option Zones disabled")

        # Regenerate the graph to apply the change
        self.generate_density_graph()

    def on_price_type_toggle(self, checked):
        """
        Handle toggling between using bid prices and ask prices for options data.

        Args:
            checked: Whether the radio button is checked
        """
        # Only respond when a button is being checked (not unchecked)
        if not checked:
            return

        # Determine which price type is selected based on which button is checked
        self.use_ask_prices = self.use_ask_prices_btn.isChecked()

        # Update the status label
        if self.use_ask_prices:
            self.status_label.setText("Density Graph - Using ask prices for options")
        else:
            self.status_label.setText("Density Graph - Using bid prices for options")

        # Regenerate the graph to apply the change
        self.generate_density_graph()

    def on_expiry_changed(self, index):
        """
        Handle change in selected expiry date.

        Args:
            index: Index of the selected expiry date
        """
        if index >= 0 and self.expiry_selector.count() > 0:
            self.selected_expiry_date = self.expiry_selector.currentText()
            logger.info(f"Expiry date changed to: {self.selected_expiry_date}")

            # Update status label
            self.status_label.setText(f"Density Graph - Expiry changed to {self.selected_expiry_date}")

            # Regenerate the graph with the new expiry date
            self.generate_density_graph()

    def fetch_expiry_dates(self, ticker):
        """
        Fetch available expiration dates for the given ticker.

        Args:
            ticker: Stock symbol to fetch expiry dates for
        """
        try:
            # Import yfinance for fetching expiry dates
            import yfinance as yf

            # Format ticker for Yahoo Finance
            yahoo_ticker = f"^{ticker}" if ticker.upper() in ["SPX", "NDX", "VIX", "DJI", "RUT"] else ticker
            stock = yf.Ticker(yahoo_ticker)

            # Get available expiry dates
            expiry_dates = stock.options

            if expiry_dates:
                self.available_expiry_dates = list(expiry_dates)

                # Update the expiry selector
                self.expiry_selector.clear()
                self.expiry_selector.addItems(self.available_expiry_dates)

                # Set the first expiry date as selected
                if self.available_expiry_dates:
                    self.selected_expiry_date = self.available_expiry_dates[0]
                    logger.info(f"Fetched {len(self.available_expiry_dates)} expiry dates for {ticker}")
                    logger.info(f"Selected expiry date: {self.selected_expiry_date}")
                else:
                    self.selected_expiry_date = None
                    logger.warning(f"No expiry dates available for {ticker}")
            else:
                self.available_expiry_dates = []
                self.selected_expiry_date = None
                self.expiry_selector.clear()
                logger.warning(f"No options data available for {ticker}")

        except Exception as e:
            logger.error(f"Error fetching expiry dates for {ticker}: {str(e)}")
            self.available_expiry_dates = []
            self.selected_expiry_date = None
            self.expiry_selector.clear()

    def get_selected_expiry_date(self):
        """
        Get the currently selected expiry date.

        Returns:
            str: Selected expiry date, or None if none selected
        """
        return self.selected_expiry_date

    def create_smooth_curve(self, x_start, x_end, y_start, y_end, num_points=50, method='cubic_spline', smoothness=0.5):
        """
        Create a smooth curve between two points using various interpolation methods.

        Args:
            x_start: Starting x-coordinate
            x_end: Ending x-coordinate
            y_start: Starting y-coordinate
            y_end: Ending y-coordinate
            num_points: Number of points to generate for the curve
            method: Interpolation method ('cubic_spline', 'b_spline', or 'sinusoidal')
            smoothness: Controls the smoothness of the curve (0.0 to 1.0)
                        Higher values create more pronounced curves

        Returns:
            tuple: (x_points, y_points) arrays for the smooth curve
        """
        # Generate x points
        x_points = np.linspace(x_start, x_end, num=num_points)

        if method == 'sinusoidal':
            # Original sinusoidal method
            y_diff = y_end - y_start
            y_points = y_start + y_diff * (1 - np.cos(np.pi * (x_points - x_start) / (x_end - x_start))) / 2

        elif method == 'cubic_spline':
            # Create control points for cubic spline
            # Add intermediate control points to shape the curve
            x_range = x_end - x_start
            y_range = y_end - y_start

            # Calculate control points - we use 4 points for cubic spline
            x_controls = [x_start, x_start + x_range * 0.3, x_start + x_range * 0.7, x_end]

            # Adjust the height of control points based on smoothness
            # Higher smoothness creates more pronounced curves
            curve_height = y_range * smoothness

            # Create control points with a natural curve shape
            if y_end > y_start:  # Rising curve
                y_controls = [
                    y_start,
                    y_start + curve_height * 0.5,
                    y_end - curve_height * 0.5,
                    y_end
                ]
            else:  # Falling curve
                y_controls = [
                    y_start,
                    y_start - curve_height * 0.5,
                    y_end + curve_height * 0.5,
                    y_end
                ]

            # Create cubic spline
            cs = CubicSpline(x_controls, y_controls)
            y_points = cs(x_points)

        elif method == 'b_spline':
            # B-spline interpolation
            # Create more control points for B-spline
            num_controls = 5
            x_controls = np.linspace(x_start, x_end, num_controls)

            # Calculate intermediate y values with some randomness for natural look
            y_controls = np.zeros(num_controls)
            y_controls[0] = y_start
            y_controls[-1] = y_end

            # Create intermediate control points with natural variation
            for i in range(1, num_controls-1):
                t = i / (num_controls - 1)
                # Linear interpolation with controlled randomness
                base_y = y_start + (y_end - y_start) * t
                # Add controlled variation based on smoothness
                variation = (y_end - y_start) * smoothness * 0.3 * (0.5 - np.random.random())
                y_controls[i] = base_y + variation

            # Create B-spline
            # k=3 gives a cubic B-spline
            tck = splrep(x_controls, y_controls, k=3)
            y_points = splev(x_points, tck)

        else:
            # Default to cubic spline if method not recognized
            cs = CubicSpline([x_start, x_end], [y_start, y_end])
            y_points = cs(x_points)

        return x_points, y_points

    def create_constrained_curve(self, x_points, y_points, curve_type='call', num_points=100):
        """
        Create a constrained B-spline curve that follows the correct patterns for call and put options.

        Args:
            x_points: Array of x coordinates [4 points]
            y_points: Array of y coordinates [4 points]
            curve_type: 'call' or 'put' to apply appropriate constraints
            num_points: Number of points to generate for the output curve

        Returns:
            tuple: (x_smooth, y_smooth) arrays for the constrained B-spline curve

        Uses cubic B-splines (k=3) for smooth, professional curves.
        B-splines specifically optimized for:
        - Call lines: Smooth peaks (NO peak at new price location)
        - Put lines: Smooth troughs (NO trough at new price location)
        """
        try:
            if len(x_points) != 4 or len(y_points) != 4:
                # Fallback to straight line if not 4 points
                return np.array(x_points), np.array(y_points)

            # Convert to numpy arrays
            x_points = np.array(x_points)
            y_points = np.array(y_points)

            if curve_type == 'call':
                # Call pattern: strike price → trough → peak → new price
                # B-spline optimization: Create smooth, natural peaks
                # Constraint: NO peak at new price location (x_points[3])
                # The actual peak should be at x_points[2], not at the end

                # Create segments with controlled curvature
                x_smooth = np.linspace(x_points[0], x_points[3], num_points)

                # Use B-spline interpolation optimized for smooth peaks
                from scipy.interpolate import splrep, splev

                # Cubic B-spline (k=3) creates smooth, professional peaks
                # s=0 ensures exact interpolation through control points
                tck = splrep(x_points, y_points, k=3, s=0)
                y_smooth = splev(x_smooth, tck)

                # Minimal constraint: only prevent extreme peaks at new price location
                # Let B-spline create natural smooth peaks at the designated location
                # Only intervene if there's a clear violation at the new price point
                final_point_index = -1  # Last point (new price location)
                if len(y_smooth) > 1:
                    # Check if the final point is higher than it should be
                    if y_smooth[final_point_index] > y_points[3] * 1.01:  # 1% tolerance
                        # Gently adjust only the final point to maintain B-spline smoothness
                        y_smooth[final_point_index] = y_points[3]

            elif curve_type == 'put':
                # Put pattern: new price → trough → peak → strike price
                # B-spline optimization: Create smooth, natural troughs
                # Constraint: NO trough at new price location (x_points[0])
                # The actual trough should be at x_points[1], not at the start

                # Create segments with controlled curvature
                x_smooth = np.linspace(x_points[0], x_points[3], num_points)

                # Use B-spline interpolation optimized for smooth troughs
                from scipy.interpolate import splrep, splev

                # Cubic B-spline (k=3) creates smooth, professional troughs
                # s=0 ensures exact interpolation through control points
                tck = splrep(x_points, y_points, k=3, s=0)
                y_smooth = splev(x_smooth, tck)

                # Minimal constraint: only prevent extreme troughs at new price location
                # Let B-spline create natural smooth troughs at the designated location
                # Only intervene if there's a clear violation at the new price point
                first_point_index = 0  # First point (new price location)
                if len(y_smooth) > 1:
                    # Check if the first point is lower than it should be
                    if y_smooth[first_point_index] < y_points[0] * 0.99:  # 1% tolerance
                        # Gently adjust only the first point to maintain B-spline smoothness
                        y_smooth[first_point_index] = y_points[0]

            else:
                # Unknown curve type, fallback to straight line
                return np.array(x_points), np.array(y_points)

            return x_smooth, y_smooth

        except Exception as e:
            logger.warning(f"Error creating constrained curve: {str(e)}")
            # Fallback to straight line connection
            return np.array(x_points), np.array(y_points)

    def create_continuous_spline(self, x_points, y_points, num_output_points=500, smoothness=0.5):
        """
        Create a continuous spline through multiple points with professional-grade rendering.

        Args:
            x_points: Array of x coordinates
            y_points: Array of y coordinates
            num_output_points: Number of points to generate for the output curve
            smoothness: Controls the smoothness of the curve (0.0 to 1.0)

        Returns:
            tuple: (x_out, y_out) arrays for the smooth curve
        """
        if len(x_points) < 2 or len(y_points) < 2:
            return np.array([]), np.array([])

        if len(x_points) != len(y_points):
            return np.array([]), np.array([])

        try:
            # Sort points by x value to ensure proper ordering
            sorted_indices = np.argsort(x_points)
            x_sorted = np.array(x_points)[sorted_indices]
            y_sorted = np.array(y_points)[sorted_indices]

            # Add intermediate points to simulate IV at micro steps between strikes
            # This creates a more continuous, natural flow like professional systems
            x_enhanced = []
            y_enhanced = []

            # For each pair of consecutive points, add micro-step points in between
            for i in range(len(x_sorted) - 1):
                # Add the current point
                x_enhanced.append(x_sorted[i])
                y_enhanced.append(y_sorted[i])

                # Calculate micro steps between this point and the next
                x_start = x_sorted[i]
                x_end = x_sorted[i + 1]
                y_start = y_sorted[i]
                y_end = y_sorted[i + 1]

                # Add micro steps (every 0.05 increment as suggested)
                x_step = 0.05
                x_current = x_start + x_step

                while x_current < x_end:
                    # Calculate y value using local cubic interpolation
                    # This simulates how IV would behave between strikes
                    t = (x_current - x_start) / (x_end - x_start)

                    # Use cubic Hermite interpolation for natural IV simulation
                    # h00, h10, h01, h11 are the Hermite basis functions
                    h00 = 2*t**3 - 3*t**2 + 1
                    h10 = t**3 - 2*t**2 + t
                    h01 = -2*t**3 + 3*t**2
                    h11 = t**3 - t**2

                    # Estimate tangents for natural curve shape
                    # Use finite differences for tangent estimation
                    m0 = 0
                    m1 = 0

                    # If we have points before and after, use them for better tangent estimation
                    if i > 0:
                        m0 = (y_end - y_sorted[i-1]) / (x_end - x_sorted[i-1]) * (x_end - x_start) * 0.5
                    else:
                        m0 = (y_end - y_start) / (x_end - x_start) * (x_end - x_start) * 0.5

                    if i < len(x_sorted) - 2:
                        m1 = (y_sorted[i+2] - y_start) / (x_sorted[i+2] - x_start) * (x_end - x_start) * 0.5
                    else:
                        m1 = (y_end - y_start) / (x_end - x_start) * (x_end - x_start) * 0.5

                    # Calculate interpolated y value
                    y_interp = h00 * y_start + h10 * m0 + h01 * y_end + h11 * m1

                    # Add the micro step point
                    x_enhanced.append(x_current)
                    y_enhanced.append(y_interp)

                    # Move to next micro step
                    x_current += x_step

            # Add the last point
            x_enhanced.append(x_sorted[-1])
            y_enhanced.append(y_sorted[-1])

            # Convert to numpy arrays
            x_enhanced = np.array(x_enhanced)
            y_enhanced = np.array(y_enhanced)

            # For B-spline, we need at least k+1 points for a degree k spline
            # With our enhanced points, we should always have enough points now
            if len(x_enhanced) >= 4:
                # Use B-spline for professional-grade smoothness
                # The smoothness parameter controls how closely the curve follows the points

                # Calculate appropriate smoothness factor
                # For professional rendering, we want a balance between smoothness and accuracy
                # Lower s values make the curve follow points more closely
                s = (1.0 - smoothness) * len(x_enhanced) * 0.1

                # Create B-spline representation with cubic splines (k=3)
                tck = splrep(x_enhanced, y_enhanced, k=3, s=s)

                # Generate output points with sub-pixel resolution
                # Using more points creates smoother rendering with sub-pixel precision
                x_out = np.linspace(x_enhanced[0], x_enhanced[-1], num_output_points)
                y_out = splev(x_out, tck)
            else:
                # Use cubic spline for fewer points
                cs = CubicSpline(x_enhanced, y_enhanced, bc_type='natural')
                x_out = np.linspace(x_enhanced[0], x_enhanced[-1], num_output_points)
                y_out = cs(x_out)

            return x_out, y_out

        except Exception as e:
            logger.warning(f"Failed to create continuous spline: {str(e)}")
            return np.array([]), np.array([])

    def on_view_range_changed(self, *_):
        """
        Handle view range changes when zooming.

        Args:
            *_: Arguments passed by the signal (not used directly)
        """
        # This method is called when the view range changes due to zooming
        # We can use it to update any elements that need to be repositioned

        # If we have a crosshair, make sure it's still visible and properly positioned
        if hasattr(self, 'crosshair') and self.crosshair is not None:
            # The crosshair will automatically update on mouse movement
            # But we can force an update if needed by triggering a mouse move event
            pass  # The crosshair will update automatically on next mouse movement

        # No longer updating status label with zoom information as requested

    def on_calculation_mode_changed(self, checked):
        """
        Handle calculation mode change in the data tab.

        Args:
            checked: Whether the button is checked
        """
        if checked:  # Only respond to the button that was checked (not the ones unchecked)
            logger.info("Calculation mode changed in data tab, refreshing density graph")
            # Regenerate the density graph with the new calculation mode
            self.generate_density_graph()

    def plot_toggled_statistics(self, toggled_stats, reference_price):
        """Plot toggled statistics on the graph

        Args:
            toggled_stats (list): List of dictionaries with statistics data
            reference_price (float): Current reference price
        """
        try:
            if not toggled_stats:
                return

            logger.info(f"Plotting {len(toggled_stats)} toggled statistics")

            # Define colors for different types of stats
            bear_color = self.chart_colors['bearish']
            bull_color = self.chart_colors['bullish']
            long_color = self.chart_colors.get('long', '#00FF00')  # Green for long theoretical
            short_color = self.chart_colors.get('short', '#FF00FF')  # Magenta for short theoretical

            # Plot each toggled statistic
            for stat in toggled_stats:
                if 'price' not in stat or 'type' not in stat:
                    logger.warning(f"Skipping stat without price or type: {stat}")
                    continue

                price = stat['price']
                stat_type = stat['type']

                # Determine color and label based on type
                if stat_type == 'bear':
                    color = bear_color
                    label_prefix = "Bear"
                elif stat_type == 'bull':
                    color = bull_color
                    label_prefix = "Bull"
                elif stat_type == 'long':
                    color = long_color
                    label_prefix = "Long"
                elif stat_type == 'short':
                    color = short_color
                    label_prefix = "Short"
                else:
                    logger.warning(f"Unknown stat type: {stat_type}")
                    continue

                # Create a horizontal line at the price level with win rate if available
                winrate_str = stat.get('winrate_str', '')
                label_text = f"{label_prefix}: {price:.2f} ({winrate_str})" if winrate_str else f"{label_prefix}: {price:.2f}"
                line = pg.InfiniteLine(
                    pos=price,
                    angle=0,  # Horizontal line
                    pen=pg.mkPen(color=color, width=2, style=QtCore.Qt.PenStyle.DashLine),  # Dashed line
                    label=label_text,
                    labelOpts={
                        'position': 0.95,  # Position near the right end
                        'color': color,
                        'movable': False,
                        'fill': (0, 0, 0, 0)  # Transparent background
                    }
                )
                self.plot_widget.addItem(line)

            logger.info("Successfully plotted toggled statistics")
        except Exception as e:
            logger.error(f"Error plotting toggled statistics: {str(e)}", exc_info=True)

    def validate_call_bid_ask_ordering(self, call_data_by_strike, use_ask_prices):
        """
        Validate call bid/ask ordering to determine which strikes should have sine waves.

        For calls: bid/ask should be ordered least to greatest with greatest at smallest strike.
        This means as strike prices decrease, the bid/ask values should increase.

        Args:
            call_data_by_strike: Dictionary of call data by strike price
            use_ask_prices: Boolean indicating whether to use ask prices (True) or bid prices (False)

        Returns:
            set: Set of strike prices that have properly ordered bid/ask values
        """
        if not call_data_by_strike:
            return set()

        try:
            # Get the price type we're using
            price_type = 'ask' if use_ask_prices else 'bid'

            # Create a list of (strike, price) tuples and sort by strike
            strike_price_pairs = []
            for strike, data in call_data_by_strike.items():
                price = data.get(price_type, 0)
                if price is not None and price >= 0:  # Include zero prices
                    strike_price_pairs.append((strike, price))

            # Sort by strike price (ascending)
            strike_price_pairs.sort(key=lambda x: x[0])

            if len(strike_price_pairs) < 2:
                # If we have less than 2 data points, include all
                return set(strike for strike, _ in strike_price_pairs)

            valid_strikes = set()

            # For calls, as strike decreases, bid/ask should increase
            # So we check that each strike has a price >= the next higher strike's price
            # However, we'll be more permissive with zero values
            for i in range(len(strike_price_pairs)):
                current_strike, current_price = strike_price_pairs[i]
                is_valid = True

                # Check against the next strike (higher strike)
                if i < len(strike_price_pairs) - 1:
                    next_strike, next_price = strike_price_pairs[i + 1]
                    # Current price should be >= next price (since current strike < next strike)
                    # But allow zero prices to pass validation
                    if current_price > 0 and next_price > 0 and current_price < next_price:
                        is_valid = False
                        logger.debug(f"Call strike {current_strike:.2f} ({price_type}={current_price:.2f}) is out of order with strike {next_strike:.2f} ({price_type}={next_price:.2f})")

                if is_valid:
                    valid_strikes.add(current_strike)

            logger.info(f"Call {price_type} validation: {len(valid_strikes)} out of {len(strike_price_pairs)} strikes are properly ordered")
            return valid_strikes

        except Exception as e:
            logger.error(f"Error validating call bid/ask ordering: {str(e)}", exc_info=True)
            return set()

    def validate_put_bid_ask_ordering(self, put_data_by_strike, use_ask_prices):
        """
        Validate put bid/ask ordering to determine which strikes should have sine waves.

        For puts: bid/ask should be ordered least to greatest with greatest at biggest strike.
        This means as strike prices increase, the bid/ask values should increase.

        Args:
            put_data_by_strike: Dictionary of put data by strike price
            use_ask_prices: Boolean indicating whether to use ask prices (True) or bid prices (False)

        Returns:
            set: Set of strike prices that have properly ordered bid/ask values
        """
        if not put_data_by_strike:
            return set()

        try:
            # Get the price type we're using
            price_type = 'ask' if use_ask_prices else 'bid'

            # Create a list of (strike, price) tuples and sort by strike
            strike_price_pairs = []
            for strike, data in put_data_by_strike.items():
                price = data.get(price_type, 0)
                if price is not None and price >= 0:  # Include zero prices
                    strike_price_pairs.append((strike, price))

            # Sort by strike price (ascending)
            strike_price_pairs.sort(key=lambda x: x[0])

            if len(strike_price_pairs) < 2:
                # If we have less than 2 data points, include all
                return set(strike for strike, _ in strike_price_pairs)

            valid_strikes = set()

            # For puts, as strike increases, bid/ask should increase
            # So we check that each strike has a price <= the next higher strike's price
            # However, we'll be more permissive with zero values
            for i in range(len(strike_price_pairs)):
                current_strike, current_price = strike_price_pairs[i]
                is_valid = True

                # Check against the next strike (higher strike)
                if i < len(strike_price_pairs) - 1:
                    next_strike, next_price = strike_price_pairs[i + 1]
                    # Current price should be <= next price (since current strike < next strike)
                    # But allow zero prices to pass validation
                    if current_price > 0 and next_price > 0 and current_price > next_price:
                        is_valid = False
                        logger.debug(f"Put strike {current_strike:.2f} ({price_type}={current_price:.2f}) is out of order with strike {next_strike:.2f} ({price_type}={next_price:.2f})")

                if is_valid:
                    valid_strikes.add(current_strike)

            logger.info(f"Put {price_type} validation: {len(valid_strikes)} out of {len(strike_price_pairs)} strikes are properly ordered")
            return valid_strikes

        except Exception as e:
            logger.error(f"Error validating put bid/ask ordering: {str(e)}", exc_info=True)
            return set()

    def detect_and_draw_breakthrough_lines(self, call_curves_data, put_curves_data,
                                          bid_call_curves_data, bid_put_curves_data,
                                          ask_call_curves_data, ask_put_curves_data):
        """
        Detect breakthrough points where call lines intersect with other call lines
        and put lines intersect with other put lines, then draw horizontal lines
        at the highest call breakthrough and lowest put breakthrough.

        IV Peak lines are derived from bid prices and their bid-based breakthrough points.
        IV Walls, Overflow, and Max Fear are derived from ask prices and their ask-based breakthrough points.
        Each price type (bid/ask) uses its own breakthrough reference to determine which zones to draw.

        Args:
            call_curves_data: List of (strike, peak_y, curve_y_values, x_values) for displayed call curves
            put_curves_data: List of (strike, trough_y, curve_y_values, x_values) for displayed put curves
            bid_call_curves_data: List of (strike, peak_y, curve_y_values, x_values) for bid call curves (IV Peak)
            bid_put_curves_data: List of (strike, trough_y, curve_y_values, x_values) for bid put curves (IV Peak)
            ask_call_curves_data: List of (strike, peak_y, curve_y_values, x_values) for ask call curves (IV Walls/Overflow/Max Fear)
            ask_put_curves_data: List of (strike, trough_y, curve_y_values, x_values) for ask put curves (IV Walls/Overflow/Max Fear)
        """
        try:
            logger.info(f"Starting breakthrough detection with {len(call_curves_data)} call curves and {len(put_curves_data)} put curves")
            logger.info(f"Bid curves: {len(bid_call_curves_data)} calls, {len(bid_put_curves_data)} puts")
            logger.info(f"Ask curves: {len(ask_call_curves_data)} calls, {len(ask_put_curves_data)} puts")
            logger.info(f"IMPORTANT: IV zones are calculated independently of display toggle. Current display mode: {'Ask' if hasattr(self, 'use_ask_prices') and self.use_ask_prices else 'Bid'} prices")

            # Find call line breakthroughs using BID prices for IV Peak determination
            bid_call_breakthrough_y = self.find_highest_call_breakthrough(bid_call_curves_data)

            # Find put line breakthroughs using BID prices for IV Peak determination
            bid_put_breakthrough_y = self.find_lowest_put_breakthrough(bid_put_curves_data)

            # Find call line breakthroughs using ASK prices for IV Walls/Overflow/Max Fear reference
            ask_call_breakthrough_y = self.find_highest_call_breakthrough(ask_call_curves_data)

            # Find put line breakthroughs using ASK prices for IV Walls/Overflow/Max Fear reference
            ask_put_breakthrough_y = self.find_lowest_put_breakthrough(ask_put_curves_data)



            # Draw horizontal lines at breakthrough points only if Eth's Option Zones is enabled
            if self.show_eth_zones:
                # Draw BID-based IV Peak lines
                if bid_call_breakthrough_y is not None:
                    self.draw_breakthrough_line(bid_call_breakthrough_y, 'call')
                    logger.info(f"Drew call IV Peak line (from bid prices) at y={bid_call_breakthrough_y:.2f}")

                if bid_put_breakthrough_y is not None:
                    self.draw_breakthrough_line(bid_put_breakthrough_y, 'put')
                    logger.info(f"Drew put IV Peak line (from bid prices) at y={bid_put_breakthrough_y:.2f}")

                # Draw ASK-based IV Walls, Overflow, and Max Fear lines
                # Use ASK breakthrough as reference point (not bid breakthrough)
                if ask_call_breakthrough_y is not None:
                    logger.info(f"Using ask call breakthrough at y={ask_call_breakthrough_y:.2f} as reference for ask-based zones")
                    # Add horizontal lines at 1st, 2nd, 3rd, and 5th highest call lines above ASK breakthrough
                    self.draw_ranked_call_highs_above_breakthrough(ask_call_curves_data, ask_call_breakthrough_y)

                if ask_put_breakthrough_y is not None:
                    logger.info(f"Using ask put breakthrough at y={ask_put_breakthrough_y:.2f} as reference for ask-based zones")
                    # Add horizontal lines at 1st, 2nd, 3rd, and 5th lowest put lines below ASK breakthrough
                    self.draw_ranked_put_lows_below_breakthrough(ask_put_curves_data, ask_put_breakthrough_y)
            else:
                logger.info("Eth's Option Zones disabled - skipping IV zone lines")

        except Exception as e:
            logger.error(f"Error in breakthrough detection: {str(e)}", exc_info=True)







    def find_highest_call_breakthrough(self, call_curves_data):
        """
        Find the highest call line that actually broke through another call line.

        Args:
            call_curves_data: List of (strike, peak_y, curve_y_values, x_values) for call curves

        Returns:
            float or None: Y-coordinate of the highest call breakthrough, or None if no breakthrough found
        """
        try:
            if len(call_curves_data) < 2:
                logger.info("Not enough call curves for breakthrough detection")
                return None

            breakthroughs = []

            # Compare each call curve with every other call curve to find intersections
            for i, (strike1, peak1, curve1_y, curve1_x) in enumerate(call_curves_data):
                for j, (strike2, peak2, curve2_y, curve2_x) in enumerate(call_curves_data):
                    if i >= j:  # Avoid duplicate comparisons and self-comparison
                        continue

                    # Check if curves intersect by finding where they cross
                    intersection_y = self.find_curve_intersection(curve1_x, curve1_y, curve2_x, curve2_y)

                    if intersection_y is not None:
                        # Determine which curve broke through the other
                        # The curve with the higher peak is considered to have broken through
                        if peak1 > peak2:
                            breakthroughs.append((strike1, peak1, intersection_y))
                            logger.debug(f"Call breakthrough: Strike {strike1:.2f} (peak {peak1:.2f}) broke through strike {strike2:.2f} at y={intersection_y:.2f}")
                        elif peak2 > peak1:
                            breakthroughs.append((strike2, peak2, intersection_y))
                            logger.debug(f"Call breakthrough: Strike {strike2:.2f} (peak {peak2:.2f}) broke through strike {strike1:.2f} at y={intersection_y:.2f}")

            if not breakthroughs:
                logger.info("No call breakthroughs detected")
                return None

            # Find the breakthrough with the highest peak (the highest call line that broke through)
            highest_breakthrough = max(breakthroughs, key=lambda x: x[1])  # Sort by peak_y (index 1)
            breakthrough_strike, breakthrough_peak, intersection_y = highest_breakthrough

            logger.info(f"Highest call breakthrough: Strike {breakthrough_strike:.2f} with peak {breakthrough_peak:.2f}")
            return breakthrough_peak  # Return the peak Y coordinate of the highest breakthrough

        except Exception as e:
            logger.error(f"Error finding call breakthroughs: {str(e)}", exc_info=True)
            return None

    def find_lowest_put_breakthrough(self, put_curves_data):
        """
        Find the lowest put line that actually broke through another put line.

        Args:
            put_curves_data: List of (strike, trough_y, curve_y_values, x_values) for put curves

        Returns:
            float or None: Y-coordinate of the lowest put breakthrough, or None if no breakthrough found
        """
        try:
            if len(put_curves_data) < 2:
                logger.info("Not enough put curves for breakthrough detection")
                return None

            breakthroughs = []

            # Compare each put curve with every other put curve to find intersections
            for i, (strike1, trough1, curve1_y, curve1_x) in enumerate(put_curves_data):
                for j, (strike2, trough2, curve2_y, curve2_x) in enumerate(put_curves_data):
                    if i >= j:  # Avoid duplicate comparisons and self-comparison
                        continue

                    # Check if curves intersect by finding where they cross
                    intersection_y = self.find_curve_intersection(curve1_x, curve1_y, curve2_x, curve2_y)

                    if intersection_y is not None:
                        # Determine which curve broke through the other
                        # The curve with the lower trough is considered to have broken through
                        if trough1 < trough2:
                            breakthroughs.append((strike1, trough1, intersection_y))
                            logger.debug(f"Put breakthrough: Strike {strike1:.2f} (trough {trough1:.2f}) broke through strike {strike2:.2f} at y={intersection_y:.2f}")
                        elif trough2 < trough1:
                            breakthroughs.append((strike2, trough2, intersection_y))
                            logger.debug(f"Put breakthrough: Strike {strike2:.2f} (trough {trough2:.2f}) broke through strike {strike1:.2f} at y={intersection_y:.2f}")

            if not breakthroughs:
                logger.info("No put breakthroughs detected")
                return None

            # Find the breakthrough with the lowest trough (the lowest put line that broke through)
            lowest_breakthrough = min(breakthroughs, key=lambda x: x[1])  # Sort by trough_y (index 1)
            breakthrough_strike, breakthrough_trough, intersection_y = lowest_breakthrough

            logger.info(f"Lowest put breakthrough: Strike {breakthrough_strike:.2f} with trough {breakthrough_trough:.2f}")
            return breakthrough_trough  # Return the trough Y coordinate of the lowest breakthrough

        except Exception as e:
            logger.error(f"Error finding put breakthroughs: {str(e)}", exc_info=True)
            return None

    def find_curve_intersection(self, x1, y1, x2, y2):
        """
        Find intersection points between two curves.

        Args:
            x1, y1: Arrays for first curve
            x2, y2: Arrays for second curve

        Returns:
            float or None: Y-coordinate of intersection point, or None if no intersection
        """
        try:
            if len(x1) == 0 or len(y1) == 0 or len(x2) == 0 or len(y2) == 0:
                return None

            # Convert to numpy arrays for easier processing
            x1, y1 = np.array(x1), np.array(y1)
            x2, y2 = np.array(x2), np.array(y2)

            # Find overlapping x-range
            x_min = max(np.min(x1), np.min(x2))
            x_max = min(np.max(x1), np.max(x2))

            if x_min >= x_max:
                return None  # No overlapping x-range

            # Create interpolation functions for both curves
            from scipy.interpolate import interp1d

            # Only use points within the overlapping range
            mask1 = (x1 >= x_min) & (x1 <= x_max)
            mask2 = (x2 >= x_min) & (x2 <= x_max)

            if np.sum(mask1) < 2 or np.sum(mask2) < 2:
                return None  # Not enough points for interpolation

            f1 = interp1d(x1[mask1], y1[mask1], kind='linear', bounds_error=False, fill_value='extrapolate')
            f2 = interp1d(x2[mask2], y2[mask2], kind='linear', bounds_error=False, fill_value='extrapolate')

            # Sample points in the overlapping range
            x_sample = np.linspace(x_min, x_max, 100)
            y1_sample = f1(x_sample)
            y2_sample = f2(x_sample)

            # Find where curves cross (sign changes in difference)
            diff = y1_sample - y2_sample
            sign_changes = np.where(np.diff(np.sign(diff)))[0]

            if len(sign_changes) > 0:
                # Return the y-coordinate of the first intersection
                intersection_x = x_sample[sign_changes[0]]
                intersection_y = f1(intersection_x)
                return float(intersection_y)

            return None

        except Exception as e:
            logger.debug(f"Error finding curve intersection: {str(e)}")
            return None

    def draw_breakthrough_line(self, y_position, line_type):
        """
        Draw a horizontal line at the breakthrough position (IV Peak from bid prices).

        Args:
            y_position: Y-coordinate where to draw the line
            line_type: 'call' or 'put' to determine line color and style
        """
        try:
            # Choose color and style based on line type
            if line_type == 'call':
                color = '#808080'  # Grey for IV Peak (call breakthroughs from bid prices)
                line_style = QtCore.Qt.PenStyle.SolidLine
                label_text = f"IV Peak (Bid): {y_position:.2f}"
            else:  # put
                color = '#808080'  # Grey for IV Peak (put breakthroughs from bid prices)
                line_style = QtCore.Qt.PenStyle.SolidLine
                label_text = f"IV Peak (Bid): {y_position:.2f}"

            # Create horizontal line
            breakthrough_line = pg.InfiniteLine(
                pos=y_position,
                angle=0,  # Horizontal line
                pen=pg.mkPen(color=color, width=2, style=line_style),
                label=label_text,
                labelOpts={
                    'position': 0.95,  # Position near the right end
                    'color': color,
                    'movable': False,
                    'fill': (0, 0, 0, 100)  # Semi-transparent black background
                }
            )

            # Add the line to the plot with lower z-order so it renders behind other elements
            breakthrough_line.setZValue(-10)  # Lower z-value means it renders behind
            self.plot_widget.addItem(breakthrough_line)

            logger.info(f"Added {line_type} breakthrough line at y={y_position:.2f}")

        except Exception as e:
            logger.error(f"Error drawing breakthrough line: {str(e)}", exc_info=True)

    def draw_ranked_call_highs_above_breakthrough(self, call_curves_data, breakthrough_y):
        """
        Draw horizontal lines at the 1st, 2nd, 3rd, and 5th highest call line peaks above the breakthrough.
        These represent IV Walls, IV Overflow, and Max Fear levels derived from ASK prices.
        Only draws zones that don't touch the ASK-based breakthrough point.

        Args:
            call_curves_data: List of (strike, peak_y, curve_y_values, x_values) for ask call curves
            breakthrough_y: Y-coordinate of the ASK call breakthrough line (reference point for ask-based zones)
        """
        try:
            if not call_curves_data:
                logger.info("No call curves data available for ranked highs")
                return

            # Filter call curves that are above the breakthrough line and get their peaks
            peaks_above_breakthrough = []
            for strike, peak_y, _, _ in call_curves_data:
                if peak_y > breakthrough_y:
                    peaks_above_breakthrough.append((strike, peak_y))

            if not peaks_above_breakthrough:
                logger.info("No call peaks found above breakthrough line")
                return

            # Sort by peak_y in ascending order (closest to breakthrough first)
            peaks_above_breakthrough.sort(key=lambda x: x[1])

            # Define which ranks to draw (1st, 2nd, 3rd, 5th highest)
            ranks_to_draw = [1, 2, 3, 5]

            logger.info(f"Found {len(peaks_above_breakthrough)} call peaks above breakthrough at y={breakthrough_y:.2f}")
            for i, (strike, peak) in enumerate(peaks_above_breakthrough[:10]):  # Log first 10 for debugging
                logger.info(f"  Next {i+1}: Strike {strike:.2f}, Peak {peak:.2f}")

            for rank in ranks_to_draw:
                if rank <= len(peaks_above_breakthrough):
                    strike, peak_y = peaks_above_breakthrough[rank - 1]  # rank-1 because list is 0-indexed

                    # Map rank to IV label and color (from ask prices)
                    rank_labels = {
                        1: "IV Inner Wall (Ask)",
                        2: "IV Wall (Ask)",
                        3: "IV Overflow (Ask)",
                        5: "Max Fear (Ask)"
                    }

                    rank_colors = {
                        1: '#2962ff',  # Bright blue for IV Inner Wall
                        2: '#2962ff',  # Bright blue for IV Wall
                        3: '#2962ff',  # Bright blue for IV Overflow
                        5: '#FF0000'   # Red for Max Fear
                    }



                    # Draw horizontal line with IV label and appropriate color
                    line_color = rank_colors[rank]
                    label_text = f"{rank_labels[rank]}: {peak_y:.2f}"

                    ranked_line = pg.InfiniteLine(
                        pos=peak_y,
                        angle=0,  # Horizontal line
                        pen=pg.mkPen(color=line_color, width=1, style=QtCore.Qt.PenStyle.DashLine),
                        label=label_text,
                        labelOpts={
                            'position': 0.85,  # Position near the right end
                            'color': line_color,
                            'movable': False,
                            'fill': (0, 0, 0, 80)  # Semi-transparent black background
                        }
                    )

                    # Add the line to the plot with lower z-order
                    ranked_line.setZValue(-15)  # Even lower than breakthrough lines
                    self.plot_widget.addItem(ranked_line)

                    logger.info(f"Added call #{rank} highest line at y={peak_y:.2f} (strike {strike:.2f})")
                else:
                    logger.info(f"Not enough call peaks above breakthrough for rank #{rank}")

        except Exception as e:
            logger.error(f"Error drawing ranked call highs: {str(e)}", exc_info=True)

    def draw_ranked_put_lows_below_breakthrough(self, put_curves_data, breakthrough_y):
        """
        Draw horizontal lines at the 1st, 2nd, 3rd, and 5th lowest put line troughs below the breakthrough.
        These represent IV Walls, IV Overflow, and Max Fear levels derived from ASK prices.
        Only draws zones that don't touch the ASK-based breakthrough point.

        Args:
            put_curves_data: List of (strike, trough_y, curve_y_values, x_values) for ask put curves
            breakthrough_y: Y-coordinate of the ASK put breakthrough line (reference point for ask-based zones)
        """
        try:
            if not put_curves_data:
                logger.info("No put curves data available for ranked lows")
                return

            # Filter put curves that are below the breakthrough line and get their troughs
            troughs_below_breakthrough = []
            for strike, trough_y, _, _ in put_curves_data:
                if trough_y < breakthrough_y:
                    troughs_below_breakthrough.append((strike, trough_y))

            if not troughs_below_breakthrough:
                logger.info("No put troughs found below breakthrough line")
                return

            # Sort by trough_y in descending order (closest to breakthrough first)
            troughs_below_breakthrough.sort(key=lambda x: x[1], reverse=True)

            # Define which ranks to draw (1st, 2nd, 3rd, 5th lowest)
            ranks_to_draw = [1, 2, 3, 5]

            logger.info(f"Found {len(troughs_below_breakthrough)} put troughs below breakthrough at y={breakthrough_y:.2f}")
            for i, (strike, trough) in enumerate(troughs_below_breakthrough[:10]):  # Log first 10 for debugging
                logger.info(f"  Next {i+1}: Strike {strike:.2f}, Trough {trough:.2f}")

            for rank in ranks_to_draw:
                if rank <= len(troughs_below_breakthrough):
                    strike, trough_y = troughs_below_breakthrough[rank - 1]  # rank-1 because list is 0-indexed

                    # Map rank to IV label and color (from ask prices)
                    rank_labels = {
                        1: "IV Inner Wall (Ask)",
                        2: "IV Wall (Ask)",
                        3: "IV Overflow (Ask)",
                        5: "Max Fear (Ask)"
                    }

                    rank_colors = {
                        1: '#2962ff',  # Bright blue for IV Inner Wall
                        2: '#2962ff',  # Bright blue for IV Wall
                        3: '#2962ff',  # Bright blue for IV Overflow
                        5: '#FF0000'   # Red for Max Fear
                    }



                    # Draw horizontal line with IV label and appropriate color
                    line_color = rank_colors[rank]
                    label_text = f"{rank_labels[rank]}: {trough_y:.2f}"

                    ranked_line = pg.InfiniteLine(
                        pos=trough_y,
                        angle=0,  # Horizontal line
                        pen=pg.mkPen(color=line_color, width=1, style=QtCore.Qt.PenStyle.DashLine),
                        label=label_text,
                        labelOpts={
                            'position': 0.85,  # Position near the right end
                            'color': line_color,
                            'movable': False,
                            'fill': (0, 0, 0, 80)  # Semi-transparent black background
                        }
                    )

                    # Add the line to the plot with lower z-order
                    ranked_line.setZValue(-15)  # Even lower than breakthrough lines
                    self.plot_widget.addItem(ranked_line)

                    logger.info(f"Added put #{rank} lowest line at y={trough_y:.2f} (strike {strike:.2f})")
                else:
                    logger.info(f"Not enough put troughs below breakthrough for rank #{rank}")

        except Exception as e:
            logger.error(f"Error drawing ranked put lows: {str(e)}", exc_info=True)