import numpy as np
from PyQt6 import QtWidgets, QtCore
import pyqtgraph as pg
import warnings
import traceback
import logging
from scipy import stats

# Set up logger
logger = logging.getLogger(__name__)

# Suppress the RuntimeWarning about all-NaN slices
warnings.filterwarnings("ignore", message="All-NaN slice encountered")
# Suppress the RuntimeWarning about hover exit events
warnings.filterwarnings("ignore", message="Error sending hover exit event:")

class OptionsAnalyzerTab(QtWidgets.QWidget):
    """
    Displays three charts:
      1. Peaks Chart – aggregated high (H) events that are actual peaks.
      2. Troughs Chart – aggregated low (L) events that are actual troughs.
      3. Combined Chart – displays all extreme events in chronological order.
         For each event in the combined chart:
           - The bar's height is set to the event's percentage value (as drawn in the Market Odds tab).
           - If high ("H"), the bar is drawn in green and a label (showing the candle count) is placed just above.
           - If low ("L"), the bar is drawn in red and a label is placed just below.
         Additionally, three horizontal white lines are drawn:
           - A pivot line at y = 0 with a label showing "Pivot: <pivot price>".
           - An average-high line at the average of high events with a label "Avg: <avg high>%".
           - An average-low line at the average of low events with a label "Avg: <avg low>%".
         The extra labels (Pivot and averages) are positioned at x = –2 so they are visible.

    The charts update every 2 seconds.
    """
    def __init__(self, parent=None):
        super().__init__(parent)

        # Apply centralized styling with black borders and grey/white color scheme
        self.setStyleSheet("""
            /* ---------- State-of-the-Art Checkbox Style ---------- */
            QCheckBox {
                spacing: 8px;
                font-size: 10pt;
            }

            QCheckBox::indicator {
                width: 20px;
                height: 20px;
                border: 2px solid #000000;
                border-radius: 4px;
                background-color: #808080;
            }

            QCheckBox::indicator:unchecked:hover {
                border: 2px solid #000000;
                background-color: #909090;
            }

            QCheckBox::indicator:checked {
                background-color: #FFFFFF;
                border: 2px solid #000000;
            }

            QCheckBox::indicator:checked:hover {
                background-color: #F0F0F0;
                border: 2px solid #000000;
            }

            /* ---------- State-of-the-Art Radio Button Style ---------- */
            QRadioButton {
                spacing: 8px;
                font-size: 10pt;
            }

            QRadioButton::indicator {
                width: 20px;
                height: 20px;
                border: 2px solid #000000;
                border-radius: 10px;
                background-color: #808080;
            }

            QRadioButton::indicator:unchecked:hover {
                border: 2px solid #000000;
                background-color: #909090;
            }

            QRadioButton::indicator:checked {
                background-color: #FFFFFF;
                border: 2px solid #000000;
                background-image: radial-gradient(circle, #000000 30%, transparent 30%);
            }

            QRadioButton::indicator:checked:hover {
                background-color: #F0F0F0;
                border: 2px solid #000000;
                background-image: radial-gradient(circle, #000000 30%, transparent 30%);
            }
        """)

        main_layout = QtWidgets.QVBoxLayout(self)

        # Chart colors
        self.chart_colors = {
            'background': '#1e1e1e',  # Dark grey background
            'text': '#e0e0e0',
            'grid': '#2d2d2d',
            'bullish': '#4CAF50',  # Material Design Green
            'bearish': '#F44336',  # Material Design Red
            'axis': '#666666',
            'atr_1d': '#2196F3',   # Blue for daily ATR
            'line': '#2196F3'      # Blue for lines
        }

        # --- Info Panel: Display Ticker, Wave, and DTL/Count ---
        self.info_panel = QtWidgets.QFrame()
        self.info_panel.setFrameStyle(QtWidgets.QFrame.Shape.StyledPanel | QtWidgets.QFrame.Shadow.Raised)
        self.info_panel.setStyleSheet("""
            QFrame {
                background-color: #1e1e1e;
                border: 1px solid #3e3e3e;
                border-radius: 4px;
                padding: 5px;
            }
            QLabel {
                color: #e0e0e0;
                font-size: 12px;
                font-weight: bold;
            }
        """)
        info_layout = QtWidgets.QHBoxLayout(self.info_panel)
        info_layout.setContentsMargins(5, 5, 5, 5)

        self.ticker_label = QtWidgets.QLabel("Ticker: --")
        info_layout.addWidget(self.ticker_label)

        self.wave_label = QtWidgets.QLabel("The Line Length: --")
        info_layout.addWidget(self.wave_label)

        self.dtl_label = QtWidgets.QLabel("DTL: --")
        info_layout.addWidget(self.dtl_label)

        info_layout.addStretch()
        main_layout.addWidget(self.info_panel)

        # --- Top Row: Aggregated Charts (Peaks and Troughs) ---
        # Create a widget to contain the top charts so we can control its visibility better
        self.top_charts_widget = QtWidgets.QWidget()
        top_layout = QtWidgets.QHBoxLayout(self.top_charts_widget)
        top_layout.setContentsMargins(0, 0, 0, 0)

        # Peaks Chart (aggregated highs)
        self.peaks_chart = pg.PlotWidget()
        self._setup_chart(self.peaks_chart, "Peaks Distribution")
        top_layout.addWidget(self.peaks_chart)

        # Troughs Chart (aggregated lows)
        self.troughs_chart = pg.PlotWidget()
        self._setup_chart(self.troughs_chart, "Troughs Distribution")
        top_layout.addWidget(self.troughs_chart)

        # Create a splitter to manage the layout proportions better
        self.main_splitter = QtWidgets.QSplitter(QtCore.Qt.Orientation.Vertical)
        self.main_splitter.addWidget(self.top_charts_widget)

        # Create a widget to contain the bottom section (controls + charts)
        self.bottom_section_widget = QtWidgets.QWidget()
        bottom_section_layout = QtWidgets.QVBoxLayout(self.bottom_section_widget)
        bottom_section_layout.setContentsMargins(0, 0, 0, 0)

        # --- Bottom Row: Combined Chart (Chronological Extremes) ---
        # Add dropdown for chart selection
        bottom_control_layout = QtWidgets.QHBoxLayout()

        # Chart selector dropdown
        self.chart_selector = QtWidgets.QComboBox()
        self.chart_selector.addItem("Combined Chart")
        self.chart_selector.addItem("Average Range")
        self.chart_selector.addItem("Range Data")

        # Sorting mode dropdown for Combined Chart
        self.combined_sort_selector = QtWidgets.QComboBox()
        self.combined_sort_selector.addItem("Chronological")
        self.combined_sort_selector.addItem("Sorted by Height")
        self.combined_sort_selector.setVisible(True)  # Initially visible for Combined Chart
        self.combined_sort_selector.currentIndexChanged.connect(self.update_charts)

        # Rebase reference dropdown for Average Range chart
        self.rebase_reference_selector = QtWidgets.QComboBox()
        self.rebase_reference_selector.addItem("Previous Candle Close")
        self.rebase_reference_selector.addItem("Current Price")
        self.rebase_reference_selector.setVisible(False)  # Initially hidden, only shown when Average Range chart is selected
        self.rebase_reference_selector.currentIndexChanged.connect(self.update_ybm_chart)

        # Split Sort checkbox for Average Range chart
        self.split_sort_checkbox = QtWidgets.QCheckBox("Split Sort")
        self.split_sort_checkbox.setStyleSheet("color: #e0e0e0;")
        self.split_sort_checkbox.setVisible(False)  # Initially hidden, only shown when Average Range chart is selected
        self.split_sort_checkbox.stateChanged.connect(self.update_ybm_chart)

        # Statistical Overlays checkbox for Average Range chart
        self.show_stats_checkbox = QtWidgets.QCheckBox("Show Statistical Overlays")
        self.show_stats_checkbox.setStyleSheet("color: #e0e0e0;")
        self.show_stats_checkbox.setVisible(False)  # Initially hidden, only shown when Average Range chart is selected
        self.show_stats_checkbox.setChecked(True)  # Default to showing overlays
        self.show_stats_checkbox.stateChanged.connect(self._on_show_stats_changed)

        # Density Heatmap checkbox for Average Range chart
        self.show_density_heatmap_checkbox = QtWidgets.QCheckBox("Show Density Heatmap")
        self.show_density_heatmap_checkbox.setStyleSheet("color: #e0e0e0;")
        self.show_density_heatmap_checkbox.setVisible(False)  # Initially hidden, only shown when Average Range chart is selected
        self.show_density_heatmap_checkbox.setChecked(False)  # Default to disabled
        self.show_density_heatmap_checkbox.stateChanged.connect(self.update_ybm_chart)

        # Density Profile checkbox for Average Range chart
        self.show_density_profile_checkbox = QtWidgets.QCheckBox("Show Density Profile")
        self.show_density_profile_checkbox.setStyleSheet("color: #e0e0e0;")
        self.show_density_profile_checkbox.setVisible(False)  # Initially hidden, only shown when Average Range chart is selected
        self.show_density_profile_checkbox.setChecked(False)  # Default to disabled
        self.show_density_profile_checkbox.stateChanged.connect(self.update_ybm_chart)

        # Show OHLC Candles checkbox for Average Range chart
        self.show_ohlc_candles_checkbox = QtWidgets.QCheckBox("Show OHLC Candles")
        self.show_ohlc_candles_checkbox.setStyleSheet("color: #e0e0e0;")
        self.show_ohlc_candles_checkbox.setVisible(False)  # Initially hidden, only shown when Average Range chart is selected
        self.show_ohlc_candles_checkbox.setChecked(True)  # Default to enabled
        self.show_ohlc_candles_checkbox.stateChanged.connect(self.update_ybm_chart)

        # Use Full Candle Length checkbox for density calculation
        self.use_full_candle_length_checkbox = QtWidgets.QCheckBox("Use Full Candle Length")
        self.use_full_candle_length_checkbox.setStyleSheet("color: #e0e0e0;")
        self.use_full_candle_length_checkbox.setVisible(False)  # Initially hidden, only shown when Average Range chart is selected
        self.use_full_candle_length_checkbox.setChecked(False)  # Default to disabled (use current sampling method)
        self.use_full_candle_length_checkbox.stateChanged.connect(self.update_ybm_chart)

        # Individual statistic toggle checkboxes
        self.stat_toggles = {}
        stat_names = ['mean', 'std', 'var95', 'var99', 'cluster']
        stat_labels = ['Mean (μ)', 'Std Dev (σ)', 'VaR 95%', 'VaR 99%', 'Cluster']

        for stat_name, stat_label in zip(stat_names, stat_labels):
            checkbox = QtWidgets.QCheckBox(stat_label)
            checkbox.setStyleSheet("color: #e0e0e0; font-size: 10pt;")
            checkbox.setVisible(False)  # Initially hidden
            checkbox.setChecked(True)  # Default to showing all stats
            checkbox.stateChanged.connect(self.update_ybm_chart)
            self.stat_toggles[stat_name] = checkbox

        # Apply the same style to all dropdowns
        dropdown_style = """
            QComboBox {
                background-color: #1e1e1e;
                color: #e0e0e0;
                border: 1px solid #3e3e3e;
                border-radius: 4px;
                padding: 4px;
                min-height: 25px;
            }
            QComboBox::drop-down {
                subcontrol-origin: padding;
                subcontrol-position: top right;
                width: 20px;
                border-left: 1px solid #3e3e3e;
            }
            QComboBox QAbstractItemView {
                background-color: #1e1e1e;
                color: #e0e0e0;
                selection-background-color: #2d2d2d;
                selection-color: #e0e0e0;
                border: 1px solid #3e3e3e;
            }
        """
        self.chart_selector.setStyleSheet(dropdown_style)
        self.combined_sort_selector.setStyleSheet(dropdown_style)
        self.rebase_reference_selector.setStyleSheet(dropdown_style)

        # Add widgets to layout
        bottom_control_layout.addWidget(self.chart_selector)
        bottom_control_layout.addWidget(self.combined_sort_selector)
        bottom_control_layout.addWidget(self.rebase_reference_selector)
        bottom_control_layout.addWidget(self.split_sort_checkbox)
        bottom_control_layout.addWidget(self.show_stats_checkbox)
        bottom_control_layout.addWidget(self.show_density_heatmap_checkbox)
        bottom_control_layout.addWidget(self.show_density_profile_checkbox)
        bottom_control_layout.addWidget(self.show_ohlc_candles_checkbox)
        bottom_control_layout.addWidget(self.use_full_candle_length_checkbox)

        # Add individual stat toggle checkboxes
        for stat_name in ['mean', 'std', 'var95', 'var99', 'cluster']:
            bottom_control_layout.addWidget(self.stat_toggles[stat_name])

        # Add label for the rebase reference dropdown
        self.rebase_label = QtWidgets.QLabel("Rebase Reference:")
        self.rebase_label.setStyleSheet("color: #e0e0e0;")
        self.rebase_label.setVisible(False)  # Initially hidden
        bottom_control_layout.insertWidget(1, self.rebase_label)  # Insert between chart selector and rebase selector

        bottom_control_layout.addStretch()

        # Add maximize buttons for charts
        self.combined_maximize_button = QtWidgets.QPushButton("⛶")
        self.combined_maximize_button.setFixedSize(30, 30)
        self.combined_maximize_button.setStyleSheet("""
            QPushButton {
                background-color: #3e3e3e;
                color: #e0e0e0;
                border: 1px solid #5e5e5e;
                border-radius: 4px;
                font-size: 10pt;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #4e4e4e;
                border: 1px solid #6e6e6e;
            }
            QPushButton:pressed {
                background-color: #2e2e2e;
            }
        """)
        self.combined_maximize_button.setToolTip("Expand Chart Area")
        self.combined_maximize_button.clicked.connect(self._toggle_expand_bottom_charts)
        bottom_control_layout.addWidget(self.combined_maximize_button)

        self.ybm_maximize_button = QtWidgets.QPushButton("⛶")
        self.ybm_maximize_button.setFixedSize(30, 30)
        self.ybm_maximize_button.setStyleSheet("""
            QPushButton {
                background-color: #3e3e3e;
                color: #e0e0e0;
                border: 1px solid #5e5e5e;
                border-radius: 4px;
                font-size: 10pt;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #4e4e4e;
                border: 1px solid #6e6e6e;
            }
            QPushButton:pressed {
                background-color: #2e2e2e;
            }
        """)
        self.ybm_maximize_button.setToolTip("Expand Chart Area")
        self.ybm_maximize_button.clicked.connect(self._toggle_expand_bottom_charts)
        self.ybm_maximize_button.setVisible(False)  # Initially hidden, shown when Average Range chart is selected
        bottom_control_layout.addWidget(self.ybm_maximize_button)

        bottom_section_layout.addLayout(bottom_control_layout)

        # Create stacked widget to hold different charts
        self.chart_stack = QtWidgets.QStackedWidget()

        # Combined chart
        self.combined_chart = pg.PlotWidget()
        self._setup_chart(self.combined_chart, "Price Level Analysis")
        self.chart_stack.addWidget(self.combined_chart)

        # Average Range chart
        self.ybm_chart = pg.PlotWidget()
        self._setup_chart(self.ybm_chart, "Average Range Chart")
        # Update the axis labels to show percentage change instead of occurrence
        self.ybm_chart.setLabel('left', '<div style="color: #e0e0e0;">Percentage Change (%)</div>')
        self.ybm_chart.setLabel('bottom', '<div style="color: #e0e0e0;">Dates</div>')
        self.chart_stack.addWidget(self.ybm_chart)

        # Range Data metrics panel
        self.range_data_widget = QtWidgets.QWidget()
        self._setup_range_data_widget()
        self.chart_stack.addWidget(self.range_data_widget)

        # Add stacked widget to bottom section layout
        bottom_section_layout.addWidget(self.chart_stack)

        # Add bottom section to splitter
        self.main_splitter.addWidget(self.bottom_section_widget)

        # Set initial splitter proportions (1:2 ratio - top charts smaller, bottom charts larger)
        self.main_splitter.setSizes([200, 400])
        self.main_splitter.setStretchFactor(0, 1)  # Top charts
        self.main_splitter.setStretchFactor(1, 2)  # Bottom charts

        # Style the splitter handle (invisible but still resizable)
        self.main_splitter.setStyleSheet("""
            QSplitter::handle {
                background-color: transparent;
                height: 3px;
                border: none;
            }
            QSplitter::handle:hover {
                background-color: transparent;
            }
        """)

        # Add splitter to main layout
        main_layout.addWidget(self.main_splitter)

        # Connect chart selector signal
        self.chart_selector.currentIndexChanged.connect(self.change_chart)



        # --- Crosshair and coordinate display ---
        # Create white dashed pen for crosshair - consistent with other charts
        white_dashed_pen = pg.mkPen('#FFFFFF', width=1, style=QtCore.Qt.PenStyle.DashLine)

        # Peaks chart crosshair
        self.peaks_vLine = pg.InfiniteLine(angle=90, movable=False, pen=white_dashed_pen)
        self.peaks_hLine = pg.InfiniteLine(angle=0, movable=False, pen=white_dashed_pen)
        self.peaks_chart.addItem(self.peaks_vLine, ignoreBounds=True)
        self.peaks_chart.addItem(self.peaks_hLine, ignoreBounds=True)

        # Troughs chart crosshair
        self.troughs_vLine = pg.InfiniteLine(angle=90, movable=False, pen=white_dashed_pen)
        self.troughs_hLine = pg.InfiniteLine(angle=0, movable=False, pen=white_dashed_pen)
        self.troughs_chart.addItem(self.troughs_vLine, ignoreBounds=True)
        self.troughs_chart.addItem(self.troughs_hLine, ignoreBounds=True)

        # Combined chart crosshair
        self.combined_vLine = pg.InfiniteLine(angle=90, movable=False, pen=white_dashed_pen)
        self.combined_hLine = pg.InfiniteLine(angle=0, movable=False, pen=white_dashed_pen)
        self.combined_chart.addItem(self.combined_vLine, ignoreBounds=True)
        self.combined_chart.addItem(self.combined_hLine, ignoreBounds=True)

        # YBM chart crosshair
        self.ybm_vLine = pg.InfiniteLine(angle=90, movable=False, pen=white_dashed_pen)
        self.ybm_hLine = pg.InfiniteLine(angle=0, movable=False, pen=white_dashed_pen)
        self.ybm_chart.addItem(self.ybm_vLine, ignoreBounds=True)
        self.ybm_chart.addItem(self.ybm_hLine, ignoreBounds=True)

        # Intersection point markers - initialize with empty data to avoid NaN warnings
        self.peaks_spot = pg.ScatterPlotItem(size=6, pen=pg.mkPen('w'), brush=pg.mkBrush(25, 150, 255, 200))
        self.peaks_chart.addItem(self.peaks_spot)

        self.troughs_spot = pg.ScatterPlotItem(size=6, pen=pg.mkPen('w'), brush=pg.mkBrush(25, 150, 255, 200))
        self.troughs_chart.addItem(self.troughs_spot)

        self.combined_spot = pg.ScatterPlotItem(size=6, pen=pg.mkPen('w'), brush=pg.mkBrush(25, 150, 255, 200))
        self.combined_chart.addItem(self.combined_spot)

        self.ybm_spot = pg.ScatterPlotItem(size=6, pen=pg.mkPen('w'), brush=pg.mkBrush(25, 150, 255, 200))
        self.ybm_chart.addItem(self.ybm_spot)

        # Initialize spots with empty data to avoid NaN warnings
        self.peaks_spot.setData([], [])
        self.troughs_spot.setData([], [])
        self.combined_spot.setData([], [])
        self.ybm_spot.setData([], [])

        # Coordinates display panel
        self.coords_panel = QtWidgets.QFrame()
        self.coords_panel.setFrameStyle(QtWidgets.QFrame.Shape.StyledPanel | QtWidgets.QFrame.Shadow.Raised)
        self.coords_panel.setStyleSheet("""
            QFrame {
                background-color: #1e1e1e;
                border: 1px solid #3e3e3e;
                border-radius: 4px;
                padding: 5px;
            }
            QLabel {
                color: #e0e0e0;
                font-size: 11px;
            }
        """)
        coords_layout = QtWidgets.QHBoxLayout(self.coords_panel)
        coords_layout.setContentsMargins(5, 5, 5, 5)

        # Single coordinate label for all charts
        self.coords_label = QtWidgets.QLabel("Coordinates | X: -- | Y: -- | Price: --")
        self.coords_label.setStyleSheet("""
            QLabel {
                color: #e0e0e0;
                font-size: 12px;
                font-weight: bold;
            }
        """)
        coords_layout.addWidget(self.coords_label)
        main_layout.addWidget(self.coords_panel)

        # Create floating tooltip for each chart (hidden by default)
        self.peaks_tooltip = pg.TextItem(text="", color='white', anchor=(0, 1))
        self.peaks_tooltip.setZValue(1000)
        self.peaks_tooltip.setVisible(False)  # Hide tooltip
        self.peaks_chart.addItem(self.peaks_tooltip)

        self.troughs_tooltip = pg.TextItem(text="", color='white', anchor=(0, 1))
        self.troughs_tooltip.setZValue(1000)
        self.troughs_tooltip.setVisible(False)  # Hide tooltip
        self.troughs_chart.addItem(self.troughs_tooltip)

        self.combined_tooltip = pg.TextItem(text="", color='white', anchor=(0, 1))
        self.combined_tooltip.setZValue(1000)
        self.combined_tooltip.setVisible(False)  # Hide tooltip
        self.combined_chart.addItem(self.combined_tooltip)

        self.ybm_tooltip = pg.TextItem(text="", color='white', anchor=(0, 1))
        self.ybm_tooltip.setZValue(1000)
        self.ybm_tooltip.setVisible(False)  # Hide tooltip
        self.ybm_chart.addItem(self.ybm_tooltip)

        # Connect mouse movement for crosshair
        self.peaks_proxy = pg.SignalProxy(self.peaks_chart.scene().sigMouseMoved,
                                     rateLimit=60, slot=lambda evt: self.mouseMoved(evt, 'peaks'))
        self.troughs_proxy = pg.SignalProxy(self.troughs_chart.scene().sigMouseMoved,
                                      rateLimit=60, slot=lambda evt: self.mouseMoved(evt, 'troughs'))
        self.combined_proxy = pg.SignalProxy(self.combined_chart.scene().sigMouseMoved,
                                       rateLimit=60, slot=lambda evt: self.mouseMoved(evt, 'combined'))
        self.ybm_proxy = pg.SignalProxy(self.ybm_chart.scene().sigMouseMoved,
                                  rateLimit=60, slot=lambda evt: self.mouseMoved(evt, 'ybm'))

        # Timer for manual updates (disabled automatic updates)
        self.timer = QtCore.QTimer(self)
        self.timer.timeout.connect(self.update_charts)

        # Initialize expanded state and store original layout state
        self.is_bottom_expanded = False
        self._original_layout_state = None

        # Initialize legend attributes
        self._statistics_legend = None
        self._legend_view_connection = None
        self._legend_transform_connection = None

        # Initialize backtesting attributes
        self.viewing_historical = False
        self.original_data = None
        self.historical_timestamp = None

        # Add backtesting controls
        self._setup_backtesting_controls()

        # Connect to the universal_controls data_fetched signal if available
        try:
            # Find the main window instance
            for widget in QtWidgets.QApplication.topLevelWidgets():
                # Connect to universal_controls data_fetched signal
                if hasattr(widget, 'universal_controls'):
                    widget.universal_controls.data_fetched.connect(self.on_data_fetched_universal)
                    logger.debug("OptionsAnalyzerTab: Connected to universal_controls data_fetched signal")
                    break
        except Exception as e:
            logger.debug(f"OptionsAnalyzerTab: Could not connect to universal_controls data_fetched signal: {str(e)}")

        # Store original layout state after a short delay to ensure everything is initialized
        QtCore.QTimer.singleShot(100, self._store_original_layout_state)

        # Initial update after a short delay to ensure market odds tab is loaded
        QtCore.QTimer.singleShot(500, self.update_charts)

    def _setup_backtesting_controls(self):
        """Set up the backtesting controls for historical data viewing"""
        import datetime

        # Create backtesting panel
        backtesting_panel = QtWidgets.QFrame()
        backtesting_panel.setFrameStyle(QtWidgets.QFrame.Shape.StyledPanel | QtWidgets.QFrame.Shadow.Raised)
        backtesting_panel.setStyleSheet("""
            QFrame {
                background-color: #1e1e1e;
                border: 1px solid #3e3e3e;
                border-radius: 4px;
                padding: 5px;
            }
            QLabel {
                color: #e0e0e0;
                font-size: 12px;
                font-weight: bold;
            }
        """)

        backtesting_layout = QtWidgets.QHBoxLayout(backtesting_panel)
        backtesting_layout.setContentsMargins(5, 5, 5, 5)

        # Historical data label
        historical_label = QtWidgets.QLabel("Backtesting:")
        backtesting_layout.addWidget(historical_label)

        # Historical data dropdown
        self.historical_dropdown = QtWidgets.QComboBox()
        self.historical_dropdown.addItem("Current Data")
        self.historical_dropdown.setStyleSheet("""
            QComboBox {
                background-color: #1e1e1e;
                color: #e0e0e0;
                border: 1px solid #3e3e3e;
                border-radius: 4px;
                padding: 4px;
                min-height: 25px;
                min-width: 150px;
            }
            QComboBox::drop-down {
                subcontrol-origin: padding;
                subcontrol-position: top right;
                width: 20px;
                border-left: 1px solid #3e3e3e;
            }
            QComboBox QAbstractItemView {
                background-color: #1e1e1e;
                color: #e0e0e0;
                selection-background-color: #2d2d2d;
                selection-color: #e0e0e0;
                border: 1px solid #3e3e3e;
            }
        """)
        self.historical_dropdown.currentIndexChanged.connect(self.on_historical_dropdown_changed)
        backtesting_layout.addWidget(self.historical_dropdown)

        # Select historical data button
        self.historical_button = QtWidgets.QPushButton("Select Historical Date")
        self.historical_button.setStyleSheet("""
            QPushButton {
                background-color: #3e3e3e;
                color: #e0e0e0;
                border: 1px solid #5e5e5e;
                border-radius: 4px;
                padding: 6px 12px;
                font-size: 11px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #4e4e4e;
                border: 1px solid #6e6e6e;
            }
            QPushButton:pressed {
                background-color: #2e2e2e;
            }
        """)
        self.historical_button.clicked.connect(self.show_historical_dialog)
        backtesting_layout.addWidget(self.historical_button)

        # Back to current button
        self.back_to_current_button = QtWidgets.QPushButton("Back to Current")
        self.back_to_current_button.setStyleSheet("""
            QPushButton {
                background-color: #4CAF50;
                color: white;
                border: 1px solid #45a049;
                border-radius: 4px;
                padding: 6px 12px;
                font-size: 11px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #45a049;
                border: 1px solid #3d8b40;
            }
            QPushButton:pressed {
                background-color: #3d8b40;
            }
            QPushButton:disabled {
                background-color: #2e2e2e;
                color: #666666;
                border: 1px solid #3e3e3e;
            }
        """)
        self.back_to_current_button.clicked.connect(self.reset_to_current_data)
        self.back_to_current_button.setEnabled(False)  # Initially disabled
        backtesting_layout.addWidget(self.back_to_current_button)

        backtesting_layout.addStretch()

        # Add backtesting panel to the main layout (insert after info panel)
        main_layout = self.layout()
        main_layout.insertWidget(1, backtesting_panel)

    def mouseMoved(self, evt, chart_name):
        try:
            pos = evt[0]

            chart = getattr(self, f"{chart_name}_chart")
            vLine = getattr(self, f"{chart_name}_vLine")
            hLine = getattr(self, f"{chart_name}_hLine")
            tooltip = getattr(self, f"{chart_name}_tooltip")
            spot = getattr(self, f"{chart_name}_spot")

            # Hide crosshairs, spots and tooltips on all charts
            for prefix in ['peaks', 'troughs', 'combined', 'ybm']:
                if prefix != chart_name:  # Keep current chart crosshair visible
                    getattr(self, f"{prefix}_vLine").setVisible(False)
                    getattr(self, f"{prefix}_hLine").setVisible(False)
                    getattr(self, f"{prefix}_tooltip").setVisible(False)  # Always hide tooltips
                    getattr(self, f"{prefix}_spot").clear()

            if chart.sceneBoundingRect().contains(pos):
                mousePoint = chart.plotItem.vb.mapSceneToView(pos)

                # Get x and y coordinates and check for NaN values
                x, y = mousePoint.x(), mousePoint.y()
                if np.isnan(x) or np.isnan(y) or not np.isfinite(x) or not np.isfinite(y):
                    # If invalid values, don't update crosshair
                    return

                # Make current chart's crosshair visible
                vLine.setVisible(True)
                hLine.setVisible(True)
                vLine.setPos(x)
                hLine.setPos(y)

                # Update spot position
                spot.setData([x], [y])

                # IMPORTANT: Hide the mouse cursor when hovering over the chart
                # Only set cursor to blank at the widget level
                chart.setCursor(QtCore.Qt.CursorShape.BlankCursor)

                # Calculate price if pivot is available
                price_str = "Price: --"
                price_val = None
                market_odds_tab = self.get_current_market_odds_tab()
                if market_odds_tab:
                    pivot_price = getattr(market_odds_tab, "current_pivot", None)
                    if pivot_price is not None and chart_name in ['peaks', 'troughs', 'combined', 'ybm']:
                        actual_price = pivot_price * (1 + y / 100)
                        price_str = f"Price: {actual_price:.2f}"
                        price_val = actual_price

                # Update coordinate display with chart name
                coords_text = f"{chart_name.capitalize()} | X: {x:.2f} | Y: {y:.2f} | {price_str}"
                self.coords_label.setText(coords_text)

                # Hide tooltip (removed as per user request)
                tooltip.setVisible(False)
            else:
                # Hide current chart's crosshair and tooltip when not hovering
                vLine.setVisible(False)
                hLine.setVisible(False)
                tooltip.setVisible(False)
                spot.clear()

                # Restore the default cursor when outside the chart
                chart.setCursor(QtCore.Qt.CursorShape.ArrowCursor)
        except Exception as e:
            # Catch any exceptions during mouse movement to prevent app crashes
            logger.debug(f"Error in mouseMoved: {e}")
            # Ensure cursor is restored and crosshairs are hidden
            try:
                chart.setCursor(QtCore.Qt.CursorShape.ArrowCursor)
                vLine.setVisible(False)
                hLine.setVisible(False)
                tooltip.setVisible(False)
                spot.clear()
            except:
                pass

    def find_tab_widget(self):
        """Find the parent QTabWidget"""
        widget = self.parentWidget()
        while widget:
            if isinstance(widget, QtWidgets.QTabWidget):
                return widget
            widget = widget.parentWidget()
        return None

    def parse_label_text(self, text):
        """
        Parses a label text assumed to be in a format like:
            "H (1) 2.53%"  or  "L (5) 10%"
        Returns a tuple: (extreme_type, candle_count, percentage)
          - extreme_type: 'H' or 'L'
          - candle_count: int (the number inside the parentheses)
          - percentage: float (the extreme percentage)
        Returns None if parsing fails.
        """
        import re
        text = text.strip()
        if not text:
            return None
        if text.startswith("H"):
            extreme_type = "H"
        elif text.startswith("L"):
            extreme_type = "L"
        else:
            return None
        m_count = re.search(r'\((\d+)\)', text)
        if not m_count:
            return None
        candle_count = int(m_count.group(1))
        m_pct = re.search(r'(\d+(\.\d+)?)\%?', text)
        if not m_pct:
            return None
        percentage = float(m_pct.group(1))
        return (extreme_type, candle_count, percentage)

    def _setup_chart(self, chart, title):
        """Helper method to set up chart styling consistently"""
        # Set background and colors
        chart.setBackground('#1e1e1e')  # Dark grey background
        chart.showGrid(x=True, y=True, alpha=0.1)

        # Style the axes
        chart.getAxis('left').setTextPen(self.chart_colors['text'])
        chart.getAxis('bottom').setTextPen(self.chart_colors['text'])
        chart.getAxis('left').setPen(self.chart_colors['axis'])
        chart.getAxis('bottom').setPen(self.chart_colors['axis'])

        # Set title with HTML styling
        chart.setTitle(f'<div style="color: {self.chart_colors["text"]}; font-size: 14pt;">{title}</div>')

        # Style the plot
        chart.getViewBox().setBackgroundColor('#1e1e1e')  # Dark grey background

        # Set default labels - these may be overridden for specific charts
        if title != "YBM Chart":  # Don't set the left label for YBM chart as it's set separately
            chart.setLabel('left', '<div style="color: #e0e0e0;">Occurrence</div>')
        chart.setLabel('bottom', '<div style="color: #e0e0e0;">Price Level (%)</div>')

        # Remove axis numbers (if needed)
        chart.getPlotItem().getAxis('bottom').setTicks([])

    def _create_bar_label(self, text, color='white'):
        """Helper method to create consistently styled bar labels"""
        return f'<div style="text-align: center; padding: 2px 4px; background-color: rgba(45,45,45,0.8); border: 1px solid {color}; border-radius: 2px; font-size: 10pt; color: {color};">{text}</div>'

    def get_current_market_odds_tab(self):
        """Get the currently active Market Odds tab from the MainWindow.

        This method gets the Market Odds tab from the MainWindow to ensure we're using
        the current chart data instead of always using the first tab.
        """
        # First try to get the MainWindow instance
        main_window = None
        for widget in QtWidgets.QApplication.topLevelWidgets():
            if type(widget).__name__ == 'MainWindow':
                main_window = widget
                break

        if main_window and hasattr(main_window, 'market_odds_tab'):
            return main_window.market_odds_tab

        # Fallback to the first tab if MainWindow not found
        tab_widget = self.find_tab_widget()
        if tab_widget is not None:
            return tab_widget.widget(0)

        return None

    def get_data_tab(self):
        """Get the Data tab from the MainWindow.

        This method gets the Data tab from the MainWindow to access the Category column data.
        """
        # First try to get the MainWindow instance
        main_window = None
        for widget in QtWidgets.QApplication.topLevelWidgets():
            if type(widget).__name__ == 'MainWindow':
                main_window = widget
                break

        # Try to find the data tab in the main window
        if main_window:
            # Check if there's a data_tab attribute
            if hasattr(main_window, 'data_tab'):
                logger.debug("Found data_tab attribute in MainWindow")
                return main_window.data_tab
            # Check if there's a tab_widget with a data tab
            elif hasattr(main_window, 'tab_widget'):
                logger.debug(f"MainWindow has tab_widget with {main_window.tab_widget.count()} tabs")
                # Look for a tab with DataTab class
                for i in range(main_window.tab_widget.count()):
                    tab = main_window.tab_widget.widget(i)
                    tab_name = tab.__class__.__name__
                    logger.debug(f"Tab {i}: {tab_name}")
                    if tab_name == 'DataTab':
                        logger.debug(f"Found DataTab at index {i}")
                        return tab

                    # Also check if there's a tab with 'Data' in its title
                    tab_title = main_window.tab_widget.tabText(i)
                    logger.debug(f"Tab {i} title: {tab_title}")
                    if 'Data' in tab_title:
                        logger.debug(f"Found tab with 'Data' in title at index {i}")
                        return tab

        # If we couldn't find it in the main window, try to find it in all top-level widgets
        logger.debug("Searching for DataTab in all top-level widgets...")
        for widget in QtWidgets.QApplication.topLevelWidgets():
            # Check if this widget is a tab widget
            if isinstance(widget, QtWidgets.QTabWidget):
                logger.debug(f"Found QTabWidget with {widget.count()} tabs")
                # Look for a tab with DataTab class
                for i in range(widget.count()):
                    tab = widget.widget(i)
                    tab_name = tab.__class__.__name__
                    logger.debug(f"Tab {i}: {tab_name}")
                    if tab_name == 'DataTab':
                        logger.debug(f"Found DataTab at index {i}")
                        return tab

                    # Also check if there's a tab with 'Data' in its title
                    tab_title = widget.tabText(i)
                    logger.debug(f"Tab {i} title: {tab_title}")
                    if 'Data' in tab_title:
                        logger.debug(f"Found tab with 'Data' in title at index {i}")
                        return tab

        logger.debug("Could not find DataTab")
        return None

    def change_chart(self, index):
        """Change the displayed chart based on dropdown selection"""
        self.chart_stack.setCurrentIndex(index)

        # Show/hide chart-specific controls based on chart selection
        is_average_range_chart = (index == 1)  # Average Range chart is at index 1
        is_combined_chart = (index == 0)  # Combined chart is at index 0

        # Combined chart controls
        self.combined_sort_selector.setVisible(is_combined_chart)

        # Average Range chart controls
        self.rebase_reference_selector.setVisible(is_average_range_chart)
        self.rebase_label.setVisible(is_average_range_chart)
        self.split_sort_checkbox.setVisible(is_average_range_chart)
        self.show_stats_checkbox.setVisible(is_average_range_chart)
        self.show_density_heatmap_checkbox.setVisible(is_average_range_chart)
        self.show_density_profile_checkbox.setVisible(is_average_range_chart)
        self.show_ohlc_candles_checkbox.setVisible(is_average_range_chart)
        self.use_full_candle_length_checkbox.setVisible(is_average_range_chart)

        # Show/hide individual stat toggles based on chart selection and main stats checkbox
        show_individual_toggles = is_average_range_chart and self.show_stats_checkbox.isChecked()
        for stat_checkbox in self.stat_toggles.values():
            stat_checkbox.setVisible(show_individual_toggles)

        # Show/hide maximize buttons based on chart selection
        self.combined_maximize_button.setVisible(is_combined_chart)
        self.ybm_maximize_button.setVisible(is_average_range_chart)

        # Update charts based on selection
        if index == 1 and self.isVisible():
            self.update_ybm_chart()
        elif index == 2 and self.isVisible():
            self.update_range_data_metrics()

    def _on_show_stats_changed(self):
        """Handle changes to the main show stats checkbox"""
        # Update visibility of individual stat toggles
        is_average_range_chart = (self.chart_selector.currentIndex() == 1)
        show_individual_toggles = is_average_range_chart and self.show_stats_checkbox.isChecked()
        for stat_checkbox in self.stat_toggles.values():
            stat_checkbox.setVisible(show_individual_toggles)

        # Update the chart
        self.update_ybm_chart()

    def update_charts(self):
        try:
            # Check if the tab is visible before updating
            if not self.isVisible():
                logger.debug("OptionsAnalyzerTab: Tab not visible, skipping update.")
                return

            # Get the currently active Market Odds tab
            market_odds_tab = self.get_current_market_odds_tab()
            if market_odds_tab is None:
                logger.debug("OptionsAnalyzerTab: Market Odds tab not found.")
                return
            if not hasattr(market_odds_tab, "cycle_extreme_labels"):
                logger.debug("OptionsAnalyzerTab: Market Odds tab missing cycle_extreme_labels.")
                return

            # Update ticker, wave number, and DTL information
            try:
                # Get ticker
                ticker = market_odds_tab.symbol_input.text().strip().upper() or "--"
                self.ticker_label.setText(f"Ticker: {ticker}")

                # Get wave number (vector length)
                if hasattr(market_odds_tab, "settings_dialog") and hasattr(market_odds_tab.settings_dialog, "vector_length_spin"):
                    wave_num = market_odds_tab.settings_dialog.vector_length_spin.value()
                    self.wave_label.setText(f"The Line Length: {wave_num}")
                else:
                    self.wave_label.setText("The Line Length: --")

                # Get DTL/bar count
                if hasattr(market_odds_tab, "dtl_spin"):
                    dtl_value = market_odds_tab.dtl_spin.value()
                    self.dtl_label.setText(f"DTL: {dtl_value}")
                else:
                    self.dtl_label.setText("DTL: --")
            except Exception as e:
                logger.debug(f"Error updating info panel: {e}")

            labels = market_odds_tab.cycle_extreme_labels
            logger.debug("Found", len(labels), "extreme labels.")

            # --- Save crosshair items before clearing ---
            crosshair_items = {
                'peaks': {'vLine': self.peaks_vLine, 'hLine': self.peaks_hLine,
                          'spot': self.peaks_spot, 'tooltip': self.peaks_tooltip},
                'troughs': {'vLine': self.troughs_vLine, 'hLine': self.troughs_hLine,
                            'spot': self.troughs_spot, 'tooltip': self.troughs_tooltip},
                'combined': {'vLine': self.combined_vLine, 'hLine': self.combined_hLine,
                             'spot': self.combined_spot, 'tooltip': self.combined_tooltip},
                'ybm': {'vLine': self.ybm_vLine, 'hLine': self.ybm_hLine,
                        'spot': self.ybm_spot, 'tooltip': self.ybm_tooltip}
            }

            # --- Try to get data from the Data tab's Category column ---
            peaks_agg = {}
            troughs_agg = {}
            # --- Data for Combined Chart (chronological order) ---
            combined_x = []         # chronological event index
            combined_y = []         # bar height = y coordinate from Market Odds tab (percentage)
            combined_counts = []      # candle count for labeling
            event_idx = 0

            # First try to get data from the Data tab
            data_tab = self.get_data_tab()
            if data_tab and hasattr(data_tab, 'table_model') and data_tab.table_model._data is not None and not data_tab.table_model._data.empty:
                # Check if Category column exists
                if 'Category' in data_tab.table_model._data.columns:
                    logger.debug("Using Category column from Data tab for peaks and troughs charts")
                    logger.debug(f"Category column data: {data_tab.table_model._data['Category'].value_counts().to_dict()}")

                    # Check if Peak/Trough column exists to filter only actual peaks and troughs
                    has_peak_trough_column = 'Peak/Trough' in data_tab.table_model._data.columns
                    logger.debug(f"Peak/Trough column exists: {has_peak_trough_column}")

                    # Get the data we need
                    df = data_tab.table_model._data

                    # Process each row to identify peaks and troughs
                    for i in range(len(df)):
                        # Skip if Category is missing or not a string
                        if 'Category' not in df.columns or not isinstance(df['Category'].iloc[i], str):
                            continue

                        hl_value = df['Category'].iloc[i]

                        # Parse the Category value (format: "1H" or "2L")
                        import re
                        match = re.match(r'(\d+)([HL])', hl_value)
                        if not match:
                            continue

                        count = int(match.group(1))  # The position number
                        type_char = match.group(2)   # 'H' or 'L'

                        # If Peak/Trough column exists, use it to filter
                        if has_peak_trough_column:
                            peak_trough_value = df['Peak/Trough'].iloc[i]

                            # Only include peaks in peaks_agg and troughs in troughs_agg
                            if type_char == 'H' and peak_trough_value == "peak":
                                # Use the count as the key for aggregation
                                peaks_agg[count] = peaks_agg.get(count, 0) + 1
                                logger.debug(f"Added peak at position {count} with value {hl_value}")
                            elif type_char == 'L' and peak_trough_value == "trough":
                                # Use the count as the key for aggregation
                                troughs_agg[count] = troughs_agg.get(count, 0) + 1
                                logger.debug(f"Added trough at position {count} with value {hl_value}")
                        else:
                            # Fallback to using all H/L values if Peak/Trough column doesn't exist
                            if type_char == 'H':
                                # Use the count as the key for aggregation
                                peaks_agg[count] = peaks_agg.get(count, 0) + 1
                            else:  # 'L'
                                troughs_agg[count] = troughs_agg.get(count, 0) + 1
                else:
                    logger.debug("Category column not found in Data tab")
            else:
                logger.debug("Data tab not found or empty, falling back to label parsing")
                # Fall back to the original method if Data tab is not available
                # We'll try to identify peaks and troughs based on the label positions
                # First, collect all labels and their positions
                all_labels = []
                for item in labels:
                    try:
                        txt = item.toPlainText()
                        pos = item.pos()
                        parsed = self.parse_label_text(txt)
                        if parsed:
                            extreme_type, candle_count, pct_parsed = parsed
                            all_labels.append((extreme_type, candle_count, pct_parsed, pos.x(), pos.y()))
                    except Exception:
                        continue

                # Sort labels by x position to identify peaks and troughs
                all_labels.sort(key=lambda x: x[3])  # Sort by x position

                # Identify peaks and troughs by comparing with neighbors
                for i in range(len(all_labels)):
                    current = all_labels[i]
                    extreme_type, candle_count, pct_parsed, _, y_pos = current  # _ for unused x_pos

                    # Check if this is a peak or trough by comparing with neighbors
                    is_peak = False
                    is_trough = False

                    if extreme_type == "H":
                        # For peaks, check if this is a local maximum
                        if i > 0 and i < len(all_labels) - 1:
                            prev_y = all_labels[i-1][4]
                            next_y = all_labels[i+1][4]
                            if y_pos > prev_y and y_pos > next_y:
                                is_peak = True
                        # If it's the first or last point, consider it a peak if it's higher than its neighbor
                        elif i == 0 and len(all_labels) > 1 and y_pos > all_labels[i+1][4]:
                            is_peak = True
                        elif i == len(all_labels) - 1 and len(all_labels) > 1 and y_pos > all_labels[i-1][4]:
                            is_peak = True

                        # Add to peaks_agg if it's a peak
                        if is_peak:
                            peaks_agg[candle_count] = peaks_agg.get(candle_count, 0) + 1
                            logger.debug(f"Added peak at position {candle_count} with value {pct_parsed}")
                    else:  # extreme_type == "L"
                        # For troughs, check if this is a local minimum
                        if i > 0 and i < len(all_labels) - 1:
                            prev_y = all_labels[i-1][4]
                            next_y = all_labels[i+1][4]
                            if y_pos < prev_y and y_pos < next_y:
                                is_trough = True
                        # If it's the first or last point, consider it a trough if it's lower than its neighbor
                        elif i == 0 and len(all_labels) > 1 and y_pos < all_labels[i+1][4]:
                            is_trough = True
                        elif i == len(all_labels) - 1 and len(all_labels) > 1 and y_pos < all_labels[i-1][4]:
                            is_trough = True

                        # Add to troughs_agg if it's a trough
                        if is_trough:
                            troughs_agg[candle_count] = troughs_agg.get(candle_count, 0) + 1
                            logger.debug(f"Added trough at position {candle_count} with value {pct_parsed}")

                # For combined chart, use all labels (not just peaks and troughs)
                for item in labels:
                    try:
                        txt = item.toPlainText()
                        parsed = self.parse_label_text(txt)
                        if not parsed:
                            continue
                        extreme_type, candle_count, pct_parsed = parsed
                        # For combined chart, use the actual y coordinate of the item.
                        y_coord = item.pos().y()
                        combined_x.append(event_idx)
                        combined_y.append(y_coord)
                        combined_counts.append(candle_count)
                        event_idx += 1
                    except Exception:
                        continue

            # If we got data from the Data tab, we still need to populate the combined chart data
            if data_tab and not combined_x and (peaks_agg or troughs_agg):
                # We'll use the market odds tab labels for the combined chart
                for item in labels:
                    try:
                        txt = item.toPlainText()
                    except Exception:
                        txt = ""
                    parsed = self.parse_label_text(txt)
                    if not parsed:
                        continue
                    extreme_type, candle_count, pct_parsed = parsed
                    # For combined chart, use the actual y coordinate of the item.
                    y_coord = item.pos().y()
                    combined_x.append(event_idx)
                    combined_y.append(y_coord)
                    combined_counts.append(candle_count)
                    event_idx += 1

            # Remove crosshair elements before clearing the chart
            for prefix in ['peaks', 'troughs', 'combined', 'ybm']:
                chart = getattr(self, f"{prefix}_chart")
                chart.removeItem(crosshair_items[prefix]['vLine'])
                chart.removeItem(crosshair_items[prefix]['hLine'])
                chart.removeItem(crosshair_items[prefix]['spot'])
                chart.removeItem(crosshair_items[prefix]['tooltip'])

            # ----- Update Peaks Chart (Aggregated Highs) -----
            self.peaks_chart.clear()
            if peaks_agg:
                xs = np.array(sorted(peaks_agg.keys()))
                ys = np.array([peaks_agg[x] for x in xs])
                bar_peaks = pg.BarGraphItem(
                    x=xs,
                    height=ys,
                    width=0.6,
                    brush=self.chart_colors['bullish'],
                    pen=pg.mkPen(color=self.chart_colors['bullish'], width=1)
                )
                self.peaks_chart.addItem(bar_peaks)
                # Set X and Y ranges with safety checks
                if len(xs) > 0 and len(ys) > 0:
                    x_min, x_max = min(xs), max(xs)
                    y_max = max(ys)
                    if np.isfinite(x_min) and np.isfinite(x_max) and np.isfinite(y_max):
                        self.peaks_chart.setXRange(x_min-1, x_max+1)
                        self.peaks_chart.setYRange(0, y_max*1.3)
                total_peaks = float(sum(ys))
                for x_val, count in zip(xs, ys):
                    label = pg.TextItem(anchor=(0.5, 1))
                    label.setHtml(self._create_bar_label(
                        f"{x_val}H<br>{count} ({(count/total_peaks*100):.0f}%)",
                        self.chart_colors['bullish']
                    ))
                    label.setPos(x_val, count+0.5)
                    self.peaks_chart.addItem(label)
            else:
                logger.debug("No aggregated peaks data.")

            # crosshair elements to peaks chart
            self.peaks_chart.addItem(self.peaks_vLine, ignoreBounds=True)
            self.peaks_chart.addItem(self.peaks_hLine, ignoreBounds=True)
            self.peaks_chart.addItem(self.peaks_spot)
            self.peaks_chart.addItem(self.peaks_tooltip)

            # ----- Update Troughs Chart (Aggregated Lows) -----
            self.troughs_chart.clear()
            if troughs_agg:
                xs = np.array(sorted(troughs_agg.keys()))
                ys = np.array([troughs_agg[x] for x in xs])
                bar_troughs = pg.BarGraphItem(
                    x=xs,
                    height=ys,
                    width=0.6,
                    brush=self.chart_colors['bearish'],
                    pen=pg.mkPen(color=self.chart_colors['bearish'], width=1)
                )
                self.troughs_chart.addItem(bar_troughs)
                # Set X and Y ranges with safety checks
                if len(xs) > 0 and len(ys) > 0:
                    x_min, x_max = min(xs), max(xs)
                    y_max = max(ys)
                    if np.isfinite(x_min) and np.isfinite(x_max) and np.isfinite(y_max):
                        self.troughs_chart.setXRange(x_min-1, x_max+1)
                        self.troughs_chart.setYRange(0, y_max*1.3)
                total_troughs = float(sum(ys))
                for x_val, count in zip(xs, ys):
                    label = pg.TextItem(anchor=(0.5, 1))
                    label.setHtml(self._create_bar_label(
                        f"{x_val}L<br>{count} ({(count/total_troughs*100):.0f}%)",
                        self.chart_colors['bearish']
                    ))
                    label.setPos(x_val, count+0.5)
                    self.troughs_chart.addItem(label)
            else:
                logger.debug("No aggregated troughs data.")

            # Re-add crosshair elements to troughs chart
            self.troughs_chart.addItem(self.troughs_vLine, ignoreBounds=True)
            self.troughs_chart.addItem(self.troughs_hLine, ignoreBounds=True)
            self.troughs_chart.addItem(self.troughs_spot)
            self.troughs_chart.addItem(self.troughs_tooltip)

            # ----- Update Combined Chart (Chronological Extremes) -----
            self.combined_chart.clear()
            if combined_x:
                # Check sorting mode
                is_sorted_mode = self.combined_sort_selector.currentIndex() == 1  # "Sorted by Height"

                if is_sorted_mode:
                    # Sort by height mode - stack positive and negative values vertically
                    combined_data = list(zip(combined_x, combined_y, combined_counts))

                    # Separate positive and negative values
                    positive_data = [(x, y, cnt) for x, y, cnt in combined_data if y >= 0]
                    negative_data = [(x, y, cnt) for x, y, cnt in combined_data if y < 0]

                    # Sort positive values by height (ascending - smallest to largest)
                    positive_data.sort(key=lambda item: item[1])

                    # Sort negative values by absolute height (ascending - smallest magnitude to largest)
                    negative_data.sort(key=lambda item: abs(item[1]))

                    # Create stacked data - both positive and negative at same x positions
                    # Use the maximum count to determine how many x positions we need
                    max_count = max(len(positive_data), len(negative_data))

                    sorted_data = []

                    # Add bars at each x position, stacking positive above and negative below
                    for i in range(max_count):
                        x_position = i

                        # Add negative bar if available (below zero line)
                        if i < len(negative_data):
                            _, y_neg, cnt_neg = negative_data[i]
                            sorted_data.append((x_position, y_neg, cnt_neg))

                        # Add positive bar if available (above zero line)
                        if i < len(positive_data):
                            _, y_pos, cnt_pos = positive_data[i]
                            sorted_data.append((x_position, y_pos, cnt_pos))

                    # Update arrays for rendering
                    x_arr = np.array([x for x, y, cnt in sorted_data], dtype=float)
                    y_arr = np.array([y for x, y, cnt in sorted_data], dtype=float)
                    count_arr = [cnt for x, y, cnt in sorted_data]

                    # Set x-axis range for sorted mode
                    self.combined_chart.setXRange(-1, max_count)

                else:
                    # Chronological mode (original behavior)
                    x_arr = np.array(combined_x, dtype=float)
                    y_arr = np.array(combined_y, dtype=float)
                    count_arr = combined_counts

                    # Extend the x-axis further into the negative to allow space for the additional horizontal labels.
                    self.combined_chart.setXRange(-3, event_idx + 1)

                # Render bars
                bar_width = 0.6
                for xx, yy, cnt in zip(x_arr, y_arr, count_arr):
                    color = self.chart_colors['bullish'] if yy >= 0 else self.chart_colors['bearish']
                    bar = pg.BarGraphItem(
                        x=[xx],
                        height=[yy],
                        width=bar_width,
                        brush=color,
                        pen=pg.mkPen(color=color, width=1)
                    )
                    self.combined_chart.addItem(bar)
                    label = pg.TextItem(anchor=(0.5, 1 if yy >= 0 else 0))
                    label.setHtml(self._create_bar_label(str(cnt), color))
                    label.setZValue(1000)
                    label.setPos(xx, yy + (0.3 if yy >= 0 else -0.3))
                    self.combined_chart.addItem(label)

                # Safely calculate y range with checks for empty arrays
                if len(y_arr) > 0:
                    y_min, y_max = float(min(y_arr)), float(max(y_arr))
                    # Check for NaN or infinite values
                    if not (np.isnan(y_min) or np.isnan(y_max) or np.isinf(y_min) or np.isinf(y_max)):
                        pad = (y_max - y_min) * 0.1 if (y_max - y_min) != 0 else 1
                        self.combined_chart.setYRange(y_min - pad, y_max + pad)

                # Add horizontal lines
                pivot_line = pg.InfiniteLine(pos=0, angle=0, pen=pg.mkPen('w', width=2))
                self.combined_chart.addItem(pivot_line)
                pivot_price = getattr(market_odds_tab, "current_pivot", None)
                if pivot_price is not None:
                    pivot_label = pg.TextItem(anchor=(1, 0.5))
                    pivot_label.setHtml(self._create_bar_label(f"Extrema: {pivot_price:.2f}", "#FFC107"))
                    pivot_label.setZValue(1000)
                    pivot_label.setPos(0, 0)
                    self.combined_chart.addItem(pivot_label)

                # Calculate and draw averages
                green_vals = [val for val in y_arr if val >= 0]
                red_vals = [val for val in y_arr if val < 0]
                if green_vals:
                    avg_green = np.mean(green_vals)
                    avg_green_line = pg.InfiniteLine(pos=avg_green, angle=0, pen=pg.mkPen(self.chart_colors['bullish'], width=2))
                    self.combined_chart.addItem(avg_green_line)
                    avg_green_label = pg.TextItem(anchor=(1, 0.5))
                    avg_green_label.setHtml(self._create_bar_label(f"Avg: {avg_green:.2f}", self.chart_colors['bullish']))
                    avg_green_label.setZValue(1000)
                    avg_green_label.setPos(0, avg_green)
                    self.combined_chart.addItem(avg_green_label)
                if red_vals:
                    avg_red = np.mean(red_vals)
                    avg_red_line = pg.InfiniteLine(pos=avg_red, angle=0, pen=pg.mkPen(self.chart_colors['bearish'], width=2))
                    self.combined_chart.addItem(avg_red_line)
                    avg_red_label = pg.TextItem(anchor=(1, 0.5))
                    avg_red_label.setHtml(self._create_bar_label(f"Avg: {avg_red:.2f}", self.chart_colors['bearish']))
                    avg_red_label.setZValue(1000)
                    avg_red_label.setPos(0, avg_red)
                    self.combined_chart.addItem(avg_red_label)
            else:
                logger.debug("No combined extreme data to display.")

            # crosshair elements to combined chart
            self.combined_chart.addItem(self.combined_vLine, ignoreBounds=True)
            self.combined_chart.addItem(self.combined_hLine, ignoreBounds=True)
            self.combined_chart.addItem(self.combined_spot)
            self.combined_chart.addItem(self.combined_tooltip)

            # crosshair elements to YBM chart
            self.ybm_chart.addItem(self.ybm_vLine, ignoreBounds=True)
            self.ybm_chart.addItem(self.ybm_hLine, ignoreBounds=True)
            self.ybm_chart.addItem(self.ybm_spot)
            self.ybm_chart.addItem(self.ybm_tooltip)

            # Update YBM chart if it's selected
            if self.chart_selector.currentIndex() == 1:  # YBM chart is selected
                self.update_ybm_chart()
            elif self.chart_selector.currentIndex() == 2:  # Range Data is selected
                self.update_range_data_metrics()



        except Exception as e:
            logger.debug(f"Error updating Options Analyzer charts: {e}")
            traceback.print_exc()



    def showEvent(self, event):
        """Handle show event when the tab is shown."""
        super().showEvent(event)
        # No automatic timer - only update once when tab is shown
        logger.debug("OptionsAnalyzerTab: Tab shown, performing single update")

        # Update charts once when tab is shown
        QtCore.QTimer.singleShot(200, self.update_charts)

    def hideEvent(self, event):
        """Handle hide event when the tab is hidden."""
        super().hideEvent(event)
        # No timer to stop - charts only update manually or when tab is shown
        logger.debug("OptionsAnalyzerTab: Tab hidden")

    def on_data_fetched_universal(self, symbol, data):
        """Handle data_fetched signal from universal_controls.

        Args:
            symbol (str): The symbol for which data was fetched
            data (pd.DataFrame): The fetched data
        """
        logger.debug(f"OptionsAnalyzerTab: Data fetched from universal_controls for {symbol}, updating charts")
        # Update charts when new data is available from universal controls
        if self.isVisible():
            QtCore.QTimer.singleShot(200, self.update_charts)

    def update_ybm_chart(self):
        """Update the YBM chart with OHLC bars for all data points"""
        try:
            # Check if the tab is visible before updating
            if not self.isVisible():
                logger.debug("OptionsAnalyzerTab: Tab not visible, skipping YBM chart update.")
                return

            # Clear the YBM chart
            self.ybm_chart.clear()

            # Re-add crosshair elements
            self.ybm_chart.addItem(self.ybm_vLine, ignoreBounds=True)
            self.ybm_chart.addItem(self.ybm_hLine, ignoreBounds=True)
            self.ybm_chart.addItem(self.ybm_spot)
            self.ybm_chart.addItem(self.ybm_tooltip)

            # Get market odds tab to access pivot price and data
            market_odds_tab = self.get_current_market_odds_tab()
            if not market_odds_tab or not hasattr(market_odds_tab, 'current_pivot'):
                logger.debug("Market odds tab or pivot price not available.")
                return

            pivot_price = market_odds_tab.current_pivot
            if pivot_price is None:
                logger.debug("Pivot price is None.")
                return

            # Get market data
            if not hasattr(market_odds_tab, 'data') or market_odds_tab.data is None or market_odds_tab.data.empty:
                logger.debug("No market data available.")
                return

            market_data = market_odds_tab.data

            # Calculate 29.5% of the data points, rounding down
            num_points = len(market_data)
            points_to_use = int(num_points * 0.295)  # Integer division to round down

            # Take the most recent points (last 29.5%) plus two extra candles for rebasing reference
            start_idx = max(0, num_points - points_to_use - 2)  # Include two extra candles
            recent_data = market_data.iloc[start_idx:].copy()

            logger.debug(f"Using {len(recent_data)} days (29.5% of {num_points} total days)")

            # Get the selected rebase reference mode
            rebase_mode = self.rebase_reference_selector.currentText()

            if rebase_mode == "Current Price":
                # Current Price mode: dynamic rebasing with latest candle close as 0% reference
                reference_type_name = "Latest Candle Close Reference"
                reference_price = recent_data['Close'].iloc[-1]  # Use last available closing price from recent data
                logger.debug(f"Using reference type: {reference_type_name} (latest closing price: {reference_price})")

            else:
                # Previous Candle Close mode: dynamic rebasing
                reference_type_name = "Dynamic Rebasing"
                logger.debug(f"Using reference type: {reference_type_name}")

            # Create rebased data
            rebased_data = []

            # Track the dates to account for gaps
            dates = []

            # Skip the first two candles (index 0 and 1) - they are used for distance calculations but not displayed
            # The first 2 rows are loaded but not used in the system, they are invisible
            # The category starts at the 3rd row since the first 2 rows are NOT used anywhere
            # These 2 rows will be used for distance calculations so it does not START AT 0
            for i, (date, row) in enumerate(recent_data.iterrows()):
                if i < 2:
                    # Skip the first two candles - they're used for distance calculations but not displayed
                    continue

                # Store the date for gap detection
                dates.append(date)

                # Calculate reference price based on selected mode
                if rebase_mode == "Current Price":
                    # Current Price mode: dynamic rebasing - each candle uses the previous candle's close as reference (EXACT SAME AS PREVIOUS CANDLE CLOSE MODE)
                    current_reference_price = recent_data.iloc[recent_data.index.get_loc(date) - 1]['Close']
                else:
                    # Previous Candle Close mode: dynamic rebasing - each candle uses the previous candle's close as reference
                    current_reference_price = recent_data.iloc[recent_data.index.get_loc(date) - 1]['Close']

                # Calculate percentage change from reference price
                open_pct = ((row['Open'] / current_reference_price) - 1) * 100
                high_pct = ((row['High'] / current_reference_price) - 1) * 100
                low_pct = ((row['Low'] / current_reference_price) - 1) * 100
                close_pct = ((row['Close'] / current_reference_price) - 1) * 100

                # Store with sequential index for display (adjusted for skipped first two candles)
                rebased_data.append([float(i-2), open_pct, high_pct, low_pct, close_pct, date])

            # For "Current Price" mode, adjust all values so the latest candle's close becomes 0%
            if rebase_mode == "Current Price" and rebased_data:
                # Get the latest candle's close percentage
                latest_close_pct = rebased_data[-1][4]  # close_pct is at index 4

                # Adjust all candles by subtracting the latest close percentage
                for candle in rebased_data:
                    candle[1] -= latest_close_pct  # open_pct
                    candle[2] -= latest_close_pct  # high_pct
                    candle[3] -= latest_close_pct  # low_pct
                    candle[4] -= latest_close_pct  # close_pct

                logger.debug(f"Adjusted all values by -{latest_close_pct:.2f}% to make latest candle close = 0%")

            # Check for gaps between trading days
            has_gaps = False
            if len(dates) > 1:
                for i in range(1, len(dates)):
                    # Calculate the difference in days
                    if hasattr(dates[i], 'date'):
                        # If it's a datetime object
                        prev_date = dates[i-1].date() if hasattr(dates[i-1], 'date') else dates[i-1]
                        curr_date = dates[i].date()
                    else:
                        # If it's already a date object
                        prev_date = dates[i-1]
                        curr_date = dates[i]

                    try:
                        # Try to calculate the difference in days
                        day_diff = (curr_date - prev_date).days
                        if day_diff > 1:
                            has_gaps = True
                            logger.debug(f"Gap detected: {day_diff} days between {prev_date} and {curr_date}")
                    except Exception as e:
                        logger.debug(f"Error calculating date difference: {e}")

            logger.debug(f"Created rebased data with {len(rebased_data)} points, gaps detected: {has_gaps}")



            # Check if split sort is enabled
            split_sort_enabled = self.split_sort_checkbox.isChecked()

            # Prepare data for plotting
            all_x = []      # Store x positions for axis range
            all_y_high = [] # Store high values for axis range
            all_y_low = []  # Store low values for axis range

            # Add a horizontal line at y=0 (pivot line)
            zero_line = pg.InfiniteLine(pos=0, angle=0, pen=pg.mkPen('w', width=1, style=QtCore.Qt.PenStyle.DashLine))
            self.ybm_chart.addItem(zero_line)

            # Collect values above and below pivot for averages
            above_pivot_values = []
            below_pivot_values = []

            # Track highest high and lowest low for median calculations
            highest_high_overall = float('-inf')
            lowest_low_overall = float('inf')

            if split_sort_enabled:
                # Split and physically cut the candles at 0% line
                above_pivot_candles = []  # Portions of candles above 0%
                below_pivot_candles = []  # Portions of candles below 0%

                # Process each candle and cut it at the 0% line
                for candle in rebased_data:
                    # Format: [x, open_pct, high_pct, low_pct, close_pct, date]
                    x_pos = candle[0]
                    open_pct = candle[1]
                    high_pct = candle[2]
                    low_pct = candle[3]
                    close_pct = candle[4]
                    date = candle[5]

                    # Determine original candle color based on normal candlestick logic (close vs open)
                    original_is_bullish = close_pct >= open_pct
                    original_color = self.chart_colors['bullish'] if original_is_bullish else self.chart_colors['bearish']

                    # ALWAYS create above 0% portion if any part of candle is above 0%
                    if high_pct > 0:
                        # Cut the candle at 0% - only keep the portion above
                        cut_low = max(low_pct, 0)  # If low is below 0%, cut it at 0%
                        cut_open = max(open_pct, 0) if open_pct > 0 else 0  # Cut open at 0% if below
                        cut_close = max(close_pct, 0) if close_pct > 0 else 0  # Cut close at 0% if below

                        # Create the cut candle for above section (with original color info)
                        above_candle = [x_pos, cut_open, high_pct, cut_low, cut_close, date, original_color, original_is_bullish]
                        above_pivot_candles.append(above_candle)

                    # ALWAYS create below 0% portion if any part of candle is below 0%
                    if low_pct < 0:
                        # Cut the candle at 0% - only keep the portion below
                        cut_high = min(high_pct, 0)  # If high is above 0%, cut it at 0%
                        cut_open = min(open_pct, 0) if open_pct < 0 else 0  # Cut open at 0% if above
                        cut_close = min(close_pct, 0) if close_pct < 0 else 0  # Cut close at 0% if above

                        # Create the cut candle for below section (with original color info)
                        below_candle = [x_pos, cut_open, cut_high, low_pct, cut_close, date, original_color, original_is_bullish]
                        below_pivot_candles.append(below_candle)

                # Sort above_pivot_candles by distance from high to 0% (close to far)
                above_pivot_candles.sort(key=lambda x: x[2])  # Sort by high_pct (ascending)

                # Sort below_pivot_candles by distance from low to 0% (close to far)
                below_pivot_candles.sort(key=lambda x: abs(x[3]))  # Sort by absolute value of low_pct (ascending)

                # Combine the sorted datasets for display
                # Display above_pivot_candles first, then below_pivot_candles
                sorted_rebased_data = above_pivot_candles + below_pivot_candles

                logger.debug(f"Split sort: {len(above_pivot_candles)} cut portions above 0%, {len(below_pivot_candles)} cut portions below 0%")
                logger.debug(f"Original candles: {len(rebased_data)}, Total cut pieces: {len(above_pivot_candles) + len(below_pivot_candles)}")

                # Log some examples for debugging
                if above_pivot_candles:
                    logger.debug(f"First above portion - High: {above_pivot_candles[0][2]:.2f}%, Cut Low: {above_pivot_candles[0][3]:.2f}%")
                if below_pivot_candles:
                    logger.debug(f"First below portion - Cut High: {below_pivot_candles[0][2]:.2f}%, Low: {below_pivot_candles[0][3]:.2f}%")

                # Process the sorted data
                for i, candle in enumerate(sorted_rebased_data):
                    # Extract data from candle
                    x_position = float(i)   # Use sequential index for display

                    # Get percentage values
                    # Format: [x, open_pct, high_pct, low_pct, close_pct, date, original_color, original_is_bullish]
                    open_pct = candle[1]
                    high_pct = candle[2]
                    low_pct = candle[3]
                    close_pct = candle[4]
                    date = candle[5]  # Date for this candle
                    original_color = candle[6]  # Original color from before splitting
                    original_is_bullish = candle[7]  # Original bullish/bearish state

                    # Use the original candle color (same as normal chart)
                    color = original_color

                    # Update overall highest high and lowest low
                    if high_pct > highest_high_overall:
                        highest_high_overall = high_pct
                    if low_pct < lowest_low_overall:
                        lowest_low_overall = low_pct

                    # Store values for axis range calculation
                    all_x.append(x_position)
                    all_y_high.append(high_pct)
                    all_y_low.append(low_pct)

                    # Collect values for averages
                    if high_pct > 0:
                        above_pivot_values.append(high_pct)
                    if low_pct < 0:
                        below_pivot_values.append(low_pct)

                    # Create OHLC bar (only if enabled)
                    if self.show_ohlc_candles_checkbox.isChecked():
                        bar_width = 0.6
                        tick_width = bar_width / 2  # Width of open/close ticks

                        # Main vertical line from low to high
                        bar = pg.PlotDataItem(
                            [x_position, x_position],  # x values for vertical line
                            [low_pct, high_pct],       # y values from low to high
                            pen=pg.mkPen(color, width=2)
                        )
                        self.ybm_chart.addItem(bar)

                        # Add open tick (left side)
                        open_tick = pg.PlotDataItem(
                            [x_position - tick_width, x_position],
                            [open_pct, open_pct],
                            pen=pg.mkPen(color, width=2)
                        )
                        self.ybm_chart.addItem(open_tick)

                        # Add close tick (right side)
                        close_tick = pg.PlotDataItem(
                            [x_position, x_position + tick_width],
                            [close_pct, close_pct],
                            pen=pg.mkPen(color, width=2)
                        )
                        self.ybm_chart.addItem(close_tick)

                    # Date labels and vertical reference lines removed as requested

                    # Add labels to identify the sections
                    if all_y_high and all_y_low:
                        y_range = max(all_y_high) - min(all_y_low)
                        label_y_pos = max(all_y_high) + y_range * 0.05

                        # Label for above pivot section
                        above_label = pg.TextItem(
                            text="Above 0% Portions (sorted by high distance)",
                            color='#4CAF50',
                            anchor=(0.5, 0)
                        )
                        above_label.setPos(len(above_pivot_candles) / 2, label_y_pos)
                        self.ybm_chart.addItem(above_label)

                        # Label for below pivot section
                        if below_pivot_candles:
                            below_label = pg.TextItem(
                                text="Below 0% Portions (sorted by low distance)",
                                color='#F44336',
                                anchor=(0.5, 0)
                            )
                            below_label.setPos(len(above_pivot_candles) + len(below_pivot_candles) / 2, label_y_pos)
                            self.ybm_chart.addItem(below_label)

            else:
                # Original processing when split sort is disabled
                for i, candle in enumerate(rebased_data):
                    # Extract data from candle
                    x_position = float(i)   # Use sequential index for display

                    # Get percentage values
                    # Format: [x, open_pct, high_pct, low_pct, close_pct, date]
                    open_pct = candle[1]
                    high_pct = candle[2]
                    low_pct = candle[3]
                    close_pct = candle[4]
                    date = candle[5]  # Date for this candle

                    # Determine bar color (green for bullish, red for bearish) using normal candlestick logic
                    is_bullish = close_pct >= open_pct  # Bullish if close is above open
                    color = self.chart_colors['bullish'] if is_bullish else self.chart_colors['bearish']

                    # Update overall highest high and lowest low
                    if high_pct > highest_high_overall:
                        highest_high_overall = high_pct
                    if low_pct < lowest_low_overall:
                        lowest_low_overall = low_pct

                    # Store values for axis range calculation
                    all_x.append(x_position)
                    all_y_high.append(high_pct)
                    all_y_low.append(low_pct)

                    # Collect values for averages
                    if high_pct > 0:
                        above_pivot_values.append(high_pct)
                    if low_pct < 0:
                        below_pivot_values.append(low_pct)

                    # Create OHLC bar (only if enabled)
                    if self.show_ohlc_candles_checkbox.isChecked():
                        bar_width = 0.6
                        tick_width = bar_width / 2  # Width of open/close ticks



                        # Main vertical line from low to high
                        bar = pg.PlotDataItem(
                            [x_position, x_position],  # x values for vertical line
                            [low_pct, high_pct],       # y values from low to high
                            pen=pg.mkPen(color, width=2)
                        )
                        self.ybm_chart.addItem(bar)

                        # Add open tick (left side)
                        open_tick = pg.PlotDataItem(
                            [x_position - tick_width, x_position],
                            [open_pct, open_pct],
                            pen=pg.mkPen(color, width=2)
                        )
                        self.ybm_chart.addItem(open_tick)

                        # Add close tick (right side)
                        close_tick = pg.PlotDataItem(
                            [x_position, x_position + tick_width],
                            [close_pct, close_pct],
                            pen=pg.mkPen(color, width=2)
                        )
                        self.ybm_chart.addItem(close_tick)

                    # Date labels and vertical reference lines removed as requested

            # Set the axis ranges
            if all_x and all_y_high and all_y_low:
                x_min, x_max = min(all_x), max(all_x)
                y_min, y_max = min(all_y_low), max(all_y_high)

                # Add some padding
                x_padding = 2.5  # Fixed padding for x-axis (increased for more zoom out)
                y_padding = (y_max - y_min) * 0.1 if y_max > y_min else 1

                self.ybm_chart.setXRange(x_min - x_padding, x_max + x_padding)
                self.ybm_chart.setYRange(y_min - y_padding, y_max + y_padding)

            # Add a label for the zero line
            if all_x:
                # Set label text based on selected rebase mode
                if rebase_mode == "Current Price":
                    label_text = "Latest Candle Close (Current Price Mode): 0%"
                else:
                    label_text = "Dynamic Rebasing: 0%"

                zero_label = pg.TextItem(text=label_text, color='w', anchor=(1, 0.5))
                zero_label.setPos(-0.5, 0)  # Position to the left of the first bar
                self.ybm_chart.addItem(zero_label)

                # Calculate averages for above and below pivot
                avg_above = None
                avg_below = None

                if above_pivot_values:
                    avg_above = sum(above_pivot_values) / len(above_pivot_values)

                if below_pivot_values:
                    avg_below = sum(below_pivot_values) / len(below_pivot_values)

                # Add blue background between averages if both exist
                if avg_above is not None and avg_below is not None:
                    # Create a dark blue color between midnight blue and dark blue
                    dark_blue_color = pg.mkBrush(color=(25, 50, 100, 50))  # RGBA with alpha for transparency

                    # Create a filled region between the two averages
                    x_range = [min(all_x) - 1, max(all_x) + 1]  # Extend slightly beyond the bars

                    # Create the filled region
                    fill_region = pg.FillBetweenItem(
                        pg.PlotCurveItem([x_range[0], x_range[1]], [avg_above, avg_above]),
                        pg.PlotCurveItem([x_range[0], x_range[1]], [avg_below, avg_below]),
                        brush=dark_blue_color
                    )
                    self.ybm_chart.addItem(fill_region)

                # Add average lines for above and below pivot
                if avg_above is not None:
                    avg_above_line = pg.InfiniteLine(
                        pos=avg_above,
                        angle=0,
                        pen=pg.mkPen(self.chart_colors['bullish'], width=1, style=QtCore.Qt.PenStyle.DashLine)
                    )
                    self.ybm_chart.addItem(avg_above_line)

                    # Add label for average above
                    avg_above_label = pg.TextItem(
                        text=f"Avg High: {avg_above:.2f}%",
                        color=self.chart_colors['bullish'],
                        anchor=(1, 0.5)
                    )
                    avg_above_label.setPos(-0.5, avg_above)  # Position to the left of the first bar
                    self.ybm_chart.addItem(avg_above_label)

                if avg_below is not None:
                    avg_below_line = pg.InfiniteLine(
                        pos=avg_below,
                        angle=0,
                        pen=pg.mkPen(self.chart_colors['bearish'], width=1, style=QtCore.Qt.PenStyle.DashLine)
                    )
                    self.ybm_chart.addItem(avg_below_line)

                    # Add label for average below
                    avg_below_label = pg.TextItem(
                        text=f"Avg Low: {avg_below:.2f}%",
                        color=self.chart_colors['bearish'],
                        anchor=(1, 0.5)
                    )
                    avg_below_label.setPos(-0.5, avg_below)  # Position to the left of the first bar
                    self.ybm_chart.addItem(avg_below_label)

                # Add median line between pivot and highest high
                if highest_high_overall > 0:
                    median_high = highest_high_overall / 2  # 50% between 0 (pivot) and highest high
                    median_high_line = pg.InfiniteLine(
                        pos=median_high,
                        angle=0,
                        pen=pg.mkPen(self.chart_colors['bullish'], width=1, style=QtCore.Qt.PenStyle.DotLine)
                    )
                    self.ybm_chart.addItem(median_high_line)

                    # Add label for median high
                    median_high_label = pg.TextItem(
                        text=f"Median High: {median_high:.2f}%",
                        color=self.chart_colors['bullish'],
                        anchor=(1, 0.5)
                    )
                    median_high_label.setPos(-0.5, median_high)  # Position to the left of the first bar
                    self.ybm_chart.addItem(median_high_label)

                # Add median line between pivot and lowest low
                if lowest_low_overall < 0:
                    median_low = lowest_low_overall / 2  # 50% between 0 (pivot) and lowest low
                    median_low_line = pg.InfiniteLine(
                        pos=median_low,
                        angle=0,
                        pen=pg.mkPen(self.chart_colors['bearish'], width=1, style=QtCore.Qt.PenStyle.DotLine)
                    )
                    self.ybm_chart.addItem(median_low_line)

                    # Add label for median low
                    median_low_label = pg.TextItem(
                        text=f"Median Low: {median_low:.2f}%",
                        color=self.chart_colors['bearish'],
                        anchor=(1, 0.5)
                    )
                    median_low_label.setPos(-0.5, median_low)  # Position to the left of the first bar
                    self.ybm_chart.addItem(median_low_label)

                # Add density visualizations if enabled
                if self.show_density_heatmap_checkbox.isChecked() or self.show_density_profile_checkbox.isChecked():
                    if split_sort_enabled:
                        # Calculate separate densities for split mode
                        (above_bin_centers, above_density_counts, above_bin_edges,
                         below_bin_centers, below_density_counts, below_bin_edges) = self._calculate_split_candle_densities(
                            above_pivot_candles, below_pivot_candles
                        )

                        # Add split heatmap if enabled
                        if self.show_density_heatmap_checkbox.isChecked():
                            above_data = (above_bin_centers, above_density_counts, above_bin_edges)
                            below_data = (below_bin_centers, below_density_counts, below_bin_edges)
                            self._add_split_density_heatmap_to_chart(
                                above_data, below_data,
                                len(above_pivot_candles) if above_pivot_candles else 0,
                                len(below_pivot_candles) if below_pivot_candles else 0
                            )

                        # Add split density profile if enabled
                        if self.show_density_profile_checkbox.isChecked():
                            above_data = (above_bin_centers, above_density_counts, above_bin_edges)
                            below_data = (below_bin_centers, below_density_counts, below_bin_edges)
                            self._add_split_density_profile_to_chart(
                                above_data, below_data,
                                len(above_pivot_candles) if above_pivot_candles else 0,
                                len(below_pivot_candles) if below_pivot_candles else 0
                            )
                    else:
                        # Calculate density for normal mode
                        bin_centers, density_counts, bin_edges = self._calculate_candle_density(
                            rebased_data, split_sort_enabled
                        )

                        x_range = (min(all_x) if all_x else 0, max(all_x) if all_x else 10)

                        # Add heatmap if enabled
                        if self.show_density_heatmap_checkbox.isChecked() and bin_centers is not None:
                            self._add_density_heatmap_to_chart(bin_centers, density_counts, bin_edges, x_range)

                        # Add density profile if enabled
                        if self.show_density_profile_checkbox.isChecked() and bin_centers is not None:
                            self._add_density_profile_to_chart(bin_centers, density_counts, bin_edges, x_range)

                # Add statistical overlays if enabled and Range Data metrics are available
                if self.show_stats_checkbox.isChecked():
                    if split_sort_enabled:
                        # For split sort, use the cut candle data
                        self._add_statistical_overlays_split_mode(above_pivot_candles, below_pivot_candles)
                    else:
                        # For normal mode, use the original above/below pivot values
                        self._add_statistical_overlays_to_chart(above_pivot_values, below_pivot_values)
                    self._add_statistics_legend(split_sort_enabled)

                # Set simple title as requested
                self.ybm_chart.setTitle(f'<div style="color: {self.chart_colors["text"]}; font-size: 14pt;">Average Range Chart</div>')

                # Set x-axis title to "Dates"
                self.ybm_chart.setLabel('bottom', '<div style="color: #e0e0e0;">Dates</div>')



        except Exception as e:
            logger.debug(f"Error updating YBM chart: {e}")
            traceback.print_exc()

    def _setup_range_data_widget(self):
        """Set up the Range Data metrics widget"""
        layout = QtWidgets.QVBoxLayout(self.range_data_widget)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(15)

        # Title bar with maximize button
        title_bar = QtWidgets.QHBoxLayout()

        self.range_data_title_label = QtWidgets.QLabel("Range Data Statistical Metrics")
        self.range_data_title_label.setStyleSheet("""
            QLabel {
                color: #e0e0e0;
                font-size: 18pt;
                font-weight: bold;
                margin-bottom: 10px;
            }
        """)
        title_bar.addWidget(self.range_data_title_label)

        # Maximize/Minimize button
        self.maximize_button = QtWidgets.QPushButton("⛶")  # Maximize icon
        self.maximize_button.setFixedSize(40, 40)
        self.maximize_button.setStyleSheet("""
            QPushButton {
                background-color: #3e3e3e;
                color: #e0e0e0;
                border: 1px solid #5e5e5e;
                border-radius: 6px;
                font-size: 12pt;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #4e4e4e;
                border: 1px solid #6e6e6e;
            }
            QPushButton:pressed {
                background-color: #2e2e2e;
            }
        """)
        self.maximize_button.setToolTip("Maximize Range Data Tab")
        self.maximize_button.clicked.connect(self._toggle_maximize_range_data)
        title_bar.addWidget(self.maximize_button)

        layout.addLayout(title_bar)

        # Control panel for Range Data options
        control_panel = QtWidgets.QFrame()
        control_panel.setFrameStyle(QtWidgets.QFrame.Shape.StyledPanel | QtWidgets.QFrame.Shadow.Raised)
        control_panel.setStyleSheet("""
            QFrame {
                background-color: #2d2d2d;
                border: 1px solid #3e3e3e;
                border-radius: 8px;
                padding: 10px;
                margin: 5px;
            }
        """)
        control_layout = QtWidgets.QVBoxLayout(control_panel)
        control_layout.setContentsMargins(15, 10, 15, 10)
        control_layout.setSpacing(10)

        # First row - Mode and standard deviation customization
        first_row = QtWidgets.QHBoxLayout()

        # Split Sort checkbox for Range Data
        self.range_data_split_sort_checkbox = QtWidgets.QCheckBox("Use Split Sort Mode")
        self.range_data_split_sort_checkbox.setStyleSheet("""
            QCheckBox {
                color: #e0e0e0;
                font-size: 12pt;
                font-weight: bold;
            }
            QCheckBox::indicator {
                width: 18px;
                height: 18px;
            }
            QCheckBox::indicator:unchecked {
                background-color: #1e1e1e;
                border: 2px solid #3e3e3e;
                border-radius: 3px;
            }
            QCheckBox::indicator:checked {
                background-color: #4CAF50;
                border: 2px solid #4CAF50;
                border-radius: 3px;
            }
        """)
        self.range_data_split_sort_checkbox.setChecked(False)
        self.range_data_split_sort_checkbox.stateChanged.connect(self.update_range_data_metrics)
        first_row.addWidget(self.range_data_split_sort_checkbox)

        # Standard deviation multipliers
        std_label = QtWidgets.QLabel("Std Dev Levels:")
        std_label.setStyleSheet("color: #e0e0e0; font-size: 11pt; font-weight: bold;")
        first_row.addWidget(std_label)

        self.std_level_1_input = QtWidgets.QLineEdit("1.0")
        self.std_level_1_input.setFixedWidth(50)
        self.std_level_1_input.setStyleSheet("""
            QLineEdit {
                background-color: #3e3e3e;
                color: #e0e0e0;
                border: 1px solid #5e5e5e;
                border-radius: 4px;
                padding: 4px 6px;
                font-size: 10pt;
            }
            QLineEdit:focus {
                border: 1px solid #4CAF50;
            }
        """)
        self.std_level_1_input.textChanged.connect(self.update_range_data_metrics)
        first_row.addWidget(self.std_level_1_input)

        std_sigma_1 = QtWidgets.QLabel("σ")
        std_sigma_1.setStyleSheet("color: #b0b0b0; font-size: 10pt;")
        first_row.addWidget(std_sigma_1)

        self.std_level_2_input = QtWidgets.QLineEdit("2.0")
        self.std_level_2_input.setFixedWidth(50)
        self.std_level_2_input.setStyleSheet("""
            QLineEdit {
                background-color: #3e3e3e;
                color: #e0e0e0;
                border: 1px solid #5e5e5e;
                border-radius: 4px;
                padding: 4px 6px;
                font-size: 10pt;
            }
            QLineEdit:focus {
                border: 1px solid #4CAF50;
            }
        """)
        self.std_level_2_input.textChanged.connect(self.update_range_data_metrics)
        first_row.addWidget(self.std_level_2_input)

        std_sigma_2 = QtWidgets.QLabel("σ")
        std_sigma_2.setStyleSheet("color: #b0b0b0; font-size: 10pt;")
        first_row.addWidget(std_sigma_2)

        first_row.addStretch()
        control_layout.addLayout(first_row)

        # Second row - Statistical customizations
        second_row = QtWidgets.QHBoxLayout()

        # VaR confidence levels (custom input)
        var_label = QtWidgets.QLabel("VaR Levels:")
        var_label.setStyleSheet("color: #e0e0e0; font-size: 11pt; font-weight: bold;")
        second_row.addWidget(var_label)

        self.var_level_1_input = QtWidgets.QLineEdit("95")
        self.var_level_1_input.setFixedWidth(50)
        self.var_level_1_input.setStyleSheet("""
            QLineEdit {
                background-color: #3e3e3e;
                color: #e0e0e0;
                border: 1px solid #5e5e5e;
                border-radius: 4px;
                padding: 4px 6px;
                font-size: 10pt;
            }
            QLineEdit:focus {
                border: 1px solid #4CAF50;
            }
        """)
        self.var_level_1_input.textChanged.connect(self.update_range_data_metrics)
        second_row.addWidget(self.var_level_1_input)

        var_percent_1 = QtWidgets.QLabel("%")
        var_percent_1.setStyleSheet("color: #b0b0b0; font-size: 10pt;")
        second_row.addWidget(var_percent_1)

        self.var_level_2_input = QtWidgets.QLineEdit("99")
        self.var_level_2_input.setFixedWidth(50)
        self.var_level_2_input.setStyleSheet("""
            QLineEdit {
                background-color: #3e3e3e;
                color: #e0e0e0;
                border: 1px solid #5e5e5e;
                border-radius: 4px;
                padding: 4px 6px;
                font-size: 10pt;
            }
            QLineEdit:focus {
                border: 1px solid #4CAF50;
            }
        """)
        self.var_level_2_input.textChanged.connect(self.update_range_data_metrics)
        second_row.addWidget(self.var_level_2_input)

        var_percent_2 = QtWidgets.QLabel("%")
        var_percent_2.setStyleSheet("color: #b0b0b0; font-size: 10pt;")
        second_row.addWidget(var_percent_2)

        # Entropy bins selector
        entropy_label = QtWidgets.QLabel("Entropy Bins:")
        entropy_label.setStyleSheet("color: #e0e0e0; font-size: 11pt; font-weight: bold;")
        second_row.addWidget(entropy_label)

        self.entropy_bins_selector = QtWidgets.QComboBox()
        self.entropy_bins_selector.addItems(["5", "10", "15", "20", "Auto"])
        self.entropy_bins_selector.setCurrentText("Auto")
        self.entropy_bins_selector.setStyleSheet("""
            QComboBox {
                background-color: #3e3e3e;
                color: #e0e0e0;
                border: 1px solid #5e5e5e;
                border-radius: 4px;
                padding: 4px 8px;
                font-size: 10pt;
                min-width: 50px;
            }
            QComboBox::drop-down {
                border: none;
                width: 20px;
            }
            QComboBox::down-arrow {
                image: none;
                border-left: 5px solid transparent;
                border-right: 5px solid transparent;
                border-top: 5px solid #e0e0e0;
                margin-right: 5px;
            }
            QComboBox QAbstractItemView {
                background-color: #3e3e3e;
                color: #e0e0e0;
                selection-background-color: #4e4e4e;
                border: 1px solid #5e5e5e;
            }
        """)
        self.entropy_bins_selector.currentTextChanged.connect(self.update_range_data_metrics)
        second_row.addWidget(self.entropy_bins_selector)

        second_row.addStretch()
        control_layout.addLayout(second_row)

        # Third row - Display options
        third_row = QtWidgets.QHBoxLayout()

        # Precision selector
        precision_label = QtWidgets.QLabel("Precision:")
        precision_label.setStyleSheet("color: #e0e0e0; font-size: 11pt; font-weight: bold;")
        third_row.addWidget(precision_label)

        self.precision_selector = QtWidgets.QComboBox()
        self.precision_selector.addItems(["1 decimal", "2 decimals", "3 decimals", "4 decimals"])
        self.precision_selector.setCurrentText("2 decimals")
        self.precision_selector.setStyleSheet("""
            QComboBox {
                background-color: #3e3e3e;
                color: #e0e0e0;
                border: 1px solid #5e5e5e;
                border-radius: 4px;
                padding: 4px 8px;
                font-size: 10pt;
                min-width: 80px;
            }
            QComboBox::drop-down {
                border: none;
                width: 20px;
            }
            QComboBox::down-arrow {
                image: none;
                border-left: 5px solid transparent;
                border-right: 5px solid transparent;
                border-top: 5px solid #e0e0e0;
                margin-right: 5px;
            }
            QComboBox QAbstractItemView {
                background-color: #3e3e3e;
                color: #e0e0e0;
                selection-background-color: #4e4e4e;
                border: 1px solid #5e5e5e;
            }
        """)
        self.precision_selector.currentTextChanged.connect(self.update_range_data_metrics)
        third_row.addWidget(self.precision_selector)

        # Show Z-Score range checkbox
        self.show_zscore_range_checkbox = QtWidgets.QCheckBox("Show Z-Score Range")
        self.show_zscore_range_checkbox.setStyleSheet("""
            QCheckBox {
                color: #e0e0e0;
                font-size: 11pt;
            }
            QCheckBox::indicator {
                width: 16px;
                height: 16px;
            }
            QCheckBox::indicator:unchecked {
                background-color: #1e1e1e;
                border: 2px solid #3e3e3e;
                border-radius: 3px;
            }
            QCheckBox::indicator:checked {
                background-color: #4CAF50;
                border: 2px solid #4CAF50;
                border-radius: 3px;
            }
        """)
        self.show_zscore_range_checkbox.setChecked(True)
        self.show_zscore_range_checkbox.stateChanged.connect(self.update_range_data_metrics)
        third_row.addWidget(self.show_zscore_range_checkbox)

        third_row.addStretch()
        control_layout.addLayout(third_row)

        # Info label explaining the difference
        info_label = QtWidgets.QLabel("Normal: Full candle moves | Split: Cut candle ranges")
        info_label.setStyleSheet("""
            QLabel {
                color: #b0b0b0;
                font-size: 10pt;
                font-style: italic;
                text-align: center;
            }
        """)
        info_label.setAlignment(QtCore.Qt.AlignmentFlag.AlignCenter)
        control_layout.addWidget(info_label)

        layout.addWidget(control_panel)

        # Create scroll area for metrics
        scroll_area = QtWidgets.QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setStyleSheet("""
            QScrollArea {
                background-color: #1e1e1e;
                border: none;
            }
            QScrollBar:vertical {
                background-color: #2d2d2d;
                width: 12px;
                border-radius: 6px;
            }
            QScrollBar::handle:vertical {
                background-color: #4d4d4d;
                border-radius: 6px;
                min-height: 20px;
            }
            QScrollBar::handle:vertical:hover {
                background-color: #5d5d5d;
            }
        """)

        # Metrics container widget
        metrics_widget = QtWidgets.QWidget()
        metrics_layout = QtWidgets.QVBoxLayout(metrics_widget)
        metrics_layout.setSpacing(10)

        # Define metrics with their descriptions
        self.metrics_data = [
            ("Mean (μ)", "Average upside or downside move size", "mean_upside", "mean_downside"),
            ("Std Dev (σ)", "How much each side varies in move size", "std_upside", "std_downside"),
            ("Z-Score", "Normalize individual bars to see extreme outliers", "zscore_upside", "zscore_downside"),
            ("Skewness", "Is one side's tail stronger? (e.g. right tail = breakout upside)", "skew_upside", "skew_downside"),
            ("Kurtosis", "Fat tails? Likelihood of spikes in that direction", "kurt_upside", "kurt_downside"),
            ("Entropy", "Are the moves uniform (high entropy) or focused (low entropy)?", "entropy_upside", "entropy_downside"),
            ("VaR 95/99", "Draw lines for what's statistically extreme for each side", "var95_upside", "var95_downside", "var99_upside", "var99_downside"),
            ("Cluster Density", "Where do most moves concentrate (your reversion gravity)?", "cluster_upside", "cluster_downside")
        ]

        # Create metric rows
        self.metric_labels = {}
        for metric_info in self.metrics_data:
            metric_name = metric_info[0]
            description = metric_info[1]

            # Create metric row frame
            metric_frame = QtWidgets.QFrame()
            metric_frame.setFrameStyle(QtWidgets.QFrame.Shape.StyledPanel | QtWidgets.QFrame.Shadow.Raised)
            metric_frame.setStyleSheet("""
                QFrame {
                    background-color: #2d2d2d;
                    border: 1px solid #3e3e3e;
                    border-radius: 8px;
                    padding: 10px;
                    margin: 2px;
                }
            """)

            metric_layout = QtWidgets.QHBoxLayout(metric_frame)
            metric_layout.setContentsMargins(15, 10, 15, 10)

            # Metric name and description
            info_layout = QtWidgets.QVBoxLayout()

            name_label = QtWidgets.QLabel(metric_name)
            name_label.setStyleSheet("""
                QLabel {
                    color: #e0e0e0;
                    font-size: 14pt;
                    font-weight: bold;
                }
            """)
            info_layout.addWidget(name_label)

            desc_label = QtWidgets.QLabel(description)
            desc_label.setStyleSheet("""
                QLabel {
                    color: #b0b0b0;
                    font-size: 11pt;
                    font-style: italic;
                }
            """)
            desc_label.setWordWrap(True)
            info_layout.addWidget(desc_label)

            metric_layout.addLayout(info_layout, 3)  # 60% of space

            # Values section
            values_layout = QtWidgets.QHBoxLayout()
            values_layout.setSpacing(20)

            # Upside values
            upside_layout = QtWidgets.QVBoxLayout()
            upside_title = QtWidgets.QLabel("Upside")
            upside_title.setStyleSheet("""
                QLabel {
                    color: #4CAF50;
                    font-size: 12pt;
                    font-weight: bold;
                    text-align: center;
                }
            """)
            upside_title.setAlignment(QtCore.Qt.AlignmentFlag.AlignCenter)
            upside_layout.addWidget(upside_title)

            # Create upside value labels based on metric type
            if metric_name == "VaR 95/99":
                upside_95_label = QtWidgets.QLabel("95%: --")
                upside_99_label = QtWidgets.QLabel("99%: --")
                for label in [upside_95_label, upside_99_label]:
                    label.setStyleSheet("""
                        QLabel {
                            color: #4CAF50;
                            font-size: 11pt;
                            text-align: center;
                        }
                    """)
                    label.setAlignment(QtCore.Qt.AlignmentFlag.AlignCenter)
                    upside_layout.addWidget(label)
                self.metric_labels[metric_info[2]] = upside_95_label  # var95_upside
                self.metric_labels[metric_info[4]] = upside_99_label  # var99_upside
            else:
                upside_value = QtWidgets.QLabel("--")
                upside_value.setStyleSheet("""
                    QLabel {
                        color: #4CAF50;
                        font-size: 11pt;
                        text-align: center;
                    }
                """)
                upside_value.setAlignment(QtCore.Qt.AlignmentFlag.AlignCenter)
                upside_layout.addWidget(upside_value)
                self.metric_labels[metric_info[2]] = upside_value

            values_layout.addLayout(upside_layout)

            # Downside values
            downside_layout = QtWidgets.QVBoxLayout()
            downside_title = QtWidgets.QLabel("Downside")
            downside_title.setStyleSheet("""
                QLabel {
                    color: #F44336;
                    font-size: 12pt;
                    font-weight: bold;
                    text-align: center;
                }
            """)
            downside_title.setAlignment(QtCore.Qt.AlignmentFlag.AlignCenter)
            downside_layout.addWidget(downside_title)

            # Create downside value labels based on metric type
            if metric_name == "VaR 95/99":
                downside_95_label = QtWidgets.QLabel("95%: --")
                downside_99_label = QtWidgets.QLabel("99%: --")
                for label in [downside_95_label, downside_99_label]:
                    label.setStyleSheet("""
                        QLabel {
                            color: #F44336;
                            font-size: 11pt;
                            text-align: center;
                        }
                    """)
                    label.setAlignment(QtCore.Qt.AlignmentFlag.AlignCenter)
                    downside_layout.addWidget(label)
                self.metric_labels[metric_info[3]] = downside_95_label  # var95_downside
                self.metric_labels[metric_info[5]] = downside_99_label  # var99_downside
            else:
                downside_value = QtWidgets.QLabel("--")
                downside_value.setStyleSheet("""
                    QLabel {
                        color: #F44336;
                        font-size: 11pt;
                        text-align: center;
                    }
                """)
                downside_value.setAlignment(QtCore.Qt.AlignmentFlag.AlignCenter)
                downside_layout.addWidget(downside_value)
                self.metric_labels[metric_info[3]] = downside_value

            values_layout.addLayout(downside_layout)

            metric_layout.addLayout(values_layout, 2)  # 40% of space

            metrics_layout.addWidget(metric_frame)

        scroll_area.setWidget(metrics_widget)
        layout.addWidget(scroll_area)

    def update_range_data_metrics(self):
        """Update the Range Data metrics based on current data"""
        try:
            # Get market odds tab to access data
            market_odds_tab = self.get_current_market_odds_tab()
            if not market_odds_tab or not hasattr(market_odds_tab, 'current_pivot'):
                logger.debug("Market odds tab or pivot price not available for range data.")
                self._clear_range_data_metrics()
                return

            pivot_price = market_odds_tab.current_pivot
            if pivot_price is None:
                logger.debug("Pivot price is None for range data.")
                self._clear_range_data_metrics()
                return

            # Get market data
            if not hasattr(market_odds_tab, 'data') or market_odds_tab.data is None or market_odds_tab.data.empty:
                logger.debug("No market data available for range data.")
                self._clear_range_data_metrics()
                return

            market_data = market_odds_tab.data

            # Use all available data (no need to add extra candle here since we use all data)
            recent_data = market_data.copy()

            # Check if split sort mode is enabled for Range Data
            use_split_sort = self.range_data_split_sort_checkbox.isChecked()

            # Get the selected rebase reference mode
            rebase_mode = self.rebase_reference_selector.currentText()

            # Create rebased data based on selected mode
            rebased_data = []
            # Skip the first two candles (index 0 and 1) - they are used for distance calculations but not displayed
            # The first 2 rows are loaded but not used in the system, they are invisible
            # The category starts at the 3rd row since the first 2 rows are NOT used anywhere
            # These 2 rows will be used for distance calculations so it does not START AT 0
            for i, (date, row) in enumerate(recent_data.iterrows()):
                if i < 2:
                    # Skip the first two candles - they're used for distance calculations but not displayed
                    continue

                # Calculate reference price based on selected mode
                if rebase_mode == "Current Price":
                    # Current Price mode: dynamic rebasing - each candle uses the previous candle's close as reference (EXACT SAME AS PREVIOUS CANDLE CLOSE MODE)
                    reference_price = recent_data.iloc[recent_data.index.get_loc(date) - 1]['Close']
                else:
                    # Previous Candle Close mode: dynamic rebasing - each candle uses the previous candle's close as reference
                    reference_price = recent_data.iloc[recent_data.index.get_loc(date) - 1]['Close']

                # Calculate percentage changes
                open_pct = ((row['Open'] / reference_price) - 1) * 100
                high_pct = ((row['High'] / reference_price) - 1) * 100
                low_pct = ((row['Low'] / reference_price) - 1) * 100
                close_pct = ((row['Close'] / reference_price) - 1) * 100

                # Store with sequential index for processing (adjusted for skipped first two candles)
                rebased_data.append([float(i-2), open_pct, high_pct, low_pct, close_pct])

            # For "Current Price" mode, adjust all values so the latest candle's close becomes 0%
            if rebase_mode == "Current Price" and rebased_data:
                # Get the latest candle's close percentage
                latest_close_pct = rebased_data[-1][4]  # close_pct is at index 4

                # Adjust all candles by subtracting the latest close percentage
                for candle in rebased_data:
                    candle[1] -= latest_close_pct  # open_pct
                    candle[2] -= latest_close_pct  # high_pct
                    candle[3] -= latest_close_pct  # low_pct
                    candle[4] -= latest_close_pct  # close_pct

                logger.debug(f"Range Data: Adjusted all values by -{latest_close_pct:.2f}% to make latest candle close = 0%")

            upside_values = []
            downside_values = []

            if use_split_sort:
                # Use split sort logic - same as in YBM chart
                above_pivot_candles = []
                below_pivot_candles = []

                # Process each candle and cut it at the 0% line
                for candle in rebased_data:
                    x_pos, open_pct, high_pct, low_pct, close_pct = candle

                    # Determine original candle color using normal candlestick logic
                    original_is_bullish = close_pct >= open_pct
                    original_color = self.chart_colors['bullish'] if original_is_bullish else self.chart_colors['bearish']

                    # Create above 0% portion if any part of candle is above 0%
                    if high_pct > 0:
                        cut_low = max(low_pct, 0)
                        cut_open = max(open_pct, 0) if open_pct > 0 else 0
                        cut_close = max(close_pct, 0) if close_pct > 0 else 0
                        above_candle = [x_pos, cut_open, high_pct, cut_low, cut_close, original_color, original_is_bullish]
                        above_pivot_candles.append(above_candle)

                    # Create below 0% portion if any part of candle is below 0%
                    if low_pct < 0:
                        cut_high = min(high_pct, 0)
                        cut_open = min(open_pct, 0) if open_pct < 0 else 0
                        cut_close = min(close_pct, 0) if close_pct < 0 else 0
                        below_candle = [x_pos, cut_open, cut_high, low_pct, cut_close, original_color, original_is_bullish]
                        below_pivot_candles.append(below_candle)

                # Extract ranges from cut candles for statistical analysis
                for candle in above_pivot_candles:
                    high_pct = candle[2]
                    cut_low = candle[3]
                    cut_range = high_pct - cut_low
                    if cut_range > 0:
                        upside_values.append(cut_range)

                for candle in below_pivot_candles:
                    cut_high = candle[2]
                    low_pct = candle[3]
                    cut_range = cut_high - low_pct  # This will be positive
                    if cut_range > 0:
                        downside_values.append(cut_range)

            else:
                # Use normal mode logic - full candle moves
                for candle in rebased_data:
                    _, _, high_pct, low_pct, _ = candle

                    # Collect upside and downside moves
                    if high_pct > 0:
                        upside_values.append(high_pct)
                    if low_pct < 0:
                        downside_values.append(abs(low_pct))  # Use absolute value for downside

            # Update title to reflect current mode
            self._update_range_data_title(use_split_sort)

            # Calculate metrics for both sides
            self._calculate_and_display_metrics(upside_values, downside_values, use_split_sort)

        except Exception as e:
            logger.debug(f"Error updating range data metrics: {e}")
            traceback.print_exc()
            self._clear_range_data_metrics()

    def _calculate_and_display_metrics(self, upside_values, downside_values, is_split_mode=False):
        """Calculate and display statistical metrics for upside and downside moves"""
        try:
            # Calculate metrics for upside
            if upside_values and len(upside_values) > 1:
                upside_array = np.array(upside_values)

                # Basic statistics
                mean_up = np.mean(upside_array)
                std_up = np.std(upside_array, ddof=1)
                skew_up = stats.skew(upside_array)
                kurt_up = stats.kurtosis(upside_array)

                # Z-scores (show range or individual values based on user preference)
                z_scores_up = (upside_array - mean_up) / std_up if std_up > 0 else np.zeros_like(upside_array)
                if self.show_zscore_range_checkbox.isChecked():
                    zscore_range_up = f"{np.min(z_scores_up):.2f} to {np.max(z_scores_up):.2f}"
                else:
                    zscore_range_up = f"σ={np.std(z_scores_up):.2f}"

                # Entropy (using user-selected bins)
                entropy_bins_text = self.entropy_bins_selector.currentText()
                if entropy_bins_text == "Auto":
                    entropy_bins = min(10, len(upside_array)//2 + 1)
                else:
                    entropy_bins = int(entropy_bins_text)

                hist, _ = np.histogram(upside_array, bins=entropy_bins)
                prob = hist / np.sum(hist)
                prob = prob[prob > 0]  # Remove zero probabilities
                entropy_up = -np.sum(prob * np.log2(prob))

                # VaR (Value at Risk) - user-input percentiles
                var_level_1, var_level_2 = self._get_var_levels()
                var95_up = np.percentile(upside_array, var_level_1)
                var99_up = np.percentile(upside_array, var_level_2)

                # Cluster density (mode approximation using most frequent bin)
                cluster_bins = entropy_bins  # Use same bins as entropy for consistency
                hist, bin_edges = np.histogram(upside_array, bins=cluster_bins)
                max_bin_idx = np.argmax(hist)
                cluster_up = (bin_edges[max_bin_idx] + bin_edges[max_bin_idx + 1]) / 2

            else:
                mean_up = std_up = skew_up = kurt_up = entropy_up = var95_up = var99_up = cluster_up = None
                zscore_range_up = "--"

            # Calculate metrics for downside
            if downside_values and len(downside_values) > 1:
                downside_array = np.array(downside_values)

                # Basic statistics
                mean_down = np.mean(downside_array)
                std_down = np.std(downside_array, ddof=1)
                skew_down = stats.skew(downside_array)
                kurt_down = stats.kurtosis(downside_array)

                # Z-scores (show range or individual values based on user preference)
                z_scores_down = (downside_array - mean_down) / std_down if std_down > 0 else np.zeros_like(downside_array)
                if self.show_zscore_range_checkbox.isChecked():
                    zscore_range_down = f"{np.min(z_scores_down):.2f} to {np.max(z_scores_down):.2f}"
                else:
                    zscore_range_down = f"σ={np.std(z_scores_down):.2f}"

                # Entropy (using user-selected bins)
                entropy_bins_text = self.entropy_bins_selector.currentText()
                if entropy_bins_text == "Auto":
                    entropy_bins = min(10, len(downside_array)//2 + 1)
                else:
                    entropy_bins = int(entropy_bins_text)

                hist, _ = np.histogram(downside_array, bins=entropy_bins)
                prob = hist / np.sum(hist)
                prob = prob[prob > 0]
                entropy_down = -np.sum(prob * np.log2(prob))

                # VaR (using user-input percentiles)
                var_level_1, var_level_2 = self._get_var_levels()
                var95_down = np.percentile(downside_array, var_level_1)
                var99_down = np.percentile(downside_array, var_level_2)

                # Cluster density (using same bins as entropy)
                cluster_bins = entropy_bins
                hist, bin_edges = np.histogram(downside_array, bins=cluster_bins)
                max_bin_idx = np.argmax(hist)
                cluster_down = (bin_edges[max_bin_idx] + bin_edges[max_bin_idx + 1]) / 2

            else:
                mean_down = std_down = skew_down = kurt_down = entropy_down = var95_down = var99_down = cluster_down = None
                zscore_range_down = "--"

            # Get precision setting
            precision_text = self.precision_selector.currentText()
            precision = int(precision_text.split()[0])

            # Update the display labels with mode indication and custom precision
            mode_suffix = " [SPLIT]" if is_split_mode else ""

            # Get VaR level labels from input fields
            try:
                var_level_1_value = float(self.var_level_1_input.text())
                var_level_1_text = f"{var_level_1_value:.1f}%"
            except (ValueError, AttributeError):
                var_level_1_text = "95%"

            try:
                var_level_2_value = float(self.var_level_2_input.text())
                var_level_2_text = f"{var_level_2_value:.1f}%"
            except (ValueError, AttributeError):
                var_level_2_text = "99%"

            self._update_metric_label("mean_upside", mean_up, f"%.{precision}f%%{mode_suffix}")
            self._update_metric_label("mean_downside", mean_down, f"%.{precision}f%%{mode_suffix}")

            self._update_metric_label("std_upside", std_up, f"%.{precision}f%%{mode_suffix}")
            self._update_metric_label("std_downside", std_down, f"%.{precision}f%%{mode_suffix}")

            self._update_metric_label("zscore_upside", zscore_range_up, f"%s{mode_suffix}")
            self._update_metric_label("zscore_downside", zscore_range_down, f"%s{mode_suffix}")

            self._update_metric_label("skew_upside", skew_up, f"%.{precision}f{mode_suffix}")
            self._update_metric_label("skew_downside", skew_down, f"%.{precision}f{mode_suffix}")

            self._update_metric_label("kurt_upside", kurt_up, f"%.{precision}f{mode_suffix}")
            self._update_metric_label("kurt_downside", kurt_down, f"%.{precision}f{mode_suffix}")

            self._update_metric_label("entropy_upside", entropy_up, f"%.{precision}f bits{mode_suffix}")
            self._update_metric_label("entropy_downside", entropy_down, f"%.{precision}f bits{mode_suffix}")

            self._update_metric_label("var95_upside", var95_up, f"{var_level_1_text}: %.{precision}f%%{mode_suffix}")
            self._update_metric_label("var95_downside", var95_down, f"{var_level_1_text}: %.{precision}f%%{mode_suffix}")
            self._update_metric_label("var99_upside", var99_up, f"{var_level_2_text}: %.{precision}f%%{mode_suffix}")
            self._update_metric_label("var99_downside", var99_down, f"{var_level_2_text}: %.{precision}f%%{mode_suffix}")

            self._update_metric_label("cluster_upside", cluster_up, f"%.{precision}f%%{mode_suffix}")
            self._update_metric_label("cluster_downside", cluster_down, f"%.{precision}f%%{mode_suffix}")

            # Update maximized window if it's open
            if hasattr(self, 'maximized_window') and self.maximized_window is not None:
                self._sync_maximized_values()

        except Exception as e:
            logger.debug(f"Error calculating range data metrics: {e}")
            traceback.print_exc()

    def _update_metric_label(self, key, value, format_str):
        """Update a specific metric label with formatted value"""
        if key in self.metric_labels:
            if value is not None:
                if isinstance(value, str):
                    # Handle string values (like zscore ranges)
                    if "%s" in format_str:
                        self.metric_labels[key].setText(format_str % value)
                    else:
                        self.metric_labels[key].setText(value)
                else:
                    # Handle numeric values
                    try:
                        self.metric_labels[key].setText(format_str % value)
                    except (TypeError, ValueError):
                        # Fallback if formatting fails
                        self.metric_labels[key].setText(str(value))
            else:
                self.metric_labels[key].setText("--")

    def _clear_range_data_metrics(self):
        """Clear all range data metric values"""
        for key in self.metric_labels:
            self.metric_labels[key].setText("--")

    def _update_range_data_title(self, is_split_mode):
        """Update the Range Data title to reflect the current mode"""
        if is_split_mode:
            title = "Range Data Statistical Metrics - Split Sort Mode"
            self.range_data_title_label.setStyleSheet("""
                QLabel {
                    color: #FFD54F;
                    font-size: 18pt;
                    font-weight: bold;
                    margin-bottom: 10px;
                }
            """)
        else:
            title = "Range Data Statistical Metrics - Normal Mode"
            self.range_data_title_label.setStyleSheet("""
                QLabel {
                    color: #e0e0e0;
                    font-size: 18pt;
                    font-weight: bold;
                    margin-bottom: 10px;
                }
            """)
        self.range_data_title_label.setText(title)

    def _store_original_layout_state(self):
        """Store the original layout state for proper restoration"""
        try:
            # Store the original splitter sizes and visibility states
            self._original_layout_state = {
                'info_panel_visible': self.info_panel.isVisible(),
                'top_charts_visible': self.top_charts_widget.isVisible(),
                'splitter_sizes': self.main_splitter.sizes(),
                'splitter_stretch_factors': [
                    self.main_splitter.stretchFactor(0),
                    self.main_splitter.stretchFactor(1)
                ]
            }
            logger.debug(f"Stored original layout state: {self._original_layout_state}")
        except Exception as e:
            logger.debug(f"Error storing original layout state: {e}")

    def _toggle_expand_bottom_charts(self):
        """Toggle expand/collapse state of bottom chart area"""
        try:
            if not self.is_bottom_expanded:
                self._expand_bottom_charts()
            else:
                self._collapse_bottom_charts()
        except Exception as e:
            logger.debug(f"Error toggling expand bottom charts: {e}")

    def _expand_bottom_charts(self):
        """Expand the bottom chart area to take up more space"""
        try:
            # Store current state if not already stored
            if self._original_layout_state is None:
                self._store_original_layout_state()

            # Hide the top charts widget and info panel
            self.top_charts_widget.setVisible(False)
            self.info_panel.setVisible(False)

            # Set splitter to give all space to bottom section
            total_height = sum(self.main_splitter.sizes())
            self.main_splitter.setSizes([0, total_height])

            # Update button appearance with smaller font for collapse icon
            self.combined_maximize_button.setText("🗗")
            self.combined_maximize_button.setStyleSheet("""
                QPushButton {
                    background-color: #3e3e3e;
                    color: #e0e0e0;
                    border: 1px solid #5e5e5e;
                    border-radius: 4px;
                    font-size: 8pt;
                    font-weight: bold;
                }
                QPushButton:hover {
                    background-color: #4e4e4e;
                    border: 1px solid #6e6e6e;
                }
                QPushButton:pressed {
                    background-color: #2e2e2e;
                }
            """)
            self.combined_maximize_button.setToolTip("Collapse Chart Area")

            self.ybm_maximize_button.setText("🗗")
            self.ybm_maximize_button.setStyleSheet("""
                QPushButton {
                    background-color: #3e3e3e;
                    color: #e0e0e0;
                    border: 1px solid #5e5e5e;
                    border-radius: 4px;
                    font-size: 8pt;
                    font-weight: bold;
                }
                QPushButton:hover {
                    background-color: #4e4e4e;
                    border: 1px solid #6e6e6e;
                }
                QPushButton:pressed {
                    background-color: #2e2e2e;
                }
            """)
            self.ybm_maximize_button.setToolTip("Collapse Chart Area")

            # Set expanded state
            self.is_bottom_expanded = True

            logger.debug("Expanded bottom charts")

        except Exception as e:
            logger.debug(f"Error expanding bottom charts: {e}")

    def _collapse_bottom_charts(self):
        """Collapse the bottom chart area back to normal size"""
        try:
            # Restore the original visibility state
            if self._original_layout_state:
                self.info_panel.setVisible(self._original_layout_state['info_panel_visible'])
                self.top_charts_widget.setVisible(self._original_layout_state['top_charts_visible'])

                # Restore original splitter sizes
                self.main_splitter.setSizes(self._original_layout_state['splitter_sizes'])

                # Restore stretch factors
                stretch_factors = self._original_layout_state['splitter_stretch_factors']
                self.main_splitter.setStretchFactor(0, stretch_factors[0])
                self.main_splitter.setStretchFactor(1, stretch_factors[1])
            else:
                # Fallback to default visibility and proportions
                self.info_panel.setVisible(True)
                self.top_charts_widget.setVisible(True)
                self.main_splitter.setSizes([200, 400])
                self.main_splitter.setStretchFactor(0, 1)
                self.main_splitter.setStretchFactor(1, 2)

            # Update button appearance - restore original styling for expand icon
            self.combined_maximize_button.setText("⛶")
            self.combined_maximize_button.setStyleSheet("""
                QPushButton {
                    background-color: #3e3e3e;
                    color: #e0e0e0;
                    border: 1px solid #5e5e5e;
                    border-radius: 4px;
                    font-size: 10pt;
                    font-weight: bold;
                }
                QPushButton:hover {
                    background-color: #4e4e4e;
                    border: 1px solid #6e6e6e;
                }
                QPushButton:pressed {
                    background-color: #2e2e2e;
                }
            """)
            self.combined_maximize_button.setToolTip("Expand Chart Area")

            self.ybm_maximize_button.setText("⛶")
            self.ybm_maximize_button.setStyleSheet("""
                QPushButton {
                    background-color: #3e3e3e;
                    color: #e0e0e0;
                    border: 1px solid #5e5e5e;
                    border-radius: 4px;
                    font-size: 10pt;
                    font-weight: bold;
                }
                QPushButton:hover {
                    background-color: #4e4e4e;
                    border: 1px solid #6e6e6e;
                }
                QPushButton:pressed {
                    background-color: #2e2e2e;
                }
            """)
            self.ybm_maximize_button.setToolTip("Expand Chart Area")

            # Set collapsed state
            self.is_bottom_expanded = False

            logger.debug("Collapsed bottom charts")

        except Exception as e:
            logger.debug(f"Error collapsing bottom charts: {e}")

    def _toggle_maximize_range_data(self):
        """Toggle maximize/minimize state of Range Data tab"""
        try:
            if not hasattr(self, 'maximized_window') or self.maximized_window is None:
                self._maximize_range_data()
            else:
                self._minimize_range_data()
        except Exception as e:
            logger.debug(f"Error toggling maximize range data: {e}")

    def _maximize_range_data(self):
        """Create and show maximized Range Data window"""
        try:
            # Create maximized window
            self.maximized_window = QtWidgets.QDialog(self)
            self.maximized_window.setWindowTitle("Range Data Statistical Metrics - Maximized")
            self.maximized_window.setModal(False)
            self.maximized_window.resize(1200, 800)

            # Apply dark theme to maximized window
            self.maximized_window.setStyleSheet("""
                QDialog {
                    background-color: #1e1e1e;
                    color: #e0e0e0;
                }
            """)

            # Create layout for maximized window
            max_layout = QtWidgets.QVBoxLayout(self.maximized_window)
            max_layout.setContentsMargins(20, 20, 20, 20)
            max_layout.setSpacing(15)

            # Title bar with minimize button
            max_title_bar = QtWidgets.QHBoxLayout()

            max_title_label = QtWidgets.QLabel("Range Data Statistical Metrics - Maximized")
            max_title_label.setStyleSheet("""
                QLabel {
                    color: #e0e0e0;
                    font-size: 20pt;
                    font-weight: bold;
                    margin-bottom: 10px;
                }
            """)
            max_title_bar.addWidget(max_title_label)

            # Minimize button
            minimize_button = QtWidgets.QPushButton("🗗")  # Minimize icon
            minimize_button.setFixedSize(40, 40)
            minimize_button.setStyleSheet("""
                QPushButton {
                    background-color: #3e3e3e;
                    color: #e0e0e0;
                    border: 1px solid #5e5e5e;
                    border-radius: 6px;
                    font-size: 12pt;
                    font-weight: bold;
                }
                QPushButton:hover {
                    background-color: #4e4e4e;
                    border: 1px solid #6e6e6e;
                }
                QPushButton:pressed {
                    background-color: #2e2e2e;
                }
            """)
            minimize_button.setToolTip("Minimize Range Data Tab")
            minimize_button.clicked.connect(self._minimize_range_data)
            max_title_bar.addWidget(minimize_button)

            max_layout.addLayout(max_title_bar)

            # Clone the control panel with all customization options
            max_control_panel = QtWidgets.QFrame()
            max_control_panel.setFrameStyle(QtWidgets.QFrame.Shape.StyledPanel | QtWidgets.QFrame.Shadow.Raised)
            max_control_panel.setStyleSheet("""
                QFrame {
                    background-color: #2d2d2d;
                    border: 1px solid #3e3e3e;
                    border-radius: 8px;
                    padding: 10px;
                    margin: 5px;
                }
            """)
            max_control_layout = QtWidgets.QVBoxLayout(max_control_panel)
            max_control_layout.setContentsMargins(15, 10, 15, 10)
            max_control_layout.setSpacing(10)

            # First row - Mode and standard deviation customization
            max_first_row = QtWidgets.QHBoxLayout()

            # Clone split sort checkbox
            self.max_range_data_split_sort_checkbox = QtWidgets.QCheckBox("Use Split Sort Mode")
            self.max_range_data_split_sort_checkbox.setStyleSheet("""
                QCheckBox {
                    color: #e0e0e0;
                    font-size: 12pt;
                    font-weight: bold;
                }
                QCheckBox::indicator {
                    width: 18px;
                    height: 18px;
                }
                QCheckBox::indicator:unchecked {
                    background-color: #1e1e1e;
                    border: 2px solid #3e3e3e;
                    border-radius: 3px;
                }
                QCheckBox::indicator:checked {
                    background-color: #4CAF50;
                    border: 2px solid #4CAF50;
                    border-radius: 3px;
                }
            """)
            # Sync with original checkbox
            self.max_range_data_split_sort_checkbox.setChecked(self.range_data_split_sort_checkbox.isChecked())
            self.max_range_data_split_sort_checkbox.stateChanged.connect(self._sync_all_controls)
            max_first_row.addWidget(self.max_range_data_split_sort_checkbox)

            # Standard deviation multipliers
            max_std_label = QtWidgets.QLabel("Std Dev Levels:")
            max_std_label.setStyleSheet("color: #e0e0e0; font-size: 11pt; font-weight: bold;")
            max_first_row.addWidget(max_std_label)

            self.max_std_level_1_input = QtWidgets.QLineEdit(self.std_level_1_input.text())
            self.max_std_level_1_input.setFixedWidth(50)
            self.max_std_level_1_input.setStyleSheet("""
                QLineEdit {
                    background-color: #3e3e3e;
                    color: #e0e0e0;
                    border: 1px solid #5e5e5e;
                    border-radius: 4px;
                    padding: 4px 6px;
                    font-size: 10pt;
                }
                QLineEdit:focus {
                    border: 1px solid #4CAF50;
                }
            """)
            self.max_std_level_1_input.textChanged.connect(self._sync_all_controls)
            max_first_row.addWidget(self.max_std_level_1_input)

            max_std_sigma_1 = QtWidgets.QLabel("σ")
            max_std_sigma_1.setStyleSheet("color: #b0b0b0; font-size: 10pt;")
            max_first_row.addWidget(max_std_sigma_1)

            self.max_std_level_2_input = QtWidgets.QLineEdit(self.std_level_2_input.text())
            self.max_std_level_2_input.setFixedWidth(50)
            self.max_std_level_2_input.setStyleSheet("""
                QLineEdit {
                    background-color: #3e3e3e;
                    color: #e0e0e0;
                    border: 1px solid #5e5e5e;
                    border-radius: 4px;
                    padding: 4px 6px;
                    font-size: 10pt;
                }
                QLineEdit:focus {
                    border: 1px solid #4CAF50;
                }
            """)
            self.max_std_level_2_input.textChanged.connect(self._sync_all_controls)
            max_first_row.addWidget(self.max_std_level_2_input)

            max_std_sigma_2 = QtWidgets.QLabel("σ")
            max_std_sigma_2.setStyleSheet("color: #b0b0b0; font-size: 10pt;")
            max_first_row.addWidget(max_std_sigma_2)

            max_first_row.addStretch()
            max_control_layout.addLayout(max_first_row)

            # Second row - Statistical customizations
            max_second_row = QtWidgets.QHBoxLayout()

            max_var_label = QtWidgets.QLabel("VaR Levels:")
            max_var_label.setStyleSheet("color: #e0e0e0; font-size: 11pt; font-weight: bold;")
            max_second_row.addWidget(max_var_label)

            self.max_var_level_1_input = QtWidgets.QLineEdit(self.var_level_1_input.text())
            self.max_var_level_1_input.setFixedWidth(50)
            self.max_var_level_1_input.setStyleSheet("""
                QLineEdit {
                    background-color: #3e3e3e;
                    color: #e0e0e0;
                    border: 1px solid #5e5e5e;
                    border-radius: 4px;
                    padding: 4px 6px;
                    font-size: 10pt;
                }
                QLineEdit:focus {
                    border: 1px solid #4CAF50;
                }
            """)
            self.max_var_level_1_input.textChanged.connect(self._sync_all_controls)
            max_second_row.addWidget(self.max_var_level_1_input)

            max_var_percent_1 = QtWidgets.QLabel("%")
            max_var_percent_1.setStyleSheet("color: #b0b0b0; font-size: 10pt;")
            max_second_row.addWidget(max_var_percent_1)

            self.max_var_level_2_input = QtWidgets.QLineEdit(self.var_level_2_input.text())
            self.max_var_level_2_input.setFixedWidth(50)
            self.max_var_level_2_input.setStyleSheet("""
                QLineEdit {
                    background-color: #3e3e3e;
                    color: #e0e0e0;
                    border: 1px solid #5e5e5e;
                    border-radius: 4px;
                    padding: 4px 6px;
                    font-size: 10pt;
                }
                QLineEdit:focus {
                    border: 1px solid #4CAF50;
                }
            """)
            self.max_var_level_2_input.textChanged.connect(self._sync_all_controls)
            max_second_row.addWidget(self.max_var_level_2_input)

            max_var_percent_2 = QtWidgets.QLabel("%")
            max_var_percent_2.setStyleSheet("color: #b0b0b0; font-size: 10pt;")
            max_second_row.addWidget(max_var_percent_2)

            max_entropy_label = QtWidgets.QLabel("Entropy Bins:")
            max_entropy_label.setStyleSheet("color: #e0e0e0; font-size: 11pt; font-weight: bold;")
            max_second_row.addWidget(max_entropy_label)

            self.max_entropy_bins_selector = QtWidgets.QComboBox()
            self.max_entropy_bins_selector.addItems(["5", "10", "15", "20", "Auto"])
            self.max_entropy_bins_selector.setCurrentText(self.entropy_bins_selector.currentText())
            self.max_entropy_bins_selector.setStyleSheet("""
                QComboBox {
                    background-color: #3e3e3e;
                    color: #e0e0e0;
                    border: 1px solid #5e5e5e;
                    border-radius: 4px;
                    padding: 4px 8px;
                    font-size: 10pt;
                    min-width: 50px;
                }
                QComboBox QAbstractItemView {
                    background-color: #3e3e3e;
                    color: #e0e0e0;
                    selection-background-color: #4e4e4e;
                    border: 1px solid #5e5e5e;
                }
            """)
            self.max_entropy_bins_selector.currentTextChanged.connect(self._sync_all_controls)
            max_second_row.addWidget(self.max_entropy_bins_selector)

            max_second_row.addStretch()
            max_control_layout.addLayout(max_second_row)

            # Third row - Display options
            max_third_row = QtWidgets.QHBoxLayout()

            max_precision_label = QtWidgets.QLabel("Precision:")
            max_precision_label.setStyleSheet("color: #e0e0e0; font-size: 11pt; font-weight: bold;")
            max_third_row.addWidget(max_precision_label)

            self.max_precision_selector = QtWidgets.QComboBox()
            self.max_precision_selector.addItems(["1 decimal", "2 decimals", "3 decimals", "4 decimals"])
            self.max_precision_selector.setCurrentText(self.precision_selector.currentText())
            self.max_precision_selector.setStyleSheet("""
                QComboBox {
                    background-color: #3e3e3e;
                    color: #e0e0e0;
                    border: 1px solid #5e5e5e;
                    border-radius: 4px;
                    padding: 4px 8px;
                    font-size: 10pt;
                    min-width: 80px;
                }
                QComboBox QAbstractItemView {
                    background-color: #3e3e3e;
                    color: #e0e0e0;
                    selection-background-color: #4e4e4e;
                    border: 1px solid #5e5e5e;
                }
            """)
            self.max_precision_selector.currentTextChanged.connect(self._sync_all_controls)
            max_third_row.addWidget(self.max_precision_selector)

            self.max_show_zscore_range_checkbox = QtWidgets.QCheckBox("Show Z-Score Range")
            self.max_show_zscore_range_checkbox.setStyleSheet("""
                QCheckBox {
                    color: #e0e0e0;
                    font-size: 11pt;
                }
                QCheckBox::indicator {
                    width: 16px;
                    height: 16px;
                }
                QCheckBox::indicator:unchecked {
                    background-color: #1e1e1e;
                    border: 2px solid #3e3e3e;
                    border-radius: 3px;
                }
                QCheckBox::indicator:checked {
                    background-color: #4CAF50;
                    border: 2px solid #4CAF50;
                    border-radius: 3px;
                }
            """)
            self.max_show_zscore_range_checkbox.setChecked(self.show_zscore_range_checkbox.isChecked())
            self.max_show_zscore_range_checkbox.stateChanged.connect(self._sync_all_controls)
            max_third_row.addWidget(self.max_show_zscore_range_checkbox)

            max_third_row.addStretch()
            max_control_layout.addLayout(max_third_row)

            # Info label
            max_info_label = QtWidgets.QLabel("Normal: Full candle moves | Split: Cut candle ranges")
            max_info_label.setStyleSheet("""
                QLabel {
                    color: #b0b0b0;
                    font-size: 10pt;
                    font-style: italic;
                    text-align: center;
                }
            """)
            max_info_label.setAlignment(QtCore.Qt.AlignmentFlag.AlignCenter)
            max_control_layout.addWidget(max_info_label)

            max_layout.addWidget(max_control_panel)

            # Clone the metrics display with larger sizing
            max_scroll_area = QtWidgets.QScrollArea()
            max_scroll_area.setWidgetResizable(True)
            max_scroll_area.setStyleSheet("""
                QScrollArea {
                    background-color: #1e1e1e;
                    border: none;
                }
                QScrollBar:vertical {
                    background-color: #2d2d2d;
                    width: 12px;
                    border-radius: 6px;
                }
                QScrollBar::handle:vertical {
                    background-color: #4d4d4d;
                    border-radius: 6px;
                    min-height: 20px;
                }
                QScrollBar::handle:vertical:hover {
                    background-color: #5d5d5d;
                }
            """)

            # Create maximized metrics widget
            max_metrics_widget = QtWidgets.QWidget()
            max_metrics_layout = QtWidgets.QVBoxLayout(max_metrics_widget)
            max_metrics_layout.setSpacing(15)

            # Store reference to maximized metric labels
            self.max_metric_labels = {}

            # Create larger metric rows for maximized view
            for metric_info in self.metrics_data:
                metric_name = metric_info[0]
                description = metric_info[1]

                # Create metric row frame with larger sizing
                metric_frame = QtWidgets.QFrame()
                metric_frame.setFrameStyle(QtWidgets.QFrame.Shape.StyledPanel | QtWidgets.QFrame.Shadow.Raised)
                metric_frame.setStyleSheet("""
                    QFrame {
                        background-color: #2d2d2d;
                        border: 1px solid #3e3e3e;
                        border-radius: 8px;
                        padding: 15px;
                        margin: 3px;
                    }
                """)

                metric_layout = QtWidgets.QHBoxLayout(metric_frame)
                metric_layout.setContentsMargins(20, 15, 20, 15)

                # Metric name and description with larger fonts
                info_layout = QtWidgets.QVBoxLayout()

                name_label = QtWidgets.QLabel(metric_name)
                name_label.setStyleSheet("""
                    QLabel {
                        color: #e0e0e0;
                        font-size: 16pt;
                        font-weight: bold;
                    }
                """)
                info_layout.addWidget(name_label)

                desc_label = QtWidgets.QLabel(description)
                desc_label.setStyleSheet("""
                    QLabel {
                        color: #b0b0b0;
                        font-size: 12pt;
                        font-style: italic;
                    }
                """)
                desc_label.setWordWrap(True)
                info_layout.addWidget(desc_label)

                metric_layout.addLayout(info_layout, 3)

                # Values section with larger fonts
                values_layout = QtWidgets.QHBoxLayout()
                values_layout.setSpacing(30)

                # Upside values
                upside_layout = QtWidgets.QVBoxLayout()
                upside_title = QtWidgets.QLabel("Upside")
                upside_title.setStyleSheet("""
                    QLabel {
                        color: #4CAF50;
                        font-size: 14pt;
                        font-weight: bold;
                        text-align: center;
                    }
                """)
                upside_title.setAlignment(QtCore.Qt.AlignmentFlag.AlignCenter)
                upside_layout.addWidget(upside_title)

                # Create upside value labels
                if metric_name == "VaR 95/99":
                    upside_95_label = QtWidgets.QLabel("95%: --")
                    upside_99_label = QtWidgets.QLabel("99%: --")
                    for label in [upside_95_label, upside_99_label]:
                        label.setStyleSheet("""
                            QLabel {
                                color: #4CAF50;
                                font-size: 13pt;
                                text-align: center;
                                font-weight: bold;
                            }
                        """)
                        label.setAlignment(QtCore.Qt.AlignmentFlag.AlignCenter)
                        upside_layout.addWidget(label)
                    self.max_metric_labels[metric_info[2]] = upside_95_label
                    self.max_metric_labels[metric_info[4]] = upside_99_label
                else:
                    upside_value = QtWidgets.QLabel("--")
                    upside_value.setStyleSheet("""
                        QLabel {
                            color: #4CAF50;
                            font-size: 13pt;
                            text-align: center;
                            font-weight: bold;
                        }
                    """)
                    upside_value.setAlignment(QtCore.Qt.AlignmentFlag.AlignCenter)
                    upside_layout.addWidget(upside_value)
                    self.max_metric_labels[metric_info[2]] = upside_value

                values_layout.addLayout(upside_layout)

                # Downside values
                downside_layout = QtWidgets.QVBoxLayout()
                downside_title = QtWidgets.QLabel("Downside")
                downside_title.setStyleSheet("""
                    QLabel {
                        color: #F44336;
                        font-size: 14pt;
                        font-weight: bold;
                        text-align: center;
                    }
                """)
                downside_title.setAlignment(QtCore.Qt.AlignmentFlag.AlignCenter)
                downside_layout.addWidget(downside_title)

                # Create downside value labels
                if metric_name == "VaR 95/99":
                    downside_95_label = QtWidgets.QLabel("95%: --")
                    downside_99_label = QtWidgets.QLabel("99%: --")
                    for label in [downside_95_label, downside_99_label]:
                        label.setStyleSheet("""
                            QLabel {
                                color: #F44336;
                                font-size: 13pt;
                                text-align: center;
                                font-weight: bold;
                            }
                        """)
                        label.setAlignment(QtCore.Qt.AlignmentFlag.AlignCenter)
                        downside_layout.addWidget(label)
                    self.max_metric_labels[metric_info[3]] = downside_95_label
                    self.max_metric_labels[metric_info[5]] = downside_99_label
                else:
                    downside_value = QtWidgets.QLabel("--")
                    downside_value.setStyleSheet("""
                        QLabel {
                            color: #F44336;
                            font-size: 13pt;
                            text-align: center;
                            font-weight: bold;
                        }
                    """)
                    downside_value.setAlignment(QtCore.Qt.AlignmentFlag.AlignCenter)
                    downside_layout.addWidget(downside_value)
                    self.max_metric_labels[metric_info[3]] = downside_value

                values_layout.addLayout(downside_layout)
                metric_layout.addLayout(values_layout, 2)
                max_metrics_layout.addWidget(metric_frame)

            max_scroll_area.setWidget(max_metrics_widget)
            max_layout.addWidget(max_scroll_area)

            # Update maximize button with smaller font for collapse icon
            self.maximize_button.setText("🗗")
            self.maximize_button.setStyleSheet("""
                QPushButton {
                    background-color: #3e3e3e;
                    color: #e0e0e0;
                    border: 1px solid #5e5e5e;
                    border-radius: 6px;
                    font-size: 10pt;
                    font-weight: bold;
                }
                QPushButton:hover {
                    background-color: #4e4e4e;
                    border: 1px solid #6e6e6e;
                }
                QPushButton:pressed {
                    background-color: #2e2e2e;
                }
            """)
            self.maximize_button.setToolTip("Minimize Range Data Tab")

            # Show maximized window
            self.maximized_window.show()

            # Copy current values to maximized display
            self._sync_maximized_values()

            # Connect window close event
            self.maximized_window.closeEvent = self._on_maximized_window_close

        except Exception as e:
            logger.debug(f"Error maximizing range data: {e}")

    def _minimize_range_data(self):
        """Close maximized Range Data window"""
        try:
            if hasattr(self, 'maximized_window') and self.maximized_window is not None:
                self.maximized_window.close()
                self.maximized_window = None

                # Update maximize button - restore original styling for expand icon
                self.maximize_button.setText("⛶")
                self.maximize_button.setStyleSheet("""
                    QPushButton {
                        background-color: #3e3e3e;
                        color: #e0e0e0;
                        border: 1px solid #5e5e5e;
                        border-radius: 6px;
                        font-size: 12pt;
                        font-weight: bold;
                    }
                    QPushButton:hover {
                        background-color: #4e4e4e;
                        border: 1px solid #6e6e6e;
                    }
                    QPushButton:pressed {
                        background-color: #2e2e2e;
                    }
                """)
                self.maximize_button.setToolTip("Maximize Range Data Tab")

        except Exception as e:
            logger.debug(f"Error minimizing range data: {e}")

    def _on_maximized_window_close(self, event):
        """Handle maximized window close event"""
        self._minimize_range_data()
        event.accept()

    def _sync_split_sort_checkboxes(self):
        """Sync split sort checkbox states between normal and maximized views"""
        try:
            if hasattr(self, 'max_range_data_split_sort_checkbox'):
                # Sync from maximized to normal
                self.range_data_split_sort_checkbox.setChecked(
                    self.max_range_data_split_sort_checkbox.isChecked()
                )
                # Update metrics
                self.update_range_data_metrics()
        except Exception as e:
            logger.debug(f"Error syncing split sort checkboxes: {e}")

    def _sync_all_controls(self):
        """Sync all control states between normal and maximized views"""
        try:
            if hasattr(self, 'max_range_data_split_sort_checkbox'):
                # Sync from maximized to normal
                self.range_data_split_sort_checkbox.setChecked(
                    self.max_range_data_split_sort_checkbox.isChecked()
                )
                self.std_level_1_input.setText(
                    self.max_std_level_1_input.text()
                )
                self.std_level_2_input.setText(
                    self.max_std_level_2_input.text()
                )
                self.var_level_1_input.setText(
                    self.max_var_level_1_input.text()
                )
                self.var_level_2_input.setText(
                    self.max_var_level_2_input.text()
                )
                self.entropy_bins_selector.setCurrentText(
                    self.max_entropy_bins_selector.currentText()
                )
                self.precision_selector.setCurrentText(
                    self.max_precision_selector.currentText()
                )
                self.show_zscore_range_checkbox.setChecked(
                    self.max_show_zscore_range_checkbox.isChecked()
                )
                # Update metrics
                self.update_range_data_metrics()
        except Exception as e:
            logger.debug(f"Error syncing all controls: {e}")

    def _sync_maximized_values(self):
        """Copy current metric values to maximized display"""
        try:
            if hasattr(self, 'max_metric_labels'):
                for key, label in self.metric_labels.items():
                    if key in self.max_metric_labels:
                        self.max_metric_labels[key].setText(label.text())
        except Exception as e:
            logger.debug(f"Error syncing maximized values: {e}")

    def _is_stat_enabled(self, stat_name):
        """Check if a specific statistic is enabled for display"""
        if stat_name in self.stat_toggles:
            return self.stat_toggles[stat_name].isChecked()
        return True  # Default to enabled if toggle doesn't exist

    def _get_var_levels(self):
        """Get VaR levels from Range Data tab inputs"""
        try:
            var_level_1 = float(self.var_level_1_input.text())
            var_level_1 = max(0, min(100, var_level_1))  # Clamp between 0-100
        except (ValueError, AttributeError):
            var_level_1 = 95  # Default fallback

        try:
            var_level_2 = float(self.var_level_2_input.text())
            var_level_2 = max(0, min(100, var_level_2))  # Clamp between 0-100
        except (ValueError, AttributeError):
            var_level_2 = 99  # Default fallback

        return var_level_1, var_level_2

    def _get_std_levels(self):
        """Get standard deviation levels from Range Data tab inputs"""
        try:
            std_level_1 = float(self.std_level_1_input.text())
            std_level_1 = max(0, std_level_1)  # Must be positive
        except (ValueError, AttributeError):
            std_level_1 = 1.0  # Default fallback

        try:
            std_level_2 = float(self.std_level_2_input.text())
            std_level_2 = max(0, std_level_2)  # Must be positive
        except (ValueError, AttributeError):
            std_level_2 = 2.0  # Default fallback

        return std_level_1, std_level_2

    def _get_entropy_bins(self):
        """Get entropy bins setting from Range Data tab"""
        try:
            entropy_bins_text = self.entropy_bins_selector.currentText()
            if entropy_bins_text == "Auto":
                return "Auto"
            else:
                return int(entropy_bins_text)
        except (ValueError, AttributeError):
            return "Auto"  # Default fallback

    def _add_statistical_overlays_to_chart(self, upside_values, downside_values):
        """Add statistical metric overlays to the YBM chart"""
        try:
            if not upside_values and not downside_values:
                return

            # Calculate statistical metrics for overlays
            upside_stats = self._calculate_stats_for_overlay(upside_values) if upside_values else None
            downside_stats = self._calculate_stats_for_overlay([abs(v) for v in downside_values]) if downside_values else None

            # Get chart range for positioning
            if hasattr(self, 'ybm_chart') and self.ybm_chart.plotItem.vb.viewRange():
                x_range = self.ybm_chart.plotItem.vb.viewRange()[0]
                x_min, x_max = x_range[0], x_range[1]
            else:
                x_min, x_max = -2, 10  # Default range

            # Add upside statistical lines
            if upside_stats:
                self._add_stat_lines_to_chart(upside_stats, x_min, x_max, 'upside')

            # Add downside statistical lines (convert back to negative values)
            if downside_stats:
                # Convert positive downside stats back to negative for display
                downside_stats_negative = {k: -v if v is not None else None for k, v in downside_stats.items()}
                self._add_stat_lines_to_chart(downside_stats_negative, x_min, x_max, 'downside')

        except Exception as e:
            logger.debug(f"Error adding statistical overlays: {e}")

    def _calculate_stats_for_overlay(self, values):
        """Calculate statistical metrics for chart overlay"""
        if not values or len(values) < 2:
            return None

        try:
            values_array = np.array(values)

            # Get VaR levels from Range Data tab
            var_level_1, var_level_2 = self._get_var_levels()

            stats_dict = {
                'mean': np.mean(values_array),
                'std': np.std(values_array, ddof=1),
                'var95': np.percentile(values_array, var_level_1),
                'var99': np.percentile(values_array, var_level_2),
                'skewness': stats.skew(values_array),
                'kurtosis': stats.kurtosis(values_array)
            }

            # Calculate cluster density using Range Data tab entropy bins
            entropy_bins = self._get_entropy_bins()
            if entropy_bins == "Auto":
                bins = min(10, len(values_array)//2 + 1)
            else:
                bins = entropy_bins

            hist, bin_edges = np.histogram(values_array, bins=bins)
            max_bin_idx = np.argmax(hist)
            stats_dict['cluster'] = (bin_edges[max_bin_idx] + bin_edges[max_bin_idx + 1]) / 2

            return stats_dict

        except Exception as e:
            logger.debug(f"Error calculating stats for overlay: {e}")
            return None

    def _add_stat_lines_to_chart(self, stats_dict, x_min, x_max, side):
        """Add statistical lines to the chart"""
        try:
            if not stats_dict:
                return

            # Color scheme for different metrics
            colors = {
                'mean': '#FFC107',      # Amber
                'std': '#9C27B0',       # Purple
                'var95': '#FF5722',     # Deep Orange
                'var99': '#E91E63',     # Pink
                'cluster': '#00BCD4'    # Cyan
            }

            # Line styles for different metrics
            line_styles = {
                'mean': QtCore.Qt.PenStyle.SolidLine,
                'std': QtCore.Qt.PenStyle.DashLine,
                'var95': QtCore.Qt.PenStyle.DotLine,
                'var99': QtCore.Qt.PenStyle.DashDotLine,
                'cluster': QtCore.Qt.PenStyle.DashDotDotLine
            }

            # Add lines for each metric (only if enabled)
            for metric, value in stats_dict.items():
                if value is not None and metric in colors and self._is_stat_enabled(metric):
                    # Create the line
                    line = pg.InfiniteLine(
                        pos=value,
                        angle=0,
                        pen=pg.mkPen(colors[metric], width=2, style=line_styles[metric])
                    )
                    self.ybm_chart.addItem(line)

                    # Add label for the line with custom VaR levels
                    label_text = f"{metric.upper()}: {value:.2f}%"
                    if metric == 'var95':
                        var_level_1, _ = self._get_var_levels()
                        label_text = f"VaR{var_level_1:.1f}%: {value:.2f}%"
                    elif metric == 'var99':
                        _, var_level_2 = self._get_var_levels()
                        label_text = f"VaR{var_level_2:.1f}%: {value:.2f}%"
                    elif metric == 'cluster':
                        label_text = f"Cluster: {value:.2f}%"

                    # Position label on the right side of the chart
                    label = pg.TextItem(
                        text=label_text,
                        color=colors[metric],
                        anchor=(0, 0.5)
                    )
                    label.setPos(x_max + 0.5, value)
                    self.ybm_chart.addItem(label)

            # Standard deviation is already handled by the main std line above
            # No additional lines needed - just the main std dev line without shaded bands

        except Exception as e:
            logger.debug(f"Error adding stat lines to chart: {e}")

    def _add_statistics_legend(self, is_split_mode=False):
        """Add a floating legend in the top right corner that follows the screen"""
        try:
            # Store reference to remove old legend if it exists
            if hasattr(self, '_statistics_legend') and self._statistics_legend is not None:
                self.ybm_chart.removeItem(self._statistics_legend)

            # Get custom VaR levels for legend
            var_level_1, var_level_2 = self._get_var_levels()

            # Create different legend text based on mode and enabled statistics
            if is_split_mode:
                legend_lines = ["<b>Split Mode Statistics:</b><br/>"]
                colors = {'mean': '#FFD54F', 'std': '#BA68C8', 'var95': '#FF8A65', 'var99': '#F06292', 'cluster': '#4DD0E1'}
                labels = {'mean': 'Mean Cut Range', 'std': 'Std Dev (σ)', 'var95': f'VaR {var_level_1:.1f}%', 'var99': f'VaR {var_level_2:.1f}%', 'cluster': 'Cluster Density'}
                symbols = {'mean': '━━━', 'std': '┅┅┅', 'var95': '┈┈┈', 'var99': '┉┉┉', 'cluster': '┄┄┄'}

                for stat in ['mean', 'std', 'var95', 'var99', 'cluster']:
                    if self._is_stat_enabled(stat):
                        legend_lines.append(f'<span style="color:{colors[stat]};">{symbols[stat]}</span> {labels[stat]}<br/>')

                legend_lines.append('<small><i>Based on cut candle ranges</i></small>')
            else:
                legend_lines = ["<b>Normal Mode Statistics:</b><br/>"]
                colors = {'mean': '#FFC107', 'std': '#9C27B0', 'var95': '#FF5722', 'var99': '#E91E63', 'cluster': '#00BCD4'}
                labels = {'mean': 'Mean (μ)', 'std': 'Std Dev (σ)', 'var95': f'VaR {var_level_1:.1f}%', 'var99': f'VaR {var_level_2:.1f}%', 'cluster': 'Cluster Density'}
                symbols = {'mean': '━━━', 'std': '┅┅┅', 'var95': '┈┈┈', 'var99': '┉┉┉', 'cluster': '┄┄┄'}

                for stat in ['mean', 'std', 'var95', 'var99', 'cluster']:
                    if self._is_stat_enabled(stat):
                        legend_lines.append(f'<span style="color:{colors[stat]};">{symbols[stat]}</span> {labels[stat]}<br/>')

                legend_lines.append('<small><i>Based on full candle moves</i></small>')

            legend_content = ''.join(legend_lines)
            legend_text = f"""
            <div style="background-color:rgba(30,30,30,240); color:white; border:1px solid #3e3e3e;
                        border-radius:6px; padding:10px; font-size:10pt; box-shadow: 0 2px 8px rgba(0,0,0,0.3);">
                {legend_content}
            </div>
            """

            # Create floating legend that stays in top right corner
            self._statistics_legend = pg.TextItem(html=legend_text, anchor=(1, 0))  # Anchor to top-right
            self._statistics_legend.setZValue(2000)  # Ensure it's on top of everything

            # Add to chart
            self.ybm_chart.addItem(self._statistics_legend)

            # Position legend in top right corner and connect to view range changes
            self._update_legend_position()

            # Connect to view range changes to keep legend in top right
            if not hasattr(self, '_legend_view_connection') or self._legend_view_connection is None:
                # Connect to the view box range changed signal
                self._legend_view_connection = self.ybm_chart.plotItem.vb.sigRangeChanged.connect(self._update_legend_position)
                # Also connect to transform changed for additional responsiveness
                self._legend_transform_connection = self.ybm_chart.plotItem.vb.sigTransformChanged.connect(self._update_legend_position)

        except Exception as e:
            logger.debug(f"Error adding statistics legend: {e}")

    def _update_legend_position(self):
        """Update the legend position to stay in the top right corner of the viewport"""
        try:
            if hasattr(self, '_statistics_legend') and self._statistics_legend is not None:
                # Get the current viewport range (what's actually visible on screen)
                view_box = self.ybm_chart.plotItem.vb
                view_range = view_box.viewRange()

                # Get the visible coordinate ranges
                x_range = view_range[0]  # [x_min, x_max] of visible area
                y_range = view_range[1]  # [y_min, y_max] of visible area

                x_min, x_max = x_range[0], x_range[1]
                y_min, y_max = y_range[0], y_range[1]

                # Calculate position in top right corner of the VISIBLE area
                # Use smaller margins for tighter positioning
                margin_x_percent = 0.01  # 1% margin from right edge
                margin_y_percent = 0.01  # 1% margin from top edge

                # Calculate actual margins in data coordinates
                margin_x = (x_max - x_min) * margin_x_percent
                margin_y = (y_max - y_min) * margin_y_percent

                # Position legend in top right of visible area
                legend_x = x_max - margin_x
                legend_y = y_max - margin_y

                # Set the position
                self._statistics_legend.setPos(legend_x, legend_y)

        except Exception as e:
            logger.debug(f"Error updating legend position: {e}")

    def _add_statistical_overlays_split_mode(self, above_pivot_candles, below_pivot_candles):
        """Add statistical overlays specifically for split sort mode using cut candle data"""
        try:
            if not above_pivot_candles and not below_pivot_candles:
                return

            # Extract values from cut candles for statistical analysis
            upside_values = []
            downside_values = []

            # Process above pivot candles (cut portions above 0%)
            for candle in above_pivot_candles:
                # Format: [x_pos, cut_open, high_pct, cut_low, cut_close, date, original_color, original_is_bullish]
                high_pct = candle[2]  # High percentage
                cut_low = candle[3]   # Cut low (minimum 0%)

                # For split mode, we analyze the range of each cut portion
                cut_range = high_pct - cut_low
                if cut_range > 0:
                    upside_values.append(cut_range)

            # Process below pivot candles (cut portions below 0%)
            for candle in below_pivot_candles:
                # Format: [x_pos, cut_open, cut_high, low_pct, cut_close, date, original_color, original_is_bullish]
                cut_high = candle[2]  # Cut high (maximum 0%)
                low_pct = candle[3]   # Low percentage

                # For split mode, we analyze the range of each cut portion
                cut_range = cut_high - low_pct  # This will be positive since cut_high is 0% and low_pct is negative
                if cut_range > 0:
                    downside_values.append(cut_range)

            # Calculate statistical metrics for the cut ranges
            upside_stats = self._calculate_stats_for_overlay(upside_values) if upside_values else None
            downside_stats = self._calculate_stats_for_overlay(downside_values) if downside_values else None

            # Get chart range for positioning
            if hasattr(self, 'ybm_chart') and self.ybm_chart.plotItem.vb.viewRange():
                x_range = self.ybm_chart.plotItem.vb.viewRange()[0]
                x_min, x_max = x_range[0], x_range[1]
            else:
                x_min, x_max = -2, 10

            # Add upside statistical lines (positioned in positive territory)
            if upside_stats:
                self._add_split_stat_lines_to_chart(upside_stats, x_min, x_max, 'upside')

            # Add downside statistical lines (positioned in negative territory)
            if downside_stats:
                # Convert to negative positioning for display
                downside_stats_negative = {k: -v if v is not None else None for k, v in downside_stats.items()}
                self._add_split_stat_lines_to_chart(downside_stats_negative, x_min, x_max, 'downside')

        except Exception as e:
            logger.debug(f"Error adding split mode statistical overlays: {e}")

    def _add_split_stat_lines_to_chart(self, stats_dict, x_min, x_max, side):
        """Add statistical lines to chart for split mode with different styling"""
        try:
            if not stats_dict:
                return

            # Different color scheme for split mode to distinguish from normal mode
            colors = {
                'mean': '#FFD54F',      # Light Amber
                'std': '#BA68C8',       # Light Purple
                'var95': '#FF8A65',     # Light Deep Orange
                'var99': '#F06292',     # Light Pink
                'cluster': '#4DD0E1'    # Light Cyan
            }

            # Different line styles for split mode
            line_styles = {
                'mean': QtCore.Qt.PenStyle.SolidLine,
                'std': QtCore.Qt.PenStyle.DashLine,
                'var95': QtCore.Qt.PenStyle.DotLine,
                'var99': QtCore.Qt.PenStyle.DashDotLine,
                'cluster': QtCore.Qt.PenStyle.DashDotDotLine
            }

            # Add lines for each metric (only if enabled)
            for metric, value in stats_dict.items():
                if value is not None and metric in colors and self._is_stat_enabled(metric):
                    # Create the line with thicker width for split mode
                    line = pg.InfiniteLine(
                        pos=value,
                        angle=0,
                        pen=pg.mkPen(colors[metric], width=3, style=line_styles[metric])
                    )
                    self.ybm_chart.addItem(line)

                    # Add label for the line with [SPLIT] prefix and custom VaR levels
                    label_text = f"[SPLIT] {metric.upper()}: {abs(value):.2f}%"
                    if metric == 'var95':
                        var_level_1, _ = self._get_var_levels()
                        label_text = f"[SPLIT] VaR{var_level_1:.1f}%: {abs(value):.2f}%"
                    elif metric == 'var99':
                        _, var_level_2 = self._get_var_levels()
                        label_text = f"[SPLIT] VaR{var_level_2:.1f}%: {abs(value):.2f}%"
                    elif metric == 'cluster':
                        label_text = f"[SPLIT] Cluster: {abs(value):.2f}%"

                    # Position label on the left side for split mode to avoid overlap
                    label = pg.TextItem(
                        text=label_text,
                        color=colors[metric],
                        anchor=(1, 0.5)
                    )
                    label.setPos(x_min - 0.5, value)
                    self.ybm_chart.addItem(label)

            # Standard deviation is already handled by the main std line above
            # No additional lines needed - just the main std dev line without shaded bands

        except Exception as e:
            logger.debug(f"Error adding split stat lines to chart: {e}")

    def _calculate_split_candle_densities(self, above_pivot_candles, below_pivot_candles):
        """Calculate separate density for above and below pivot candles in split sort mode"""
        try:
            # Calculate density for above pivot candles (left side)
            above_bin_centers, above_density_counts, above_bin_edges = None, None, None
            if above_pivot_candles:
                above_highs = [candle[2] for candle in above_pivot_candles]  # high_pct
                above_lows = [candle[3] for candle in above_pivot_candles]   # low_pct

                if above_highs and above_lows:
                    above_y_min = min(above_lows)
                    above_y_max = max(above_highs)

                    # Create bins for above pivot density
                    num_bins = 75
                    above_bin_edges = np.linspace(above_y_min, above_y_max, num_bins + 1)
                    above_bin_centers = (above_bin_edges[:-1] + above_bin_edges[1:]) / 2
                    above_density_counts = np.zeros(num_bins)

                    # Count candle presence in each price bin for above pivot
                    for candle in above_pivot_candles:
                        high_pct = candle[2]
                        low_pct = candle[3]

                        if self.use_full_candle_length_checkbox.isChecked():
                            # Full candle length mode: treat entire range uniformly
                            # Add uniform density across all bins that the candle spans
                            for bin_idx in range(num_bins):
                                bin_center = above_bin_centers[bin_idx]

                                # Check if this bin overlaps with the candle range
                                if bin_center >= low_pct and bin_center <= high_pct:
                                    above_density_counts[bin_idx] += 1
                        else:
                            # Original sampling mode: sample points along the candle range
                            candle_range = np.linspace(low_pct, high_pct, max(1, int(abs(high_pct - low_pct) * 10)))

                            for price in candle_range:
                                if above_y_min <= price <= above_y_max:
                                    bin_idx = int((price - above_y_min) / (above_y_max - above_y_min) * num_bins)
                                    bin_idx = min(bin_idx, num_bins - 1)
                                    above_density_counts[bin_idx] += 1

                    # Normalize the above density counts
                    if above_density_counts.max() > 0:
                        above_density_counts = above_density_counts / above_density_counts.max()

            # Calculate density for below pivot candles (right side)
            below_bin_centers, below_density_counts, below_bin_edges = None, None, None
            if below_pivot_candles:
                below_highs = [candle[2] for candle in below_pivot_candles]  # high_pct
                below_lows = [candle[3] for candle in below_pivot_candles]   # low_pct

                if below_highs and below_lows:
                    below_y_min = min(below_lows)
                    below_y_max = max(below_highs)

                    # Create bins for below pivot density
                    num_bins = 75
                    below_bin_edges = np.linspace(below_y_min, below_y_max, num_bins + 1)
                    below_bin_centers = (below_bin_edges[:-1] + below_bin_edges[1:]) / 2
                    below_density_counts = np.zeros(num_bins)

                    # Count candle presence in each price bin for below pivot
                    for candle in below_pivot_candles:
                        high_pct = candle[2]
                        low_pct = candle[3]

                        if self.use_full_candle_length_checkbox.isChecked():
                            # Full candle length mode: treat entire range uniformly
                            # Add uniform density across all bins that the candle spans
                            for bin_idx in range(num_bins):
                                bin_center = below_bin_centers[bin_idx]

                                # Check if this bin overlaps with the candle range
                                if bin_center >= low_pct and bin_center <= high_pct:
                                    below_density_counts[bin_idx] += 1
                        else:
                            # Original sampling mode: sample points along the candle range
                            candle_range = np.linspace(low_pct, high_pct, max(1, int(abs(high_pct - low_pct) * 10)))

                            for price in candle_range:
                                if below_y_min <= price <= below_y_max:
                                    bin_idx = int((price - below_y_min) / (below_y_max - below_y_min) * num_bins)
                                    bin_idx = min(bin_idx, num_bins - 1)
                                    below_density_counts[bin_idx] += 1

                    # Normalize the below density counts
                    if below_density_counts.max() > 0:
                        below_density_counts = below_density_counts / below_density_counts.max()

            return (above_bin_centers, above_density_counts, above_bin_edges,
                    below_bin_centers, below_density_counts, below_bin_edges)

        except Exception as e:
            logger.debug(f"Error calculating split candle densities: {e}")
            return None, None, None, None, None, None

    def _calculate_candle_density(self, rebased_data, split_sort_enabled, above_pivot_candles=None, below_pivot_candles=None):
        """Calculate density of candles across price levels for heatmap visualization"""
        try:
            # Determine the price range for density calculation
            all_highs = []
            all_lows = []

            if split_sort_enabled and above_pivot_candles and below_pivot_candles:
                # Use split candle data
                for candle in above_pivot_candles + below_pivot_candles:
                    all_highs.append(candle[2])  # high_pct
                    all_lows.append(candle[3])   # low_pct
            else:
                # Use regular rebased data
                for candle in rebased_data:
                    all_highs.append(candle[2])  # high_pct
                    all_lows.append(candle[3])   # low_pct

            if not all_highs or not all_lows:
                return None, None, None

            # Calculate the overall price range
            density_y_min = min(all_lows)
            density_y_max = max(all_highs)

            # Create bins for density calculation with moderate resolution for granular but clean visualization
            num_bins = 75  # Reduced from 100 for more granular appearance
            bin_edges = np.linspace(density_y_min, density_y_max, num_bins + 1)
            bin_centers = (bin_edges[:-1] + bin_edges[1:]) / 2
            density_counts = np.zeros(num_bins)

            # Count candle presence in each price bin
            if split_sort_enabled and above_pivot_candles and below_pivot_candles:
                # Count split candle portions
                for candle in above_pivot_candles + below_pivot_candles:
                    high_pct = candle[2]
                    low_pct = candle[3]

                    if self.use_full_candle_length_checkbox.isChecked():
                        # Full candle length mode: treat entire range uniformly
                        for bin_idx in range(num_bins):
                            bin_center = bin_centers[bin_idx]
                            # Check if this bin overlaps with the candle range
                            if bin_center >= low_pct and bin_center <= high_pct:
                                density_counts[bin_idx] += 1
                    else:
                        # Original sampling mode: sample points along the candle range
                        candle_range = np.linspace(low_pct, high_pct, max(1, int(abs(high_pct - low_pct) * 10)))

                        for price in candle_range:
                            if density_y_min <= price <= density_y_max:
                                bin_idx = int((price - density_y_min) / (density_y_max - density_y_min) * num_bins)
                                bin_idx = min(bin_idx, num_bins - 1)
                                density_counts[bin_idx] += 1
            else:
                # Count regular candles
                for candle in rebased_data:
                    high_pct = candle[2]
                    low_pct = candle[3]

                    if self.use_full_candle_length_checkbox.isChecked():
                        # Full candle length mode: treat entire range uniformly
                        for bin_idx in range(num_bins):
                            bin_center = bin_centers[bin_idx]
                            # Check if this bin overlaps with the candle range
                            if bin_center >= low_pct and bin_center <= high_pct:
                                density_counts[bin_idx] += 1
                    else:
                        # Original sampling mode: sample points along the candle range
                        candle_range = np.linspace(low_pct, high_pct, max(1, int(abs(high_pct - low_pct) * 10)))

                        for price in candle_range:
                            if density_y_min <= price <= density_y_max:
                                bin_idx = int((price - density_y_min) / (density_y_max - density_y_min) * num_bins)
                                bin_idx = min(bin_idx, num_bins - 1)
                                density_counts[bin_idx] += 1

            # Normalize the density counts without smoothing
            if density_counts.max() > 0:
                density_counts = density_counts / density_counts.max()
            # If no data, keep original (already zeros)

            return bin_centers, density_counts, bin_edges

        except Exception as e:
            logger.debug(f"Error calculating candle density: {e}")
            return None, None, None

    def _add_density_heatmap_to_chart(self, bin_centers, density_counts, bin_edges, x_range):
        """Add density heatmap visualization to the chart"""
        try:
            if bin_centers is None or density_counts is None or bin_edges is None:
                return

            # Calculate heatmap positioning
            x_min, x_max = x_range
            bar_width = x_max - x_min + 5  # Span the full width of the chart with some padding
            bar_x_start = x_min - 2.5  # Start slightly before the first candle

            # White color for brighter density visualization
            white_color = (255, 255, 255)  # Pure white

            # Create horizontal bars with opacity based on density
            for i, (bin_center, density) in enumerate(zip(bin_centers, density_counts)):
                if density < 0.001:  # Lower threshold for more visible density bars
                    continue

                # Create a white rectangle spanning the chart width
                rect = QtWidgets.QGraphicsRectItem(
                    bar_x_start,
                    bin_center - (bin_edges[i+1] - bin_edges[i]) / 2,  # Center on bin
                    bar_width,
                    bin_edges[i+1] - bin_edges[i]  # Height is bin size
                )

                # Set opacity based on density with brighter, more visible gradients
                min_opacity = 30   # Higher minimum opacity for better visibility
                max_opacity = 220  # Much higher maximum for solid white appearance

                # Apply square root transformation to boost lower densities for better visibility
                boosted_density = np.power(density, 0.6)  # More aggressive boost for better contrast
                opacity = int(min_opacity + boosted_density * (max_opacity - min_opacity))

                # Create white color with calculated opacity (solid white to clear gradient)
                gradient_color = pg.mkColor(white_color[0], white_color[1], white_color[2], opacity)
                rect.setBrush(pg.mkBrush(gradient_color))

                # No border for cleaner look
                rect.setPen(pg.mkPen(None))

                # Add the rectangle to the chart (it will render behind candles due to z-order)
                self.ybm_chart.addItem(rect)

            logger.debug(f"Added bright white density heatmap with {len([d for d in density_counts if d >= 0.001])} visible density bars")

        except Exception as e:
            logger.debug(f"Error adding density heatmap to chart: {e}")

    def _add_density_profile_to_chart(self, bin_centers, density_counts, bin_edges, x_range):
        """Add density profile visualization to the chart (white bars on the left side)"""
        try:
            if bin_centers is None or density_counts is None or bin_edges is None:
                return

            # Calculate profile positioning (moved even further left)
            x_min, x_max = x_range
            profile_x_start = x_min - 7.0  # Position even further to the left of the chart
            profile_max_width = 3.0  # Slightly wider maximum width for better visibility

            # Create density profile bars with width based on density
            for i, (bin_center, density) in enumerate(zip(bin_centers, density_counts)):
                # Skip bins with very low density for cleaner visualization
                if density < 0.001:  # Lower threshold for more visible density bars (matching heatmap)
                    continue

                # Calculate bar width based on density
                profile_width = density * profile_max_width

                # Create a white rectangle with width based on density
                profile_rect = QtWidgets.QGraphicsRectItem(
                    profile_x_start,
                    bin_center - (bin_edges[i+1] - bin_edges[i]) / 2,  # Center on bin
                    profile_width,
                    bin_edges[i+1] - bin_edges[i]  # Height is bin size
                )

                # Set color to match density heatmap gradient (white with opacity based on density)
                # Use the same white color and opacity calculation as the heatmap for consistency
                white_color = (255, 255, 255)  # Pure white (same as heatmap)

                # Set opacity based on density with brighter, more visible gradients (matching heatmap)
                min_opacity = 30   # Higher minimum opacity for better visibility (same as heatmap)
                max_opacity = 220  # Much higher maximum for solid white appearance (same as heatmap)

                # Apply square root transformation to boost lower densities for better visibility (same as heatmap)
                boosted_density = np.power(density, 0.6)  # More aggressive boost for better contrast
                opacity = int(min_opacity + boosted_density * (max_opacity - min_opacity))

                # Create white color with calculated opacity (solid white to clear gradient, matching heatmap exactly)
                profile_color = pg.mkColor(white_color[0], white_color[1], white_color[2], opacity)
                profile_rect.setBrush(pg.mkBrush(profile_color))

                # No border for cleaner look (matching heatmap style)
                profile_rect.setPen(pg.mkPen(None))

                # Add the density profile rectangle to the chart
                self.ybm_chart.addItem(profile_rect)

            logger.debug(f"Added bright white density profile with {len([d for d in density_counts if d >= 0.001])} visible profile bars")

        except Exception as e:
            logger.debug(f"Error adding density profile to chart: {e}")

    def _add_split_density_heatmap_to_chart(self, above_data, below_data, above_pivot_count, below_pivot_count):
        """Add separate density heatmaps for above and below pivot candles in split sort mode"""
        try:
            above_bin_centers, above_density_counts, above_bin_edges = above_data
            below_bin_centers, below_density_counts, below_bin_edges = below_data

            # White color for brighter density visualization
            white_color = (255, 255, 255)  # Pure white

            # Add heatmap for above pivot candles (left side)
            if above_bin_centers is not None and above_density_counts is not None:
                # Calculate positioning for left side
                left_x_start = -2.5  # Start slightly before the first candle
                left_width = above_pivot_count + 2.5  # Span the left section

                for i, (bin_center, density) in enumerate(zip(above_bin_centers, above_density_counts)):
                    if density < 0.001:  # Lower threshold for more visible density bars
                        continue

                    # Create a white rectangle spanning the left section
                    rect = QtWidgets.QGraphicsRectItem(
                        left_x_start,
                        bin_center - (above_bin_edges[i+1] - above_bin_edges[i]) / 2,  # Center on bin
                        left_width,
                        above_bin_edges[i+1] - above_bin_edges[i]  # Height is bin size
                    )

                    # Set opacity based on density with brighter, more visible gradients
                    min_opacity = 30   # Higher minimum opacity for better visibility
                    max_opacity = 220  # Much higher maximum for solid white appearance

                    # Apply square root transformation to boost lower densities for better visibility
                    boosted_density = np.power(density, 0.6)  # More aggressive boost for better contrast
                    opacity = int(min_opacity + boosted_density * (max_opacity - min_opacity))

                    # Create white color with calculated opacity (solid white to clear gradient)
                    gradient_color = pg.mkColor(white_color[0], white_color[1], white_color[2], opacity)
                    rect.setBrush(pg.mkBrush(gradient_color))

                    # No border for cleaner look
                    rect.setPen(pg.mkPen(None))

                    # Add the rectangle to the chart
                    self.ybm_chart.addItem(rect)

            # Add heatmap for below pivot candles (right side)
            if below_bin_centers is not None and below_density_counts is not None:
                # Calculate positioning for right side
                right_x_start = above_pivot_count - 0.5  # Start at the separator
                right_width = below_pivot_count + 2.5  # Span the right section

                for i, (bin_center, density) in enumerate(zip(below_bin_centers, below_density_counts)):
                    if density < 0.001:  # Lower threshold for more visible density bars
                        continue

                    # Create a white rectangle spanning the right section
                    rect = QtWidgets.QGraphicsRectItem(
                        right_x_start,
                        bin_center - (below_bin_edges[i+1] - below_bin_edges[i]) / 2,  # Center on bin
                        right_width,
                        below_bin_edges[i+1] - below_bin_edges[i]  # Height is bin size
                    )

                    # Set opacity based on density with brighter, more visible gradients
                    min_opacity = 30   # Higher minimum opacity for better visibility
                    max_opacity = 220  # Much higher maximum for solid white appearance

                    # Apply square root transformation to boost lower densities for better visibility
                    boosted_density = np.power(density, 0.6)  # More aggressive boost for better contrast
                    opacity = int(min_opacity + boosted_density * (max_opacity - min_opacity))

                    # Create white color with calculated opacity (solid white to clear gradient)
                    gradient_color = pg.mkColor(white_color[0], white_color[1], white_color[2], opacity)
                    rect.setBrush(pg.mkBrush(gradient_color))

                    # No border for cleaner look
                    rect.setPen(pg.mkPen(None))

                    # Add the rectangle to the chart
                    self.ybm_chart.addItem(rect)

            logger.debug(f"Added split density heatmaps - Left: {len([d for d in above_density_counts if d >= 0.001]) if above_density_counts is not None else 0} bars, Right: {len([d for d in below_density_counts if d >= 0.001]) if below_density_counts is not None else 0} bars")

        except Exception as e:
            logger.debug(f"Error adding split density heatmap to chart: {e}")

    def _add_split_density_profile_to_chart(self, above_data, below_data, above_pivot_count, below_pivot_count):
        """Add separate density profiles for above and below pivot candles in split sort mode"""
        try:
            above_bin_centers, above_density_counts, above_bin_edges = above_data
            below_bin_centers, below_density_counts, below_bin_edges = below_data

            # White color for brighter density visualization
            white_color = (255, 255, 255)  # Pure white
            profile_max_width = 3.0  # Maximum width for profile bars

            # Add profile for above pivot candles (left side)
            if above_bin_centers is not None and above_density_counts is not None:
                # Position profile to the left of the left section
                left_profile_x_start = -7.0  # Position to the left of the chart

                for i, (bin_center, density) in enumerate(zip(above_bin_centers, above_density_counts)):
                    if density < 0.001:  # Lower threshold for more visible density bars
                        continue

                    # Calculate bar width based on density
                    profile_width = density * profile_max_width

                    # Create a white rectangle with width based on density
                    profile_rect = QtWidgets.QGraphicsRectItem(
                        left_profile_x_start,
                        bin_center - (above_bin_edges[i+1] - above_bin_edges[i]) / 2,  # Center on bin
                        profile_width,
                        above_bin_edges[i+1] - above_bin_edges[i]  # Height is bin size
                    )

                    # Set opacity based on density with brighter, more visible gradients
                    min_opacity = 30   # Higher minimum opacity for better visibility
                    max_opacity = 220  # Much higher maximum for solid white appearance

                    # Apply square root transformation to boost lower densities for better visibility
                    boosted_density = np.power(density, 0.6)  # More aggressive boost for better contrast
                    opacity = int(min_opacity + boosted_density * (max_opacity - min_opacity))

                    # Create white color with calculated opacity (solid white to clear gradient)
                    profile_color = pg.mkColor(white_color[0], white_color[1], white_color[2], opacity)
                    profile_rect.setBrush(pg.mkBrush(profile_color))

                    # No border for cleaner look
                    profile_rect.setPen(pg.mkPen(None))

                    # Add the density profile rectangle to the chart
                    self.ybm_chart.addItem(profile_rect)

            # Add profile for below pivot candles (right side)
            if below_bin_centers is not None and below_density_counts is not None:
                # Position profile to the left of the right section (where the right section starts)
                right_profile_x_start = above_pivot_count - 3.5  # Position just to the left of where the right section starts

                for i, (bin_center, density) in enumerate(zip(below_bin_centers, below_density_counts)):
                    if density < 0.001:  # Lower threshold for more visible density bars
                        continue

                    # Calculate bar width based on density
                    profile_width = density * profile_max_width

                    # Create a white rectangle with width based on density
                    profile_rect = QtWidgets.QGraphicsRectItem(
                        right_profile_x_start,
                        bin_center - (below_bin_edges[i+1] - below_bin_edges[i]) / 2,  # Center on bin
                        profile_width,
                        below_bin_edges[i+1] - below_bin_edges[i]  # Height is bin size
                    )

                    # Set opacity based on density with brighter, more visible gradients
                    min_opacity = 30   # Higher minimum opacity for better visibility
                    max_opacity = 220  # Much higher maximum for solid white appearance

                    # Apply square root transformation to boost lower densities for better visibility
                    boosted_density = np.power(density, 0.6)  # More aggressive boost for better contrast
                    opacity = int(min_opacity + boosted_density * (max_opacity - min_opacity))

                    # Create white color with calculated opacity (solid white to clear gradient)
                    profile_color = pg.mkColor(white_color[0], white_color[1], white_color[2], opacity)
                    profile_rect.setBrush(pg.mkBrush(profile_color))

                    # No border for cleaner look
                    profile_rect.setPen(pg.mkPen(None))

                    # Add the density profile rectangle to the chart
                    self.ybm_chart.addItem(profile_rect)

            logger.debug(f"Added split density profiles - Left: {len([d for d in above_density_counts if d >= 0.001]) if above_density_counts is not None else 0} bars, Right: {len([d for d in below_density_counts if d >= 0.001]) if below_density_counts is not None else 0} bars")

        except Exception as e:
            logger.debug(f"Error adding split density profile to chart: {e}")

    def show_historical_dialog(self):
        """Show dialog to select historical date and time"""
        import datetime

        dialog = QtWidgets.QDialog(self)
        dialog.setWindowTitle("Select Historical Date")
        dialog.setModal(True)
        dialog.resize(400, 200)
        dialog.setStyleSheet("""
            QDialog {
                background-color: #1e1e1e;
                color: #e0e0e0;
            }
            QLabel {
                color: #e0e0e0;
                font-size: 12px;
            }
            QDateEdit, QTimeEdit {
                background-color: #2e2e2e;
                color: #e0e0e0;
                border: 1px solid #3e3e3e;
                border-radius: 4px;
                padding: 4px;
                font-size: 12px;
            }
            QPushButton {
                background-color: #3e3e3e;
                color: #e0e0e0;
                border: 1px solid #5e5e5e;
                border-radius: 4px;
                padding: 8px 16px;
                font-size: 11px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #4e4e4e;
                border: 1px solid #6e6e6e;
            }
            QPushButton:pressed {
                background-color: #2e2e2e;
            }
        """)

        dialog_layout = QtWidgets.QVBoxLayout(dialog)

        # Date selection
        date_layout = QtWidgets.QHBoxLayout()
        date_layout.addWidget(QtWidgets.QLabel("Date:"))
        date_picker = QtWidgets.QDateEdit()
        date_picker.setCalendarPopup(True)
        date_picker.setDate(QtCore.QDate.currentDate())
        date_layout.addWidget(date_picker)
        dialog_layout.addLayout(date_layout)

        # Time selection
        time_layout = QtWidgets.QHBoxLayout()
        time_layout.addWidget(QtWidgets.QLabel("Time:"))
        time_picker = QtWidgets.QTimeEdit()
        time_picker.setTime(QtCore.QTime(16, 0))  # Default to 4 PM
        time_layout.addWidget(time_picker)
        dialog_layout.addLayout(time_layout)

        # Buttons
        button_layout = QtWidgets.QHBoxLayout()
        cancel_button = QtWidgets.QPushButton("Cancel")
        load_button = QtWidgets.QPushButton("Load Historical Data")

        button_layout.addWidget(cancel_button)
        button_layout.addWidget(load_button)
        dialog_layout.addLayout(button_layout)

        # Connect buttons
        load_button.clicked.connect(lambda: self._load_historical_from_dialog(dialog, date_picker, time_picker))
        cancel_button.clicked.connect(dialog.reject)

        dialog.exec()

    def _load_historical_from_dialog(self, dialog, date_picker, time_picker):
        """Load historical data from the dialog"""
        import datetime

        # Get the selected date and time
        selected_date = date_picker.date().toPyDate()
        selected_time = time_picker.time().toPyTime()

        # Combine into a datetime object
        selected_datetime = datetime.datetime.combine(selected_date, selected_time)

        # Load the historical data
        self._load_historical_data(selected_datetime)

        # Close the dialog
        dialog.accept()

    def on_historical_dropdown_changed(self, index):
        """Handle changes to the historical data dropdown"""
        # If the index is 0, reset to current data
        if index == 0:
            self.reset_to_current_data()
            return

        # Otherwise, load the historical data for the selected timestamp
        timestamp_str = self.historical_dropdown.itemText(index)
        try:
            import datetime
            # Parse the timestamp string (format: "YYYY-MM-DD HH:MM:SS")
            timestamp = datetime.datetime.strptime(timestamp_str, "%Y-%m-%d %H:%M:%S")

            # Load the historical data and update all charts
            self._load_historical_data(timestamp)
        except Exception as e:
            logger.error(f"Error parsing timestamp: {str(e)}")
            QtWidgets.QMessageBox.warning(self, "Error", f"Could not parse timestamp: {str(e)}")

    def _load_historical_data(self, timestamp):
        """Load historical data for a specific timestamp"""
        import datetime

        try:
            logger.info(f"Loading historical data for Options Analyzer: {timestamp}")

            # Get the market odds tab
            market_odds_tab = self.get_current_market_odds_tab()
            if not market_odds_tab or not hasattr(market_odds_tab, 'data') or market_odds_tab.data is None:
                QtWidgets.QMessageBox.warning(self, "Error", "No market data available. Please load data in the Market Odds tab first.")
                return

            # Store original data if not already stored
            if not self.viewing_historical:
                self.original_data = market_odds_tab.data.copy()

            # Find the closest timestamp in the data
            data = self.original_data if self.viewing_historical else market_odds_tab.data

            # Convert timestamp to pandas datetime if needed
            if hasattr(data.index, 'tz_localize'):
                # If the index is timezone-naive, make the timestamp timezone-naive too
                if data.index.tz is None:
                    timestamp = timestamp.replace(tzinfo=None)

            # Find the closest timestamp that is <= the requested timestamp
            available_timestamps = data.index[data.index <= timestamp]

            if len(available_timestamps) == 0:
                QtWidgets.QMessageBox.warning(self, "Error", f"No data available for {timestamp}. Please select a later date.")
                return

            # Get the closest timestamp
            actual_timestamp = available_timestamps[-1]

            # Get historical data up to that point
            historical_data = data.loc[:actual_timestamp].copy()

            logger.info(f"Found {len(historical_data)} rows of historical data up to {actual_timestamp}")

            # Temporarily replace the data in the market odds tab
            market_odds_tab.data = historical_data

            # Trigger recalculation of the Market Odds tab analysis
            # This will regenerate all the cycle rays, extreme labels, and rebased data
            if hasattr(market_odds_tab, 'update_data_from_universal'):
                # Get the symbol from the market odds tab
                symbol = market_odds_tab.symbol_input.text().strip().upper()
                # Call the update method to trigger recalculation
                market_odds_tab.update_data_from_universal(symbol, historical_data)

            # Set the viewing historical flag
            self.viewing_historical = True
            self.historical_timestamp = actual_timestamp

            # Enable the "Back to Current" button
            self.back_to_current_button.setEnabled(True)

            # Format the timestamp for display
            timestamp_str = actual_timestamp.strftime("%Y-%m-%d %H:%M:%S")

            # Add the timestamp to the dropdown if it's not already there
            found = False
            for i in range(self.historical_dropdown.count()):
                if self.historical_dropdown.itemText(i) == timestamp_str:
                    found = True
                    self.historical_dropdown.setCurrentIndex(i)
                    break

            if not found:
                self.historical_dropdown.addItem(timestamp_str)
                self.historical_dropdown.setCurrentIndex(self.historical_dropdown.count() - 1)

            # Update the data tab to refresh its data
            data_tab = self.get_data_tab()
            if data_tab and hasattr(data_tab, 'refresh_data'):
                data_tab.refresh_data()

            # Update the charts with historical data
            self.update_charts()

            # Update the info panel to show we're viewing historical data
            self.ticker_label.setText(f"Ticker: {symbol or '--'} (Historical: {timestamp_str})")

        except Exception as e:
            logger.error(f"Error loading historical data: {str(e)}", exc_info=True)
            QtWidgets.QMessageBox.critical(self, "Error", f"Error loading historical data: {str(e)}")

    def reset_to_current_data(self):
        """Reset to current (live) data"""
        try:
            if not self.viewing_historical:
                return

            # Get the market odds tab
            market_odds_tab = self.get_current_market_odds_tab()
            if not market_odds_tab:
                return

            # Restore original data
            if self.original_data is not None:
                market_odds_tab.data = self.original_data.copy()

                # Trigger recalculation of the Market Odds tab analysis
                if hasattr(market_odds_tab, 'update_data_from_universal'):
                    # Get the symbol from the market odds tab
                    symbol = market_odds_tab.symbol_input.text().strip().upper()
                    # Call the update method to trigger recalculation
                    market_odds_tab.update_data_from_universal(symbol, self.original_data)

            # Reset flags
            self.viewing_historical = False
            self.historical_timestamp = None
            self.original_data = None

            # Disable the "Back to Current" button
            self.back_to_current_button.setEnabled(False)

            # Reset dropdown to "Current Data"
            self.historical_dropdown.setCurrentIndex(0)

            # Update the data tab to refresh its data
            data_tab = self.get_data_tab()
            if data_tab and hasattr(data_tab, 'refresh_data'):
                data_tab.refresh_data()

            # Update the charts with current data
            self.update_charts()

            # Update the info panel to remove historical indicator
            symbol = market_odds_tab.symbol_input.text().strip().upper()
            self.ticker_label.setText(f"Ticker: {symbol or '--'}")

        except Exception as e:
            logger.error(f"Error resetting to current data: {str(e)}", exc_info=True)
            QtWidgets.QMessageBox.critical(self, "Error", f"Error resetting to current data: {str(e)}")

    def get_data_tab(self):
        """Get the data tab from the main window"""
        try:
            # Navigate up the widget hierarchy to find the main window
            parent = self.parent()
            while parent is not None:
                if hasattr(parent, 'data_tab'):
                    return parent.data_tab
                parent = parent.parent()
            return None
        except Exception as e:
            logger.error(f"Error getting data tab: {str(e)}")
            return None
