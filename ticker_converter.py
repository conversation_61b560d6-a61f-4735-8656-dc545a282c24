"""
Ticker Converter Module

This module provides comprehensive ticker symbol conversion between different data sources
(Yahoo Finance, Schwab API, etc.) to ensure proper symbol formatting for each API.
"""

import logging

logger = logging.getLogger(__name__)

class TickerConverter:
    """
    Handles ticker symbol conversion between different data sources.

    Supports conversion between:
    - Yahoo Finance format (ES=F, ^SPX, etc.)
    - Schwab API format (/ES, SPX, etc.)
    - Standard format (ES, SPX, etc.)
    """

    # Futures symbol mappings
    FUTURES_MAPPINGS = {
        # Yahoo Finance -> Schwab API
        'ES=F': '/ES',      # E-mini S&P 500
        'NQ=F': '/NQ',      # E-mini NASDAQ-100
        'YM=F': '/YM',      # E-mini Dow Jones
        'RTY=F': '/RTY',    # E-mini Russell 2000
        'CL=F': '/CL',      # Crude Oil
        'GC=F': '/GC',      # Gold
        'SI=F': '/SI',      # Silver
        'ZB=F': '/ZB',      # 30-Year Treasury Bond
        'ZN=F': '/ZN',      # 10-Year Treasury Note
        'ZF=F': '/ZF',      # 5-Year Treasury Note
        'ZT=F': '/ZT',      # 2-Year Treasury Note
        'NG=F': '/NG',      # Natural Gas
        'HG=F': '/HG',      # Copper
        'ZC=F': '/ZC',      # Corn
        'ZS=F': '/ZS',      # Soybeans
        'ZW=F': '/ZW',      # Wheat
        'KC=F': '/KC',      # Coffee
        'SB=F': '/SB',      # Sugar
        'CT=F': '/CT',      # Cotton
        'CC=F': '/CC',      # Cocoa
        'LB=F': '/LB',      # Lumber
        'HE=F': '/HE',      # Lean Hogs
        'LE=F': '/LE',      # Live Cattle
        'GF=F': '/GF',      # Feeder Cattle
        'PA=F': '/PA',      # Palladium
        'PL=F': '/PL',      # Platinum
        'DX=F': '/DX',      # US Dollar Index
        'BTC=F': '/BTC',    # Bitcoin Futures
        'ETH=F': '/ETH',    # Ethereum Futures
    }

    # Index symbol mappings
    INDEX_MAPPINGS = {
        # Yahoo Finance -> Schwab API
        '^SPX': 'SPX',      # S&P 500 Index
        '^GSPC': 'SPX',     # S&P 500 Index (alternative)
        'SPX': 'SPX',       # S&P 500 Index (bare)
        '^NDX': 'NDX',      # NASDAQ-100 Index
        '^IXIC': 'COMP',    # NASDAQ Composite
        '^DJI': 'DJI',      # Dow Jones Industrial Average
        '^RUT': 'RUT',      # Russell 2000 Index
        '^VIX': 'VIX',      # CBOE Volatility Index
        '^TNX': 'TNX',      # 10-Year Treasury Yield
        '^TYX': 'TYX',      # 30-Year Treasury Yield
        '^FVX': 'FVX',      # 5-Year Treasury Yield
        '^IRX': 'IRX',      # 13-Week Treasury Bill
    }

    # Reverse mappings for conversion back
    REVERSE_FUTURES_MAPPINGS = {v: k for k, v in FUTURES_MAPPINGS.items()}
    REVERSE_INDEX_MAPPINGS = {v: k for k, v in INDEX_MAPPINGS.items()}

    @classmethod
    def to_schwab_format(cls, symbol: str) -> str:
        """
        Convert a symbol to Schwab API format.

        Args:
            symbol: Input symbol (can be Yahoo Finance format, Schwab format, or standard)

        Returns:
            Symbol in Schwab API format
        """
        if not symbol:
            return symbol

        symbol = symbol.upper().strip()

        # Check if it's already in Schwab futures format (starts with /)
        if symbol.startswith('/'):
            return symbol

        # Check futures mappings
        if symbol in cls.FUTURES_MAPPINGS:
            converted = cls.FUTURES_MAPPINGS[symbol]
            logger.info(f"Converted futures symbol: {symbol} -> {converted}")
            return converted

        # Check index mappings
        if symbol in cls.INDEX_MAPPINGS:
            converted = cls.INDEX_MAPPINGS[symbol]
            logger.info(f"Converted index symbol: {symbol} -> {converted}")
            return converted

        # Handle common futures patterns
        if symbol.endswith('=F'):
            # Try to map common futures without explicit mapping
            base = symbol[:-2]  # Remove =F
            schwab_symbol = f'/{base}'
            logger.info(f"Pattern-based futures conversion: {symbol} -> {schwab_symbol}")
            return schwab_symbol

        # Check if it's a common futures symbol without suffix
        common_futures = ['ES', 'NQ', 'YM', 'RTY', 'CL', 'GC', 'SI', 'ZB', 'ZN', 'ZF', 'ZT', 'NG', 'HG']
        if symbol in common_futures:
            schwab_symbol = f'/{symbol}'
            logger.info(f"Common futures symbol conversion: {symbol} -> {schwab_symbol}")
            return schwab_symbol

        # Handle index patterns
        if symbol.startswith('^'):
            # Remove ^ prefix for indices
            base = symbol[1:]
            logger.info(f"Pattern-based index conversion: {symbol} -> {base}")
            return base

        # Return as-is if no conversion needed
        return symbol

    @classmethod
    def to_yahoo_format(cls, symbol: str) -> str:
        """
        Convert a symbol to Yahoo Finance format.

        Args:
            symbol: Input symbol (can be Schwab format, Yahoo format, or standard)

        Returns:
            Symbol in Yahoo Finance format
        """
        if not symbol:
            return symbol

        symbol = symbol.upper().strip()

        # Check reverse futures mappings
        if symbol in cls.REVERSE_FUTURES_MAPPINGS:
            converted = cls.REVERSE_FUTURES_MAPPINGS[symbol]
            logger.info(f"Converted to Yahoo futures format: {symbol} -> {converted}")
            return converted

        # Check reverse index mappings
        if symbol in cls.REVERSE_INDEX_MAPPINGS:
            converted = cls.REVERSE_INDEX_MAPPINGS[symbol]
            logger.info(f"Converted to Yahoo index format: {symbol} -> {converted}")
            return converted

        # Handle Schwab futures format (starts with /)
        if symbol.startswith('/'):
            base = symbol[1:]  # Remove /
            yahoo_symbol = f'{base}=F'
            logger.info(f"Pattern-based conversion to Yahoo futures: {symbol} -> {yahoo_symbol}")
            return yahoo_symbol

        # Check if it's a common futures symbol without prefix
        common_futures = ['ES', 'NQ', 'YM', 'RTY', 'CL', 'GC', 'SI', 'ZB', 'ZN', 'ZF', 'ZT', 'NG', 'HG']
        if symbol in common_futures:
            yahoo_symbol = f'{symbol}=F'
            logger.info(f"Common futures symbol conversion to Yahoo: {symbol} -> {yahoo_symbol}")
            return yahoo_symbol

        # For indices, check if we need to add ^ prefix
        common_indices = ['SPX', 'NDX', 'DJI', 'RUT', 'VIX', 'COMP', 'TNX', 'TYX', 'FVX', 'IRX']
        if symbol in common_indices:
            # Special case for SPX - Yahoo uses ^SPX, not ^GSPC
            if symbol == 'SPX':
                yahoo_symbol = '^SPX'
            else:
                yahoo_symbol = f'^{symbol}'
            logger.info(f"Added ^ prefix for index: {symbol} -> {yahoo_symbol}")
            return yahoo_symbol

        # Return as-is if no conversion needed
        return symbol

    @classmethod
    def normalize_symbol(cls, symbol: str) -> str:
        """
        Normalize a symbol to a standard format (remove prefixes/suffixes).

        Args:
            symbol: Input symbol in any format

        Returns:
            Normalized symbol
        """
        if not symbol:
            return symbol

        symbol = symbol.upper().strip()

        # Remove common prefixes and suffixes
        if symbol.startswith('/'):
            symbol = symbol[1:]
        elif symbol.startswith('^'):
            symbol = symbol[1:]
        elif symbol.endswith('=F'):
            symbol = symbol[:-2]

        return symbol

    @classmethod
    def is_futures_symbol(cls, symbol: str) -> bool:
        """
        Check if a symbol represents a futures contract.

        Args:
            symbol: Symbol to check

        Returns:
            True if it's a futures symbol, False otherwise
        """
        if not symbol:
            return False

        symbol = symbol.upper().strip()

        # Check explicit mappings
        if symbol in cls.FUTURES_MAPPINGS or symbol in cls.REVERSE_FUTURES_MAPPINGS:
            return True

        # Check patterns
        if symbol.startswith('/') or symbol.endswith('=F'):
            return True

        # Check if it's a common futures symbol
        common_futures = ['ES', 'NQ', 'YM', 'RTY', 'CL', 'GC', 'SI', 'ZB', 'ZN', 'ZF', 'ZT', 'NG', 'HG']
        if symbol in common_futures:
            return True

        return False

    @classmethod
    def is_index_symbol(cls, symbol: str) -> bool:
        """
        Check if a symbol represents an index.

        Args:
            symbol: Symbol to check

        Returns:
            True if it's an index symbol, False otherwise
        """
        if not symbol:
            return False

        symbol = symbol.upper().strip()

        # Check explicit mappings
        if symbol in cls.INDEX_MAPPINGS or symbol in cls.REVERSE_INDEX_MAPPINGS:
            return True

        # Check patterns
        if symbol.startswith('^'):
            return True

        # Check common indices
        common_indices = ['SPX', 'NDX', 'DJI', 'RUT', 'VIX', 'COMP', 'TNX', 'TYX', 'FVX', 'IRX']
        if symbol in common_indices:
            return True

        return False

    @classmethod
    def get_symbol_info(cls, symbol: str) -> dict:
        """
        Get comprehensive information about a symbol.

        Args:
            symbol: Symbol to analyze

        Returns:
            Dictionary with symbol information
        """
        if not symbol:
            return {'original': symbol, 'type': 'unknown'}

        symbol = symbol.upper().strip()

        info = {
            'original': symbol,
            'normalized': cls.normalize_symbol(symbol),
            'schwab_format': cls.to_schwab_format(symbol),
            'yahoo_format': cls.to_yahoo_format(symbol),
            'is_futures': cls.is_futures_symbol(symbol),
            'is_index': cls.is_index_symbol(symbol),
            'type': 'unknown'
        }

        if info['is_futures']:
            info['type'] = 'futures'
        elif info['is_index']:
            info['type'] = 'index'
        else:
            info['type'] = 'equity'

        return info

# Global instance for easy access
ticker_converter = TickerConverter()

def convert_to_schwab(symbol: str) -> str:
    """Convenience function to convert symbol to Schwab format."""
    return ticker_converter.to_schwab_format(symbol)

def convert_to_yahoo(symbol: str) -> str:
    """Convenience function to convert symbol to Yahoo format."""
    return ticker_converter.to_yahoo_format(symbol)

def get_symbol_info(symbol: str) -> dict:
    """Convenience function to get symbol information."""
    return ticker_converter.get_symbol_info(symbol)
