"""
Data Tab for Market Odds and Options Analyzer

This module provides a UI tab for displaying rebased OHLC data in a table format.
"""

from PyQt6 import QtWidgets, QtCore, QtGui
import pandas as pd
import logging

# Import theme colors
try:
    import theme
    THEME_COLORS = theme.DEFAULT
except ImportError:
    # Fallback theme colors if theme module is not available
    THEME_COLORS = {
        'background': '#1e1e1e',           # Dark gray background
        'control_panel': '#2d2d2d',        # Lighter gray control panels
        'borders': '#3e3e3e',              # Border color
        'text': '#e0e0e0',                 # Light gray text
        'bullish': '#4CAF50',              # Material Design Green
        'bearish': '#F44336',              # Material Design Red
        'neutral': '#9E9E9E',              # Material Design Grey
        'highlight': '#FFC107',            # Material Design Amber
    }

logger = logging.getLogger("DataTab")

class OHLCTableModel(QtCore.QAbstractTableModel):
    """
    Model for displaying rebased OHLC data in a table.
    """
    def __init__(self, data=None):
        super().__init__()
        self._data = data if data is not None else pd.DataFrame()
        self._columns = []
        if not self._data.empty:
            self._columns = self._data.columns.tolist()
        self._is_bullish_cycle = {}  # Dictionary to track bullish/bearish cycles by row index

    def rowCount(self, parent=None):
        return len(self._data)

    def columnCount(self, parent=None):
        return len(self._columns)

    def data(self, index, role=QtCore.Qt.ItemDataRole.DisplayRole):
        if not index.isValid() or not (0 <= index.row() < len(self._data)):
            return None

        value = self._data.iloc[index.row(), index.column()]

        if role == QtCore.Qt.ItemDataRole.DisplayRole:
            # Format based on column type
            if pd.api.types.is_datetime64_any_dtype(self._data.iloc[:, index.column()]):
                return value.strftime('%Y-%m-%d %H:%M:%S')
            elif pd.api.types.is_numeric_dtype(self._data.iloc[:, index.column()]):
                return f"{value:.4f}" if isinstance(value, float) else str(value)
            return str(value)

        elif role == QtCore.Qt.ItemDataRole.TextAlignmentRole:
            # Right-align numeric data
            if pd.api.types.is_numeric_dtype(self._data.iloc[:, index.column()]):
                return int(QtCore.Qt.AlignmentFlag.AlignRight | QtCore.Qt.AlignmentFlag.AlignVCenter)
            return int(QtCore.Qt.AlignmentFlag.AlignLeft | QtCore.Qt.AlignmentFlag.AlignVCenter)

        elif role == QtCore.Qt.ItemDataRole.BackgroundRole:
            # Color rows based on bullish/bearish cycle
            row_idx = index.row()

            # Check if we have a Category column and if this is the vector prices tab
            if 'Category' in self._data.columns and len(self._data.columns) == 4 and 'Pivot Price' in self._data.columns:
                # This is the vector prices tab - color based on Category column
                category = self._data.iloc[row_idx]['Category']
                if category == "Bullish Cycle":
                    # Green highlight for bullish cycle
                    return QtGui.QColor(0, 80, 0)  # Darker green
                elif category == "Bearish Cycle":
                    # Red highlight for bearish cycle
                    return QtGui.QColor(80, 0, 0)  # Darker red
            # For other tabs, use the is_bullish_cycle dictionary
            elif row_idx in self._is_bullish_cycle:
                if self._is_bullish_cycle[row_idx]:
                    # Green highlight for bullish cycle
                    return QtGui.QColor(0, 80, 0)  # Darker green
                else:
                    # Red highlight for bearish cycle
                    return QtGui.QColor(80, 0, 0)  # Darker red

            return QtGui.QColor(30, 30, 30)  # Default dark background

        elif role == QtCore.Qt.ItemDataRole.ForegroundRole:
            # Set text color - use normal text color for all cells
            return QtGui.QColor(THEME_COLORS['text'])

        return None

    def headerData(self, section, orientation, role=QtCore.Qt.ItemDataRole.DisplayRole):
        if role == QtCore.Qt.ItemDataRole.DisplayRole:
            if orientation == QtCore.Qt.Orientation.Horizontal:
                return str(self._columns[section])
            else:
                return str(section + 1)

        elif role == QtCore.Qt.ItemDataRole.FontRole:
            font = QtGui.QFont()
            font.setBold(True)
            return font

        return None

    def setData(self, data, is_bullish_cycle=None):
        """Update the model with new data

        Args:
            data: DataFrame with the data to display
            is_bullish_cycle: Dictionary mapping row indices to boolean values indicating if the row is in a bullish cycle
        """
        self.beginResetModel()
        self._data = data if data is not None else pd.DataFrame()
        if not self._data.empty:
            self._columns = self._data.columns.tolist()
        else:
            self._columns = []

        # Update the bullish cycle tracking dictionary
        if is_bullish_cycle is not None:
            self._is_bullish_cycle = is_bullish_cycle
        else:
            self._is_bullish_cycle = {}

        self.endResetModel()
        return True


class DataTab(QtWidgets.QWidget):
    """
    Tab for displaying rebased OHLC data in a table format.
    """
    def __init__(self, parent=None, market_odds_tab=None):
        """
        Initialize the data tab.

        Args:
            parent: Parent widget
            market_odds_tab: Reference to the Market Odds tab
        """
        super().__init__(parent)

        # Store reference to Market Odds tab
        self.market_odds_tab = market_odds_tab

        # Set default calculation mode
        self.calculation_mode = "current_price"

        # Initialize state for historical pivots
        self.show_historical_pivots = False

        # Initialize UI
        self.init_ui()

    def init_ui(self):
        """Initialize the user interface."""
        # Main layout
        main_layout = QtWidgets.QVBoxLayout(self)
        main_layout.setContentsMargins(10, 10, 10, 10)

        # Add header with title and controls
        header_layout = QtWidgets.QHBoxLayout()

        # Title
        title_label = QtWidgets.QLabel("Rebased OHLC Data")
        title_label.setStyleSheet("font-size: 16px; font-weight: bold; color: #FFFFFF;")
        header_layout.addWidget(title_label)

        # Add spacer to push controls to the right
        header_layout.addStretch()

        # Create calculation mode button group
        self.calc_mode_group = QtWidgets.QButtonGroup(self)

        # Radio button style
        radio_style = """
            QRadioButton {
                color: #FFFFFF;
                spacing: 5px;
            }
            QRadioButton::indicator {
                width: 15px;
                height: 15px;
            }
            QRadioButton::indicator:checked {
                background-color: #a0a0a0;
                border: 2px solid #FFFFFF;
                border-radius: 7px;
            }
            QRadioButton::indicator:unchecked {
                background-color: #2A2A2A;
                border: 2px solid #AAAAAA;
                border-radius: 7px;
            }
        """

        # Create mode selection buttons
        mode_layout = QtWidgets.QHBoxLayout()
        mode_layout.setSpacing(10)

        # Current Price button (first in order and default selected)
        self.current_price_btn = QtWidgets.QRadioButton("Current Price")
        self.current_price_btn.setStyleSheet(radio_style)
        self.current_price_btn.setChecked(True)  # Default selected
        self.calc_mode_group.addButton(self.current_price_btn)
        mode_layout.addWidget(self.current_price_btn)

        # Percentage Based button
        self.percentage_based_btn = QtWidgets.QRadioButton("Percentage Based")
        self.percentage_based_btn.setStyleSheet(radio_style)
        self.calc_mode_group.addButton(self.percentage_based_btn)
        mode_layout.addWidget(self.percentage_based_btn)

        # Extrema Price button (renamed from Pivot Price)
        self.pivot_price_btn = QtWidgets.QRadioButton("Extrema Price")
        self.pivot_price_btn.setStyleSheet(radio_style)
        self.calc_mode_group.addButton(self.pivot_price_btn)
        mode_layout.addWidget(self.pivot_price_btn)

        # OHLC button (renamed from Cycle Pivot)
        self.cycle_pivot_btn = QtWidgets.QRadioButton("OHLC")
        self.cycle_pivot_btn.setStyleSheet(radio_style)
        self.calc_mode_group.addButton(self.cycle_pivot_btn)
        mode_layout.addWidget(self.cycle_pivot_btn)

        # Connect signals
        self.percentage_based_btn.toggled.connect(self.on_calculation_mode_changed)
        self.current_price_btn.toggled.connect(self.on_calculation_mode_changed)
        self.pivot_price_btn.toggled.connect(self.on_calculation_mode_changed)
        self.cycle_pivot_btn.toggled.connect(self.on_calculation_mode_changed)

        # Add mode layout to header
        header_layout.addLayout(mode_layout)

        # Add a small spacer
        header_layout.addSpacing(10)

        # The Line Prices button (renamed from Vector Prices)
        self.historical_pivots_btn = QtWidgets.QRadioButton("The Line Prices")
        self.historical_pivots_btn.setStyleSheet(radio_style)
        self.calc_mode_group.addButton(self.historical_pivots_btn)
        mode_layout.addWidget(self.historical_pivots_btn)

        # Connect signal
        self.historical_pivots_btn.toggled.connect(self.on_calculation_mode_changed)

        # Add a small spacer
        header_layout.addSpacing(10)

        # Refresh button
        self.refresh_button = QtWidgets.QPushButton("Refresh")
        self.refresh_button.setStyleSheet("""
            QPushButton {
                background-color: #000000;
                color: #FFFFFF;
                border: 1px solid #333333;
                border-radius: 3px;
                padding: 5px 10px;
            }
            QPushButton:hover {
                background-color: #333333;
                border: 1px solid #555555;
            }
            QPushButton:pressed {
                background-color: #111111;
                border: 1px solid #444444;
            }
        """)
        self.refresh_button.clicked.connect(self.refresh_data)
        header_layout.addWidget(self.refresh_button)

        # Add header to main layout
        main_layout.addLayout(header_layout)

        # Status label
        self.status_label = QtWidgets.QLabel("No data loaded")
        self.status_label.setStyleSheet("color: #AAAAAA;")
        main_layout.addWidget(self.status_label)

        # Create table view
        self.table_view = QtWidgets.QTableView()
        self.table_view.setStyleSheet("""
            QTableView {
                background-color: #1E1E1E;
                color: #FFFFFF;
                gridline-color: #555555;
                border: 1px solid #555555;
                selection-background-color: #a0a0a0;
                selection-color: #FFFFFF;
            }
            QHeaderView::section {
                background-color: #2A2A2A;
                color: #FFFFFF;
                padding: 5px;
                border: 1px solid #555555;
            }
            QScrollBar:vertical {
                background-color: #2A2A2A;
                width: 12px;
                border: 1px solid #555555;
                border-radius: 6px;
            }
            QScrollBar::handle:vertical {
                background-color: #808080;
                border: 1px solid #555555;
                border-radius: 5px;
                min-height: 20px;
            }
            QScrollBar::handle:vertical:hover {
                background-color: #909090;
            }
            QScrollBar::handle:vertical:pressed {
                background-color: #707070;
            }
            QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {
                background: none;
                border: none;
            }
            QScrollBar::add-page:vertical, QScrollBar::sub-page:vertical {
                background: none;
            }
            QScrollBar:horizontal {
                background-color: #2A2A2A;
                height: 12px;
                border: 1px solid #555555;
                border-radius: 6px;
            }
            QScrollBar::handle:horizontal {
                background-color: #808080;
                border: 1px solid #555555;
                border-radius: 5px;
                min-width: 20px;
            }
            QScrollBar::handle:horizontal:hover {
                background-color: #909090;
            }
            QScrollBar::handle:horizontal:pressed {
                background-color: #707070;
            }
            QScrollBar::add-line:horizontal, QScrollBar::sub-line:horizontal {
                background: none;
                border: none;
            }
            QScrollBar::add-page:horizontal, QScrollBar::sub-page:horizontal {
                background: none;
            }
        """)

        # Configure table view
        self.table_view.setAlternatingRowColors(True)
        self.table_view.setSortingEnabled(True)
        self.table_view.setSelectionBehavior(QtWidgets.QAbstractItemView.SelectionBehavior.SelectRows)

        # Set all columns to be evenly spread
        self.table_view.horizontalHeader().setSectionResizeMode(QtWidgets.QHeaderView.ResizeMode.Stretch)
        self.table_view.verticalHeader().setDefaultSectionSize(25)

        # Create and set model
        self.table_model = OHLCTableModel()
        self.table_view.setModel(self.table_model)

        # Add table to main layout
        main_layout.addWidget(self.table_view)

    def get_current_symbol(self):
        """Get the current symbol from the Market Odds tab"""
        market_odds_tab = self.market_odds_tab or self.get_market_odds_tab()

        if market_odds_tab and hasattr(market_odds_tab, 'symbol_input'):
            return market_odds_tab.symbol_input.text().strip().upper()

        return None

    def get_current_price(self):
        """Get the current price (last close) from the Market Odds tab"""
        market_odds_tab = self.market_odds_tab or self.get_market_odds_tab()

        if market_odds_tab and hasattr(market_odds_tab, 'data') and market_odds_tab.data is not None and not market_odds_tab.data.empty:
            return market_odds_tab.data['Close'].iloc[-1]

        return None

    def get_market_odds_tab(self):
        """Get the Market Odds tab from the parent window"""
        # Try to find the main window by traversing up the parent hierarchy
        print("Searching for market_odds_tab in parent hierarchy...")
        parent = self.parent()
        parent_level = 1

        while parent is not None:
            print(f"Parent level {parent_level}: {type(parent).__name__}")
            if hasattr(parent, 'market_odds_tab'):
                print(f"Found market_odds_tab in parent level {parent_level}")
                return parent.market_odds_tab
            parent = parent.parent()
            parent_level += 1

        # If we couldn't find it by traversing parents, try to get it from QApplication
        print("Searching for market_odds_tab in top level widgets...")
        from PyQt6.QtWidgets import QApplication
        for i, widget in enumerate(QApplication.topLevelWidgets()):
            print(f"Top level widget {i}: {type(widget).__name__}")
            if hasattr(widget, 'market_odds_tab'):
                print(f"Found market_odds_tab in top level widget {i}")
                return widget.market_odds_tab

        # If we still couldn't find it, try to find the MainWindow instance
        print("Searching for MainWindow instance in top level widgets...")
        for i, widget in enumerate(QApplication.topLevelWidgets()):
            if type(widget).__name__ == 'MainWindow':
                print(f"Found MainWindow in top level widget {i}")
                if hasattr(widget, 'market_odds_tab'):
                    print("MainWindow has market_odds_tab attribute")
                    return widget.market_odds_tab
                else:
                    print("MainWindow does not have market_odds_tab attribute")

        # If we still couldn't find it, print a debug message
        print("Could not find market_odds_tab in parent hierarchy or top level widgets")
        return None



    def get_volatility_statistics_tab(self):
        """Get the Volatility Statistics tab from the parent window"""
        # Try to find the main window by traversing up the parent hierarchy
        print("Searching for Volatility_Statistics_tab in parent hierarchy...")
        parent = self.parent()
        parent_level = 1

        while parent is not None:
            print(f"Parent level {parent_level}: {type(parent).__name__}")
            if hasattr(parent, 'Volatility_Statistics_tab'):
                print(f"Found Volatility_Statistics_tab in parent level {parent_level}")
                return parent.Volatility_Statistics_tab
            parent = parent.parent()
            parent_level += 1

        # If we couldn't find it by traversing parents, try to get it from QApplication
        print("Searching for Volatility_Statistics_tab in top level widgets...")
        from PyQt6.QtWidgets import QApplication
        for i, widget in enumerate(QApplication.topLevelWidgets()):
            print(f"Top level widget {i}: {type(widget).__name__}")
            if hasattr(widget, 'Volatility_Statistics_tab'):
                print(f"Found Volatility_Statistics_tab in top level widget {i}")
                return widget.Volatility_Statistics_tab

        # If we still couldn't find it, try to find the MainWindow instance
        print("Searching for MainWindow instance in top level widgets...")
        for i, widget in enumerate(QApplication.topLevelWidgets()):
            if type(widget).__name__ == 'MainWindow':
                print(f"Found MainWindow in top level widget {i}")
                if hasattr(widget, 'Volatility_Statistics_tab'):
                    print("MainWindow has Volatility_Statistics_tab attribute")
                    return widget.Volatility_Statistics_tab
                else:
                    print("MainWindow does not have Volatility_Statistics_tab attribute")

        # If we still couldn't find it, print a debug message
        print("Could not find Volatility_Statistics_tab in parent hierarchy or top level widgets")
        return None

    def refresh_data(self, *args):
        """
        Refresh the data table with the latest market data.

        This method can handle different signal signatures:
        - refresh_data() - Called directly, uses market_odds_tab reference
        - refresh_data(symbol, timeframe, days_to_load) - Called from market_odds.py data_fetched signal
        - refresh_data(symbol, data) - Called from universal_controls.py data_fetched signal
        """
        # Check if we're being called with data directly from universal_controls
        if len(args) == 2 and isinstance(args[1], pd.DataFrame):
            # We have data directly from universal_controls
            symbol = args[0]
            data = args[1]

            # Create a temporary object to use instead of market_odds_tab
            class TempMarketOddsTab:
                def __init__(self, data, symbol):
                    self.data = data
                    self.symbol_input = type('obj', (object,), {'text': lambda: symbol})
                    self.timeframe_combo = type('obj', (object,), {'currentText': lambda: 'Unknown'})

            market_odds_tab = TempMarketOddsTab(data, symbol)
        else:
            # Use the stored reference to Market Odds tab
            market_odds_tab = self.market_odds_tab
            print(f"Market odds tab found: {market_odds_tab is not None}")

            if market_odds_tab is None:
                # Try to get it from the parent hierarchy as a fallback
                market_odds_tab = self.get_market_odds_tab()
                print(f"Market odds tab found via parent hierarchy: {market_odds_tab is not None}")

            if market_odds_tab is None:
                self.status_label.setText("Error: Market Odds tab not found")
                print("Error: Market Odds tab not found")
                return

        # Check if data is available
        if not hasattr(market_odds_tab, 'data') or market_odds_tab.data is None or market_odds_tab.data.empty:
            self.status_label.setText("Error: No data available in Market Odds tab")
            return

        # Get the current price and pivot price
        current_price = None
        pivot_price = None

        # Initialize dictionaries to track cycle information
        is_bullish_cycle = {}  # Whether each candle is in a bullish or bearish cycle
        cycle_positions = {}    # Position of each candle within its cycle
        cycle_pivot_prices = {}  # Pivot price for each candle based on its cycle
        updated_values = {}     # Track which theoretical value was updated for each candle

        # Get the Volatility Statistics tab to check the selected matching mode
        volatility_statistics_tab = self.get_volatility_statistics_tab()
        matching_mode = None
        if volatility_statistics_tab is not None and hasattr(volatility_statistics_tab, 'get_matching_mode'):
            matching_mode = volatility_statistics_tab.get_matching_mode()
            print(f"Using matching mode from Volatility Statistics tab: {matching_mode}")

        # Try to get the current price (last close price or historical cutoff price)
        if hasattr(market_odds_tab, 'data') and market_odds_tab.data is not None and not market_odds_tab.data.empty:
            # Check if we're viewing historical data and should use historical cutoff
            if hasattr(market_odds_tab, 'historical_cutoff_index'):
                historical_cutoff_index = market_odds_tab.historical_cutoff_index
                current_price = market_odds_tab.data['Close'].iloc[historical_cutoff_index]
                print(f"Using historical reference price (index {historical_cutoff_index}): {current_price}")
            else:
                current_price = market_odds_tab.data['Close'].iloc[-1]

        # Try to get the pivot price
        if hasattr(market_odds_tab, 'current_pivot') and market_odds_tab.current_pivot is not None:
            pivot_price = market_odds_tab.current_pivot
        else:
            # If no pivot price is available, use the current price
            pivot_price = current_price

        if current_price is None or pivot_price is None:
            self.status_label.setText("Error: Could not determine current price or pivot price")
            return

        # Get the rebased data from market odds tab
        if hasattr(market_odds_tab, 'rebased_data') and market_odds_tab.rebased_data is not None and len(market_odds_tab.rebased_data) > 0:
            # Extract only OHLC columns from rebased data
            rebased_data = []

            # Process data based on calculation mode
            if self.calculation_mode == "percentage_based":
                # Use the original percentage-based values
                for candle in market_odds_tab.rebased_data:
                    # Each candle is (t, open, high, low, close)
                    rebased_data.append(candle)

            elif self.calculation_mode == "current_price":
                # We'll use the real OHLC data from the original data
                # and calculate the $ change and % change values
                cycle_pivot_values = []

                # First, get the real OHLC data
                if hasattr(market_odds_tab, 'data') and market_odds_tab.data is not None and not market_odds_tab.data.empty:
                    # Use the original data to populate cycle_pivot_values
                    for i, (idx, row) in enumerate(market_odds_tab.data.iterrows()):
                        if i < len(market_odds_tab.rebased_data):
                            t = market_odds_tab.rebased_data[i][0]  # Get the index from rebased_data

                            # Use the actual OHLC values from the original data
                            cp_values = {
                                'index': t,
                                'open': row['Open'],
                                'high': row['High'],
                                'low': row['Low'],
                                'close': row['Close'],
                                'is_bullish': True  # Default to bullish, will update later
                            }

                            cycle_pivot_values.append(cp_values)

                # Now determine bullish/bearish cycles
                if hasattr(market_odds_tab, 'crossing_points') and hasattr(market_odds_tab, 'below_vector'):
                    # Get the crossing points and below_vector status from market_odds_tab
                    crossing_points = market_odds_tab.crossing_points
                    current_is_below = market_odds_tab.below_vector

                    # Sort crossing points for processing
                    sorted_crossings = sorted(crossing_points)

                    # First, identify all cycle segments (same as other tabs and vector price tab)
                    cycle_segments = []
                    current_is_bullish = not current_is_below
                    segment_start_idx = 0

                    # Add a segment for each cycle between crossings
                    for i, crossing in enumerate(sorted_crossings):
                        # Get the price BEFORE this crossing
                        vector_price = None
                        if hasattr(market_odds_tab, 'data') and crossing < len(market_odds_tab.data):
                            # Vector calculation has been removed, use closing price instead
                            if crossing > 0:
                                vector_price = market_odds_tab.data['Close'].iloc[crossing - 1]
                                print(f"Current Price: Using close price at index {crossing - 1} for crossing at {crossing}")
                            else:
                                # For the first crossing, use the crossing point itself
                                vector_price = market_odds_tab.data['Close'].iloc[crossing]
                                print(f"Current Price: First crossing at {crossing}, using price at crossing")
                        else:
                            # Fallback to current price if data is not available
                            if hasattr(market_odds_tab, 'data') and not market_odds_tab.data.empty:
                                vector_price = market_odds_tab.data['Close'].iloc[-1]
                            else:
                                vector_price = pivot_price

                        # Add the segment up to this crossing
                        # For bullish cycles, end at the candle just before the crossing (crossing - 1)
                        # For bearish cycles, end at the candle just before the crossing (crossing - 1)
                        # The first cycle is a special case
                        end_idx = crossing
                        if i > 0:  # Not the first cycle
                            if current_is_bullish:  # Current cycle is bullish
                                # Bullish cycle ends at the candle just before the crossing
                                end_idx = crossing - 1 if crossing > 0 else 0
                            else:  # Current cycle is bearish
                                # Bearish cycle ends at the candle just before the crossing
                                end_idx = crossing - 1 if crossing > 0 else 0

                        cycle_segments.append({
                            'start_idx': segment_start_idx,
                            'end_idx': end_idx,
                            'is_bullish': current_is_bullish,
                            'vector_price': vector_price
                        })

                        # Debug output for cycle segment
                        print(f"Current Price: Cycle Segment: Indices {segment_start_idx}-{end_idx}, " +
                              f"Is bullish: {current_is_bullish}, Vector Price: {vector_price:.2f}")

                        # Update for next segment
                        # The next cycle starts at the crossing point
                        segment_start_idx = crossing
                        current_is_bullish = not current_is_bullish

                    # Add the final segment after the last crossing
                    final_idx = len(market_odds_tab.data) - 1 if hasattr(market_odds_tab, 'data') and market_odds_tab.data is not None and not market_odds_tab.data.empty else segment_start_idx

                    # Get the vector price for the final segment
                    final_vector_price = None
                    if hasattr(market_odds_tab, 'data') and segment_start_idx < len(market_odds_tab.data):
                        if hasattr(market_odds_tab, 'calculate_vector') and hasattr(market_odds_tab, 'vector_length_spin'):
                            vector = market_odds_tab.calculate_vector(market_odds_tab.data, market_odds_tab.vector_length_spin.value())
                            if segment_start_idx > 0:
                                final_vector_price = vector.iloc[segment_start_idx - 1]  # Get price BEFORE crossing
                            else:
                                final_vector_price = vector.iloc[segment_start_idx]
                        else:
                            if segment_start_idx > 0:
                                final_vector_price = market_odds_tab.data['Close'].iloc[segment_start_idx - 1]
                            else:
                                final_vector_price = market_odds_tab.data['Close'].iloc[segment_start_idx]
                    else:
                        final_vector_price = current_price

                    # The final segment starts at the last crossing point and continues to the end
                    # No need to adjust the end_idx for the final segment as it's already the last candle
                    cycle_segments.append({
                        'start_idx': segment_start_idx,
                        'end_idx': final_idx,
                        'is_bullish': current_is_bullish,
                        'vector_price': final_vector_price
                    })

                    # Debug output for final cycle segment
                    print(f"Current Price: Final Cycle Segment: Indices {segment_start_idx}-{final_idx}, " +
                          f"Is bullish: {current_is_bullish}, Vector Price: {final_vector_price:.2f}")

                    # Now calculate the cycle pivot values for each candle
                    for i, candle in enumerate(market_odds_tab.rebased_data):
                        t = candle[0]  # This is the original index in the data

                        # Find which segment this candle belongs to
                        segment = None
                        for j, seg in enumerate(cycle_segments):
                            if seg['start_idx'] <= t <= seg['end_idx']:
                                segment = seg
                                print(f"Current Price: Candle {i} (idx {t}) belongs to segment {j} (idx range {seg['start_idx']}-{seg['end_idx']})")
                                break

                        if segment is None:
                            # Fallback to using the current price if no segment is found
                            vector_price = current_price
                            is_bullish = not current_is_below  # Default to the current cycle state
                            print(f"Current Price: No segment found for candle {i} (idx {t}), using current price {current_price:.2f}")
                        else:
                            # Use the vector price from the segment
                            vector_price = segment['vector_price']
                            is_bullish = segment['is_bullish']
                            print(f"Current Price: Using vector price {vector_price:.2f} for candle {i} (idx {t}), is_bullish: {is_bullish}")

                        # Get percentage adjustment from the percentage-based tab
                        # The percentage value is in the candle data (e.g., 0.9 means increase by 0.9%)
                        percentage_adjustment = candle[4]  # Using the close value as the percentage
                        adjusted_vector_price = vector_price * (1 + percentage_adjustment / 100)
                        print(f"Current Price: Adjusted vector price for candle {i}: {vector_price:.2f} + {percentage_adjustment:.2f}% = {adjusted_vector_price:.2f}")

                        # Calculate cycle pivot values using the adjusted vector price
                        cp_open = adjusted_vector_price + (adjusted_vector_price / 100 * candle[1])
                        cp_high = adjusted_vector_price + (adjusted_vector_price / 100 * candle[2])
                        cp_low = adjusted_vector_price + (adjusted_vector_price / 100 * candle[3])
                        cp_close = adjusted_vector_price + (adjusted_vector_price / 100 * candle[4])

                        # Store the cycle pivot values for this candle
                        cycle_pivot_values.append({
                            'index': t,
                            'open': cp_open,
                            'high': cp_high,
                            'low': cp_low,
                            'close': cp_close,
                            'is_bullish': is_bullish
                        })

                    # Now calculate the current price values using the cycle pivot values
                    for i, candle in enumerate(market_odds_tab.rebased_data):
                        t = candle[0]

                        # Get the cycle pivot values for this candle
                        cp_values = cycle_pivot_values[i]

                        # Apply the new formulas for high and low values
                        # High %: (cycle pivot close + vector value - 2*cycle pivot low)/ current price * 100
                        # Low %: (2* cycle pivot close - cycle pivot high - vector value)/current price * 100

                        # We no longer need the vector value for the new formula

                        # Calculate the high and low values using the new, even simpler approach

                        # Define variables for clarity
                        CP = current_price       # current price
                        CPH = cp_values['high']  # cycle pivot high
                        CPL = cp_values['low']   # cycle pivot low
                        CPC = cp_values['close'] # cycle pivot close

                        # Debug output of input values
                        print(f"Current Price: Candle {i} (idx {t}): CP={CP:.2f}, CPH={CPH:.2f}, CPL={CPL:.2f}, CPC={CPC:.2f}")

                        # Calculate high and low values using the new formulas
                        # current price high = current price * cycle pivot high / cycle pivot close
                        # current price low = current price * cycle pivot low / cycle pivot close

                        # Avoid division by zero
                        if CPC != 0:
                            high_val = CP * (CPH / CPC)
                            low_val = CP * (CPL / CPC)
                        else:
                            high_val = CP  # fallback to current price if CPC is zero
                            low_val = CP   # fallback to current price if CPC is zero

                        # Calculate dollar change
                        # For any given day (e.g., Monday), we use:
                        # $ change high = next day cycle pivot high (Tuesday's high) - current day cycle pivot close (Monday's close)
                        # $ change low = next day cycle pivot low (Tuesday's low) - current day cycle pivot close (Monday's close)

                        # Get the FWL Aggr. value from the Volatility Statistics tab if available
                        fwl_aggr = 1  # Default to 1 day forward

                        # Try to find the Volatility Statistics tab using our helper method
                        volatility_statistics_tab = self.get_volatility_statistics_tab()

                        # If we found the tab and it has the get_volatility_factor method, use it
                        if volatility_statistics_tab is not None and hasattr(volatility_statistics_tab, 'get_volatility_factor'):
                            fwl_aggr = volatility_statistics_tab.get_volatility_factor()
                            print(f"Using FWL Aggr. value: {fwl_aggr}")

                        # Look forward by fwl_aggr days to get the high/low values
                        if i < len(market_odds_tab.rebased_data) - fwl_aggr:
                            # Get the candle that is fwl_aggr days ahead
                            target_candle = market_odds_tab.rebased_data[i + fwl_aggr]
                            target_idx = target_candle[0]

                            # Find the cycle pivot values for the target candle
                            target_cp_values = None
                            for j, cp_val in enumerate(cycle_pivot_values):
                                if cp_val['index'] == target_idx:
                                    target_cp_values = cp_val
                                    break

                            if target_cp_values:
                                # Check if the target day is a peak or trough
                                target_idx_value = target_candle[0]
                                is_peak = False
                                is_trough = False

                                # Check if this index is in the percentage_based_peaks_troughs dictionary
                                # This dictionary will be populated later, so we need to pre-check it
                                # We'll use a temporary approach to determine peaks and troughs

                                # Check for peak or trough
                                if hasattr(market_odds_tab, 'rebased_data'):
                                    for j, candle in enumerate(market_odds_tab.rebased_data):
                                        if candle[0] == target_idx_value:
                                            # Check if this is a peak or trough based on the candle pattern
                                            if j > 0 and j < len(market_odds_tab.rebased_data) - 1:
                                                prev_candle = market_odds_tab.rebased_data[j-1]
                                                next_candle = market_odds_tab.rebased_data[j+1]

                                                # Check for peak: current high > previous high and current high > next high
                                                if candle[2] > prev_candle[2] and candle[2] > next_candle[2]:
                                                    is_peak = True
                                                    print(f"Detected peak at index {target_idx_value}")

                                                # Check for trough: current low < previous low and current low < next low
                                                if candle[3] < prev_candle[3] and candle[3] < next_candle[3]:
                                                    is_trough = True
                                                    print(f"Detected trough at index {target_idx_value}")
                                            break

                                # Get default 1-day values (for the parts we don't want to change)
                                default_high_dollar_change = None
                                default_low_dollar_change = None

                                if i < len(market_odds_tab.rebased_data) - 1:
                                    next_candle = market_odds_tab.rebased_data[i + 1]
                                    next_idx = next_candle[0]

                                    # Find the cycle pivot values for the next candle (1 day ahead)
                                    next_cp_values = None
                                    for j, cp_val in enumerate(cycle_pivot_values):
                                        if cp_val['index'] == next_idx:
                                            next_cp_values = cp_val
                                            break

                                    if next_cp_values:
                                        default_high_dollar_change = next_cp_values['high'] - cp_values['close']
                                        default_low_dollar_change = next_cp_values['low'] - cp_values['close']

                                # If we couldn't get default values, use the target values
                                if default_high_dollar_change is None:
                                    default_high_dollar_change = target_cp_values['high'] - cp_values['close']
                                if default_low_dollar_change is None:
                                    default_low_dollar_change = target_cp_values['low'] - cp_values['close']

                                # New implementation: Look forward to find nearest peak and trough
                                # Initialize default values
                                th_value = 1  # Default TH value
                                tl_value = 1  # Default TL value

                                # Check if current day is a peak or trough
                                current_is_peak = False
                                current_is_trough = False

                                # Check if the current candle is a peak or trough
                                if i > 0 and i < len(market_odds_tab.rebased_data) - 1:
                                    prev_candle_data = market_odds_tab.rebased_data[i-1]
                                    current_candle_data = market_odds_tab.rebased_data[i]
                                    next_candle_data = market_odds_tab.rebased_data[i+1]

                                    # Check for peak: current high > previous high and current high > next high
                                    if current_candle_data[2] > prev_candle_data[2] and current_candle_data[2] > next_candle_data[2]:
                                        current_is_peak = True
                                        print(f"Current day is a peak (high: {current_candle_data[2]} > prev: {prev_candle_data[2]} and next: {next_candle_data[2]})")

                                    # Check for trough: current low < previous low and current low < next low
                                    if current_candle_data[3] < prev_candle_data[3] and current_candle_data[3] < next_candle_data[3]:
                                        current_is_trough = True
                                        print(f"Current day is a trough (low: {current_candle_data[3]} < prev: {prev_candle_data[3]} and next: {next_candle_data[3]})")

                                    # Also check if the current day is in the percentage_based_peaks_troughs dictionary
                                    current_idx = current_candle_data[0]
                                    if hasattr(market_odds_tab, 'percentage_based_peaks_troughs') and current_idx in market_odds_tab.percentage_based_peaks_troughs:
                                        peak_trough_type = market_odds_tab.percentage_based_peaks_troughs[current_idx]
                                        if peak_trough_type == "peak":
                                            current_is_peak = True
                                            print(f"Current day is marked as a peak in percentage_based_peaks_troughs")
                                        elif peak_trough_type == "trough":
                                            current_is_trough = True
                                            print(f"Current day is marked as a trough in percentage_based_peaks_troughs")

                                # Look forward to find the nearest peak and trough
                                days_to_peak = None
                                days_to_trough = None

                                # If current day is already a peak/trough, set the corresponding value to 1
                                if current_is_peak:
                                    days_to_peak = 0  # Current day (will be adjusted to 1 later)
                                    print(f"Current day is a peak, setting days_to_peak to 0 (will be adjusted to 1)")

                                if current_is_trough:
                                    days_to_trough = 0  # Current day (will be adjusted to 1 later)
                                    print(f"Current day is a trough, setting days_to_trough to 0 (will be adjusted to 1)")

                                # Look forward up to fwl_aggr days to find peaks and troughs
                                for days_ahead in range(1, fwl_aggr + 1):
                                    if i + days_ahead < len(market_odds_tab.rebased_data):
                                        # Check if this future candle is a peak or trough
                                        future_idx = i + days_ahead

                                        if future_idx > 0 and future_idx < len(market_odds_tab.rebased_data) - 1:
                                            prev_future = market_odds_tab.rebased_data[future_idx-1]
                                            current_future = market_odds_tab.rebased_data[future_idx]
                                            next_future = market_odds_tab.rebased_data[future_idx+1]

                                            # Check for peak
                                            if current_future[2] > prev_future[2] and current_future[2] > next_future[2]:
                                                if days_to_peak is None:
                                                    days_to_peak = days_ahead
                                                    print(f"Found peak {days_ahead} days ahead")

                                            # Check for trough
                                            if current_future[3] < prev_future[3] and current_future[3] < next_future[3]:
                                                if days_to_trough is None:
                                                    days_to_trough = days_ahead
                                                    print(f"Found trough {days_ahead} days ahead")

                                # Adjust days_to_peak and days_to_trough
                                # Only add 1 if the current day is a peak/trough (to make it 1-based)
                                # Otherwise, use the value as is (starting count at 0 for the original day)
                                if days_to_peak is not None:
                                    # Only add 1 if the current day is a peak
                                    if current_is_peak:
                                        days_to_peak += 1
                                        print(f"Current day is a peak, adjusting days_to_peak from {days_to_peak-1} to {days_to_peak}")

                                    if days_to_peak > fwl_aggr:
                                        days_to_peak = fwl_aggr
                                else:
                                    days_to_peak = fwl_aggr  # If no peak found, use maximum FWL Aggr value

                                if days_to_trough is not None:
                                    # Only add 1 if the current day is a trough
                                    if current_is_trough:
                                        days_to_trough += 1
                                        print(f"Current day is a trough, adjusting days_to_trough from {days_to_trough-1} to {days_to_trough}")

                                    if days_to_trough > fwl_aggr:
                                        days_to_trough = fwl_aggr
                                else:
                                    days_to_trough = fwl_aggr  # If no trough found, use maximum FWL Aggr value

                                # Set the TH and TL values
                                th_value = days_to_peak
                                tl_value = days_to_trough

                                print(f"TH value: {th_value}, TL value: {tl_value}")

                                # Now get the high and low values from the appropriate future candles
                                # For TH:1, use the next day (i+1)
                                # For TH:2, use 2 days ahead (i+2)
                                # For TH:3, use 3 days ahead (i+3)
                                th_idx = i + th_value
                                tl_idx = i + tl_value

                                # Get the high value for theoretical high
                                if th_idx < len(cycle_pivot_values):
                                    th_cp_values = None
                                    for cp_val in cycle_pivot_values:
                                        if cp_val['index'] == market_odds_tab.rebased_data[th_idx][0]:
                                            th_cp_values = cp_val
                                            break

                                    if th_cp_values:
                                        # Calculate dollar change as: future high - current close
                                        high_dollar_change = th_cp_values['high'] - cp_values['close']
                                        print(f"Using high from {th_value} days ahead (index {th_idx}) for theoretical high")
                                        print(f"Current day index: {i}, Current day close: {cp_values['close']:.2f}")
                                        print(f"Future day index: {th_idx}, Future day high: {th_cp_values['high']:.2f}")
                                        print(f"Calculation: {th_cp_values['high']:.2f} (day {th_value} high) - {cp_values['close']:.2f} (current close) = {high_dollar_change:.2f}")
                                    else:
                                        # Fallback to default
                                        high_dollar_change = default_high_dollar_change
                                        print(f"Could not find high value {th_value} days ahead (index {th_idx}), using default")
                                else:
                                    # Fallback to default
                                    high_dollar_change = default_high_dollar_change
                                    print(f"Index {th_idx} out of range, using default high value")

                                # Get the low value for theoretical low
                                if tl_idx < len(cycle_pivot_values):
                                    tl_cp_values = None
                                    for cp_val in cycle_pivot_values:
                                        if cp_val['index'] == market_odds_tab.rebased_data[tl_idx][0]:
                                            tl_cp_values = cp_val
                                            break

                                    if tl_cp_values:
                                        # Calculate dollar change as: future low - current close
                                        low_dollar_change = tl_cp_values['low'] - cp_values['close']
                                        print(f"Using low from {tl_value} days ahead (index {tl_idx}) for theoretical low")
                                        print(f"Current day index: {i}, Current day close: {cp_values['close']:.2f}")
                                        print(f"Future day index: {tl_idx}, Future day low: {tl_cp_values['low']:.2f}")
                                        print(f"Calculation: {tl_cp_values['low']:.2f} (day {tl_value} low) - {cp_values['close']:.2f} (current close) = {low_dollar_change:.2f}")
                                    else:
                                        # Fallback to default
                                        low_dollar_change = default_low_dollar_change
                                        print(f"Could not find low value {tl_value} days ahead (index {tl_idx}), using default")
                                else:
                                    # Fallback to default
                                    low_dollar_change = default_low_dollar_change
                                    print(f"Index {tl_idx} out of range, using default low value")

                                # Format the updated value string
                                updated_value = f"TH: {th_value} | TL: {tl_value}"

                                # Always store the updated value for this candle (regardless of matching mode)
                                updated_values[i] = updated_value
                                print(f"Storing updated value for row {i} (idx {t}): {updated_value}")

                                # Determine if we should update this row based on matching mode
                                should_update = True

                                # If matching mode is set, apply filtering
                                if matching_mode:
                                    # For H/L Matching, only update rows that match the latest category
                                    if matching_mode == 'hl':
                                        # Get the latest category (e.g., "2H", "3L") from the data
                                        latest_category = None

                                        # Get the latest data index
                                        if hasattr(market_odds_tab, 'data') and market_odds_tab.data is not None and not market_odds_tab.data.empty:
                                            last_data_idx = len(market_odds_tab.data) - 1

                                            # Get the original index for the last data point
                                            if last_data_idx < len(market_odds_tab.rebased_data):
                                                last_original_idx = market_odds_tab.rebased_data[last_data_idx][0]

                                                # Check if we have a category for this index in percentage_based_categories
                                                if hasattr(market_odds_tab, 'percentage_based_categories') and last_original_idx in market_odds_tab.percentage_based_categories:
                                                    latest_category = market_odds_tab.percentage_based_categories[last_original_idx]
                                                    print(f"Latest category from percentage_based_categories: {latest_category}")

                                        # If we couldn't get the category from percentage_based_categories, try to determine it from the data
                                        if latest_category is None:
                                            # Try to get the category from the data DataFrame if it exists
                                            if 'data' in locals() and 'Category' in data.columns and len(data) > 0:
                                                latest_category = data.iloc[-1]['Category']
                                                print(f"Latest category from data DataFrame: {latest_category}")
                                            else:
                                                # Fallback: Determine if the last row is bullish or bearish
                                                latest_is_bullish = False  # Default to bearish

                                                # Find the latest cycle segment (the one containing the last data point)
                                                if cycle_segments and hasattr(market_odds_tab, 'data') and market_odds_tab.data is not None and not market_odds_tab.data.empty:
                                                    last_data_idx = len(market_odds_tab.data) - 1

                                                    # Find which segment the last data point belongs to
                                                    for seg in cycle_segments:
                                                        if seg['start_idx'] <= last_data_idx <= seg['end_idx']:
                                                            latest_is_bullish = seg['is_bullish']
                                                            print(f"Latest bullish/bearish status determined from cycle segment: {'bullish' if latest_is_bullish else 'bearish'}")
                                                            break

                                                # Create a default category based on bullish/bearish status
                                                latest_category = "1H" if latest_is_bullish else "1L"
                                                print(f"Created default latest category: {latest_category}")

                                        # Get the category for the current row
                                        current_category = None

                                        # Try to get the category from percentage_based_categories
                                        if hasattr(market_odds_tab, 'percentage_based_categories') and t in market_odds_tab.percentage_based_categories:
                                            current_category = market_odds_tab.percentage_based_categories[t]
                                            print(f"Current category from percentage_based_categories: {current_category}")

                                        # If we couldn't get the category, determine it based on bullish/bearish status
                                        if current_category is None:
                                            current_category = "1H" if is_bullish else "1L"
                                            print(f"Created default current category: {current_category}")

                                        # Check if the categories match
                                        if current_category != latest_category:
                                            should_update = False
                                            print(f"Skipping update for row {i} (idx {t}) - category mismatch: row is {current_category}, latest is {latest_category}")

                                    # For Weekday Matching, only update rows that match the same weekday
                                    elif matching_mode == 'weekday':
                                        # Get the current date from the data
                                        current_date = None
                                        latest_date = None

                                        if hasattr(market_odds_tab, 'data') and t < len(market_odds_tab.data) and hasattr(market_odds_tab.data, 'index'):
                                            current_date = market_odds_tab.data.index[t]

                                            # Get the latest date (last row in the data)
                                            if len(market_odds_tab.data) > 0:
                                                latest_date = market_odds_tab.data.index[-1]

                                        # If we have both dates, compare weekdays
                                        if current_date is not None and latest_date is not None:
                                            current_weekday = current_date.weekday()
                                            latest_weekday = latest_date.weekday()

                                            if current_weekday != latest_weekday:
                                                should_update = False
                                                print(f"Skipping update for row {i} (idx {t}) - weekday mismatch: row is {current_weekday}, latest is {latest_weekday}")
                            else:
                                # Fall back to next day if target day values not found
                                # Find the cycle pivot values for the next candle (1 day ahead)
                                next_candle = market_odds_tab.rebased_data[i + 1]
                                next_idx = next_candle[0]

                                next_cp_values = None
                                for j, cp_val in enumerate(cycle_pivot_values):
                                    if cp_val['index'] == next_idx:
                                        next_cp_values = cp_val
                                        break

                                if next_cp_values:
                                    # $ change high = next day cycle pivot high - current day cycle pivot close
                                    high_dollar_change = next_cp_values['high'] - cp_values['close']
                                    # $ change low = next day cycle pivot low - current day cycle pivot close
                                    low_dollar_change = next_cp_values['low'] - cp_values['close']
                                    print(f"Target day ({fwl_aggr} days ahead) values not found, using next day values")
                                else:
                                    # If we can't find the next day's values, use the current high/low - close
                                    high_dollar_change = cp_values['high'] - cp_values['close']
                                    low_dollar_change = cp_values['low'] - cp_values['close']
                                    print(f"Next day CP values not found, using current: High={cp_values['high']:.2f}, Low={cp_values['low']:.2f}, Close={cp_values['close']:.2f}")

                                # For fallback cases, use default TH:1 | TL:1 format
                                updated_value = "TH: 1 | TL: 1"
                                updated_values[i] = updated_value
                                print(f"Fallback: Updating row {i} with {updated_value}")
                        else:
                            # Fall back to next day if target day is out of range
                            if i < len(market_odds_tab.rebased_data) - 1:
                                next_candle = market_odds_tab.rebased_data[i + 1]
                                next_idx = next_candle[0]

                                next_cp_values = None
                                for j, cp_val in enumerate(cycle_pivot_values):
                                    if cp_val['index'] == next_idx:
                                        next_cp_values = cp_val
                                        break

                                if next_cp_values:
                                    # $ change high = next day cycle pivot high - current day cycle pivot close
                                    high_dollar_change = next_cp_values['high'] - cp_values['close']
                                    # $ change low = next day cycle pivot low - current day cycle pivot close
                                    low_dollar_change = next_cp_values['low'] - cp_values['close']
                                    print(f"Target day ({fwl_aggr} days ahead) out of range, using next day values")
                                else:
                                    # If we can't find the next day's values, use the current high/low - close
                                    high_dollar_change = cp_values['high'] - cp_values['close']
                                    low_dollar_change = cp_values['low'] - cp_values['close']
                                    print(f"Next day CP values not found, using current: High={cp_values['high']:.2f}, Low={cp_values['low']:.2f}, Close={cp_values['close']:.2f}")
                            else:
                                # For the last candle, use the current high/low - close
                                high_dollar_change = cp_values['high'] - cp_values['close']
                                low_dollar_change = cp_values['low'] - cp_values['close']
                                print(f"Last candle, using current: High={cp_values['high']:.2f}, Low={cp_values['low']:.2f}, Close={cp_values['close']:.2f}")

                            # For fallback cases, use default TH:1 | TL:1 format
                            updated_value = "TH: 1 | TL: 1"
                            updated_values[i] = updated_value
                            print(f"Fallback: Updating row {i} with {updated_value}")

                        # Get date information for logging
                        if target_cp_values or (next_cp_values and i < len(market_odds_tab.rebased_data) - 1):
                            # Get the date for the current and target/next candle if available
                            current_date = "Current day"
                            target_date = f"{fwl_aggr} days ahead" if target_cp_values else "Next day"

                            if hasattr(market_odds_tab, 'data') and t < len(market_odds_tab.data):
                                if hasattr(market_odds_tab.data, 'index'):
                                    current_date = str(market_odds_tab.data.index[t])

                                    if target_cp_values and target_idx < len(market_odds_tab.data):
                                        target_date = str(market_odds_tab.data.index[target_idx])
                                    elif next_cp_values and next_idx < len(market_odds_tab.data):
                                        target_date = str(market_odds_tab.data.index[next_idx])

                            # Log the values being used
                            if target_cp_values:
                                print(f"For {current_date}: Using {target_date}'s CP High/Low and {current_date}'s CP Close")
                                print(f"CP values: Target High={target_cp_values['high']:.2f}, Target Low={target_cp_values['low']:.2f}, Current Close={cp_values['close']:.2f}")
                            elif next_cp_values:
                                print(f"For {current_date}: Using {target_date}'s CP High/Low and {current_date}'s CP Close")
                                print(f"CP values: Next High={next_cp_values['high']:.2f}, Next Low={next_cp_values['low']:.2f}, Current Close={cp_values['close']:.2f}")

                            print(f"$ Change High = {high_dollar_change:.2f}")
                            print(f"$ Change Low = {low_dollar_change:.2f}")

                        # Calculate the percentage change for $ change high and $ change low
                        # % change high = ($ change high / current day close) * 100
                        # % change low = ($ change low / current day close) * 100
                        high_percent = 100 * high_dollar_change / cp_values['close'] if cp_values['close'] != 0 else 0
                        low_percent = 100 * low_dollar_change / cp_values['close'] if cp_values['close'] != 0 else 0

                        print(f"High $ Change = {high_dollar_change:.2f}, Low $ Change = {low_dollar_change:.2f}")
                        print(f"High % Change = {high_percent:.2f}%, Low % Change = {low_percent:.2f}%")

                        # Calculate projected high and low values using the new formula
                        # projected high = current price * (1 + %change high)
                        # projected low = current price * (1 + %change low)
                        high_val = CP * (1 + high_percent / 100)
                        low_val = CP * (1 + low_percent / 100)

                        # Round to 2 decimal places to avoid floating-point precision issues
                        high_val = round(high_val, 2)
                        low_val = round(low_val, 2)

                        print(f"Formula: projected high = CP * (1 + high_percent/100) = {CP:.2f} * (1 + {high_percent:.2f}/100) = {high_val:.2f}")
                        print(f"Formula: projected low = CP * (1 + low_percent/100) = {CP:.2f} * (1 + {low_percent:.2f}/100) = {low_val:.2f}")

                        print(f"Current Price: Final High: {high_val:.2f}, Final Low: {low_val:.2f}")

                        # For open and close, use the cycle pivot values
                        open_val = cp_values['open']
                        close_val = cp_values['close']

                        # Debug output
                        print(f"Current Price: Candle {t}: Current={current_price:.2f}, " +
                              f"CP High={cp_values['high']:.2f}, CP Low={cp_values['low']:.2f}, CP Close={cp_values['close']:.2f}, " +
                              f"New High={high_val:.2f}, New Low={low_val:.2f}")

                        # Get the updated value for this candle
                        updated_value = updated_values.get(i, "None")

                        # Round dollar changes to 2 decimal places to avoid floating-point precision issues
                        high_dollar_change = round(high_dollar_change, 2)
                        low_dollar_change = round(low_dollar_change, 2)

                        # Store the data with additional columns for $ change and % change
                        # We'll store them in the Open and Close positions temporarily, then rearrange later
                        rebased_data.append([t, high_dollar_change, high_val, low_val, low_dollar_change, updated_value])

                        # Set bullish/bearish cycle information
                        is_bullish_cycle[len(rebased_data) - 1] = cp_values['is_bullish']
                else:
                    # If no cycle information is available, fall back to using the current pivot price
                    print("Current Price: No cycle information available, using current pivot price for all candles")
                    for candle in market_odds_tab.rebased_data:
                        t = candle[0]

                        # Calculate pivot-based values using the current pivot price
                        pivot_open = pivot_price + (pivot_price / 100 * candle[1])
                        pivot_high = pivot_price + (pivot_price / 100 * candle[2])
                        pivot_low = pivot_price + (pivot_price / 100 * candle[3])
                        pivot_close = pivot_price + (pivot_price / 100 * candle[4])

                        # Apply the new formulas for high and low values
                        # High %: (cycle pivot close + vector value - 2*cycle pivot low)/ current price * 100
                        # Low %: (2* cycle pivot close - cycle pivot high - vector value)/current price * 100

                        # We no longer need the vector value for the new formula

                        # Calculate the high and low values using the new, even simpler approach

                        # Define variables for clarity
                        CP = current_price    # current price
                        CPH = pivot_high      # cycle pivot high
                        CPL = pivot_low       # cycle pivot low
                        CPC = pivot_close     # cycle pivot close

                        # Debug output of input values
                        print(f"Current Price: Candle {i} (idx {t}): CP={CP:.2f}, CPH={CPH:.2f}, CPL={CPL:.2f}, CPC={CPC:.2f}")

                        # We'll calculate high_val and low_val after calculating the % changes
                        # projected high = current price * (1 + %change high)
                        # projected low = current price * (1 + %change low)

                        # Initialize with default values
                        high_val = CP
                        low_val = CP

                        # We'll calculate high_percent and low_percent after calculating dollar changes

                        # Calculate dollar change
                        # For any given day (e.g., Monday), we use:
                        # $ change high = next day cycle pivot high (Tuesday's high) - current day cycle pivot close (Monday's close)
                        # $ change low = next day cycle pivot low (Tuesday's low) - current day cycle pivot close (Monday's close)

                        # Get the FWL Aggr. value from the Volatility Statistics tab if available
                        fwl_aggr = 1  # Default to 1 day forward

                        # Try to find the Volatility Statistics tab using our helper method
                        volatility_statistics_tab = self.get_volatility_statistics_tab()

                        # If we found the tab and it has the get_volatility_factor method, use it
                        if volatility_statistics_tab is not None and hasattr(volatility_statistics_tab, 'get_volatility_factor'):
                            fwl_aggr = volatility_statistics_tab.get_volatility_factor()
                            print(f"Using FWL Aggr. value: {fwl_aggr}")

                        # Look forward by fwl_aggr days to get the high/low values
                        if i < len(market_odds_tab.rebased_data) - fwl_aggr:
                            # Get the candle that is fwl_aggr days ahead
                            target_candle = market_odds_tab.rebased_data[i + fwl_aggr]
                            target_idx = target_candle[0]

                            # Get the target day's pivot values
                            # Since we don't have a cycle_pivot_values list in the fallback case,
                            # we'll calculate the target day's pivot values directly
                            if target_idx < len(market_odds_tab.rebased_data):
                                target_candle_data = market_odds_tab.rebased_data[target_idx] if target_idx < len(market_odds_tab.rebased_data) else None

                                if target_candle_data:
                                    # Calculate the target day's pivot values (only high and low needed)
                                    target_pivot_high = pivot_price + (pivot_price / 100 * target_candle_data[2])
                                    target_pivot_low = pivot_price + (pivot_price / 100 * target_candle_data[3])

                                    # Check if the target day is a peak or trough
                                    target_idx_value = target_candle[0]
                                    is_peak = False
                                    is_trough = False

                                    # Check if this is a peak or trough based on the candle pattern
                                    if hasattr(market_odds_tab, 'rebased_data'):
                                        for j, candle in enumerate(market_odds_tab.rebased_data):
                                            if candle[0] == target_idx_value:
                                                # Check if this is a peak or trough based on the candle pattern
                                                if j > 0 and j < len(market_odds_tab.rebased_data) - 1:
                                                    prev_candle = market_odds_tab.rebased_data[j-1]
                                                    next_candle = market_odds_tab.rebased_data[j+1]

                                                    # Check for peak: current high > previous high and current high > next high
                                                    if candle[2] > prev_candle[2] and candle[2] > next_candle[2]:
                                                        is_peak = True
                                                        print(f"Detected peak at index {target_idx_value}")

                                                    # Check for trough: current low < previous low and current low < next low
                                                    if candle[3] < prev_candle[3] and candle[3] < next_candle[3]:
                                                        is_trough = True
                                                        print(f"Detected trough at index {target_idx_value}")
                                                break

                                    # Get default 1-day values (for the parts we don't want to change)
                                    default_high_dollar_change = None
                                    default_low_dollar_change = None

                                    if i < len(market_odds_tab.rebased_data) - 1:
                                        next_candle = market_odds_tab.rebased_data[i + 1]
                                        next_idx = next_candle[0]

                                        if next_idx < len(market_odds_tab.rebased_data):
                                            next_candle_data = market_odds_tab.rebased_data[next_idx]

                                            # Calculate the next day's pivot values (only high and low needed)
                                            next_pivot_high = pivot_price + (pivot_price / 100 * next_candle_data[2])
                                            next_pivot_low = pivot_price + (pivot_price / 100 * next_candle_data[3])

                                            default_high_dollar_change = next_pivot_high - pivot_close
                                            default_low_dollar_change = next_pivot_low - pivot_close

                                    # If we couldn't get default values, use the target values
                                    if default_high_dollar_change is None:
                                        default_high_dollar_change = target_pivot_high - pivot_close
                                    if default_low_dollar_change is None:
                                        default_low_dollar_change = target_pivot_low - pivot_close

                                    # New implementation: Look forward to find nearest peak and trough
                                    # Initialize default values
                                    th_value = 1  # Default TH value
                                    tl_value = 1  # Default TL value

                                    # Check if current day is a peak or trough
                                    current_is_peak = False
                                    current_is_trough = False

                                    # Check if the current candle is a peak or trough
                                    if i > 0 and i < len(market_odds_tab.rebased_data) - 1:
                                        prev_candle_data = market_odds_tab.rebased_data[i-1]
                                        current_candle_data = market_odds_tab.rebased_data[i]
                                        next_candle_data = market_odds_tab.rebased_data[i+1]

                                        # Check for peak: current high > previous high and current high > next high
                                        if current_candle_data[2] > prev_candle_data[2] and current_candle_data[2] > next_candle_data[2]:
                                            current_is_peak = True
                                            print(f"Current day is a peak (high: {current_candle_data[2]} > prev: {prev_candle_data[2]} and next: {next_candle_data[2]})")

                                        # Check for trough: current low < previous low and current low < next low
                                        if current_candle_data[3] < prev_candle_data[3] and current_candle_data[3] < next_candle_data[3]:
                                            current_is_trough = True
                                            print(f"Current day is a trough (low: {current_candle_data[3]} < prev: {prev_candle_data[3]} and next: {next_candle_data[3]})")

                                        # Also check if the current day is in the percentage_based_peaks_troughs dictionary
                                        current_idx = current_candle_data[0]
                                        if hasattr(market_odds_tab, 'percentage_based_peaks_troughs') and current_idx in market_odds_tab.percentage_based_peaks_troughs:
                                            peak_trough_type = market_odds_tab.percentage_based_peaks_troughs[current_idx]
                                            if peak_trough_type == "peak":
                                                current_is_peak = True
                                                print(f"Current day is marked as a peak in percentage_based_peaks_troughs")
                                            elif peak_trough_type == "trough":
                                                current_is_trough = True
                                                print(f"Current day is marked as a trough in percentage_based_peaks_troughs")

                                    # Look forward to find the nearest peak and trough
                                    days_to_peak = None
                                    days_to_trough = None

                                    # If current day is already a peak/trough, set the corresponding value to 1
                                    if current_is_peak:
                                        days_to_peak = 0  # Current day (will be adjusted to 1 later)
                                        print(f"Current day is a peak, setting days_to_peak to 0 (will be adjusted to 1)")

                                    if current_is_trough:
                                        days_to_trough = 0  # Current day (will be adjusted to 1 later)
                                        print(f"Current day is a trough, setting days_to_trough to 0 (will be adjusted to 1)")

                                    # Look forward up to fwl_aggr days to find peaks and troughs
                                    for days_ahead in range(1, fwl_aggr + 1):
                                        if i + days_ahead < len(market_odds_tab.rebased_data):
                                            # Check if this future candle is a peak or trough
                                            future_idx = i + days_ahead

                                            if future_idx > 0 and future_idx < len(market_odds_tab.rebased_data) - 1:
                                                prev_future = market_odds_tab.rebased_data[future_idx-1]
                                                current_future = market_odds_tab.rebased_data[future_idx]
                                                next_future = market_odds_tab.rebased_data[future_idx+1]

                                                # Check for peak
                                                if current_future[2] > prev_future[2] and current_future[2] > next_future[2]:
                                                    if days_to_peak is None:
                                                        days_to_peak = days_ahead
                                                        print(f"Found peak {days_ahead} days ahead")

                                                # Check for trough
                                                if current_future[3] < prev_future[3] and current_future[3] < next_future[3]:
                                                    if days_to_trough is None:
                                                        days_to_trough = days_ahead
                                                        print(f"Found trough {days_ahead} days ahead")

                                    # Adjust days_to_peak and days_to_trough
                                    # Only add 1 if the current day is a peak/trough (to make it 1-based)
                                    # Otherwise, use the value as is (starting count at 0 for the original day)
                                    if days_to_peak is not None:
                                        # Only add 1 if the current day is a peak
                                        if current_is_peak:
                                            days_to_peak += 1
                                            print(f"Current day is a peak, adjusting days_to_peak from {days_to_peak-1} to {days_to_peak}")

                                        if days_to_peak > fwl_aggr:
                                            days_to_peak = fwl_aggr
                                    else:
                                        days_to_peak = fwl_aggr  # If no peak found, use maximum FWL Aggr value

                                    if days_to_trough is not None:
                                        # Only add 1 if the current day is a trough
                                        if current_is_trough:
                                            days_to_trough += 1
                                            print(f"Current day is a trough, adjusting days_to_trough from {days_to_trough-1} to {days_to_trough}")

                                        if days_to_trough > fwl_aggr:
                                            days_to_trough = fwl_aggr
                                    else:
                                        days_to_trough = fwl_aggr  # If no trough found, use maximum FWL Aggr value

                                    # Set the TH and TL values
                                    th_value = days_to_peak
                                    tl_value = days_to_trough

                                    print(f"TH value: {th_value}, TL value: {tl_value}")

                                    # Now get the high and low values from the appropriate future candles
                                    # Apply special logic when FWL Aggr. is greater than 1
                                    if fwl_aggr > 1:
                                        # For TH value, determine the offset
                                        if th_value == 1 and current_is_peak:
                                            # If TH=1 and current day is a peak, use the next day (standard way)
                                            th_offset = 1
                                            print(f"TH=1 and current day is a peak, using standard offset of 1")
                                        else:
                                            # Otherwise, use TH+1 as the offset
                                            th_offset = th_value + 1
                                            print(f"TH={th_value}, using offset of {th_offset} (TH+1)")

                                        # For TL value, determine the offset
                                        if tl_value == 1 and current_is_trough:
                                            # If TL=1 and current day is a trough, use the next day (standard way)
                                            tl_offset = 1
                                            print(f"TL=1 and current day is a trough, using standard offset of 1")
                                        else:
                                            # Otherwise, use TL+1 as the offset
                                            tl_offset = tl_value + 1
                                            print(f"TL={tl_value}, using offset of {tl_offset} (TL+1)")

                                        # Calculate the indices using the offsets
                                        th_idx = i + th_offset
                                        tl_idx = i + tl_offset

                                        print(f"Using high from {th_offset} days ahead (index {th_idx}) for projected high")
                                        print(f"Using low from {tl_offset} days ahead (index {tl_idx}) for projected low")
                                    else:
                                        # Standard logic for FWL Aggr. = 1
                                        # For TH:1, use the next day (i+1)
                                        # For TH:2, use 2 days ahead (i+2)
                                        # For TH:3, use 3 days ahead (i+3)
                                        th_idx = i + th_value
                                        tl_idx = i + tl_value

                                        print(f"Standard calculation (FWL Aggr. = 1)")
                                        print(f"Using high from {th_value} days ahead (index {th_idx}) for projected high")
                                        print(f"Using low from {tl_value} days ahead (index {tl_idx}) for projected low")

                                    # Get the high value for projected high
                                    if th_idx < len(market_odds_tab.rebased_data):
                                        th_candle_data = market_odds_tab.rebased_data[th_idx]
                                        # Calculate the high value using the pivot price
                                        th_pivot_high = pivot_price + (pivot_price / 100 * th_candle_data[2])
                                        # Calculate dollar change as: future high - current close
                                        high_dollar_change = th_pivot_high - pivot_close
                                        print(f"Using high from {th_value} days ahead (index {th_idx}) for projected high")
                                        print(f"Current day index: {i}, Current day close: {pivot_close:.2f}")
                                        print(f"Future day index: {th_idx}, Future day high: {th_pivot_high:.2f}")
                                        print(f"Calculation: {th_pivot_high:.2f} (day {th_value} high) - {pivot_close:.2f} (current close) = {high_dollar_change:.2f}")
                                    else:
                                        # Fallback to default
                                        high_dollar_change = default_high_dollar_change
                                        print(f"Index {th_idx} out of range, using default high value")

                                    # Get the low value for projected low
                                    if tl_idx < len(market_odds_tab.rebased_data):
                                        tl_candle_data = market_odds_tab.rebased_data[tl_idx]
                                        # Calculate the low value using the pivot price
                                        tl_pivot_low = pivot_price + (pivot_price / 100 * tl_candle_data[3])
                                        # Calculate dollar change as: future low - current close
                                        low_dollar_change = tl_pivot_low - pivot_close
                                        print(f"Using low from {tl_value} days ahead (index {tl_idx}) for projected low")
                                        print(f"Current day index: {i}, Current day close: {pivot_close:.2f}")
                                        print(f"Future day index: {tl_idx}, Future day low: {tl_pivot_low:.2f}")
                                        print(f"Calculation: {tl_pivot_low:.2f} (day {tl_value} low) - {pivot_close:.2f} (current close) = {low_dollar_change:.2f}")
                                    else:
                                        # Fallback to default
                                        low_dollar_change = default_low_dollar_change
                                        print(f"Index {tl_idx} out of range, using default low value")

                                    # Format the updated value string
                                    if fwl_aggr > 1:
                                        # Include the offset information when FWL Aggr. > 1
                                        th_offset = th_value
                                        tl_offset = tl_value

                                        # Calculate the offsets
                                        if th_value == 1 and current_is_peak:
                                            th_offset = 1
                                        else:
                                            th_offset = th_value + 1

                                        if tl_value == 1 and current_is_trough:
                                            tl_offset = 1
                                        else:
                                            tl_offset = tl_value + 1

                                        updated_value = f"TH: {th_value}({th_offset}) | TL: {tl_value}({tl_offset})"
                                    else:
                                        # Standard format for FWL Aggr. = 1
                                        updated_value = f"TH: {th_value} | TL: {tl_value}"

                                    # Determine if we should update this row based on matching mode
                                    should_update = True

                                    # If matching mode is set, apply filtering
                                    if matching_mode:
                                        # For H/L Matching, only update rows that match the latest category
                                        if matching_mode == 'hl':
                                            # Get the latest category (e.g., "2H", "3L") from the data
                                            latest_category = None

                                            # Try to get the latest category from the data
                                            if hasattr(market_odds_tab, 'data') and market_odds_tab.data is not None and not market_odds_tab.data.empty:
                                                # Get the last index in the data
                                                last_data_idx = len(market_odds_tab.data) - 1

                                                # Try to get the category from the data DataFrame if it has a Category column
                                                if hasattr(market_odds_tab, 'data') and 'Category' in market_odds_tab.data.columns:
                                                    latest_category = market_odds_tab.data['Category'].iloc[-1]
                                                    print(f"Latest category from market_odds_tab data: {latest_category}")
                                                # Try to get the category from percentage_based_categories
                                                elif hasattr(market_odds_tab, 'percentage_based_categories') and last_data_idx < len(market_odds_tab.rebased_data):
                                                    last_original_idx = market_odds_tab.rebased_data[last_data_idx][0]
                                                    if last_original_idx in market_odds_tab.percentage_based_categories:
                                                        latest_category = market_odds_tab.percentage_based_categories[last_original_idx]
                                                        print(f"Latest category from percentage_based_categories: {latest_category}")

                                            # If we still don't have a category, try to determine it from the data
                                            if latest_category is None:
                                                # Fallback: Determine if the last row is bullish or bearish
                                                latest_is_bullish = False  # Default to bearish

                                                if hasattr(market_odds_tab, 'below_vector'):
                                                    # Use the below_vector status to determine if we're in a bullish or bearish cycle
                                                    latest_is_bullish = not market_odds_tab.below_vector
                                                    print(f"Latest bullish/bearish status from below_vector: {'bullish' if latest_is_bullish else 'bearish'}")
                                                elif hasattr(market_odds_tab, 'data') and 'Close' in market_odds_tab.data.columns:
                                                    # Fallback to using the last close value
                                                    last_close = market_odds_tab.data['Close'].iloc[-1]
                                                    latest_is_bullish = last_close >= 0
                                                    print(f"Latest bullish/bearish status from last close value: {'bullish' if latest_is_bullish else 'bearish'}")

                                                # Create a default category based on bullish/bearish status
                                                latest_category = "1H" if latest_is_bullish else "1L"
                                                print(f"Created default latest category: {latest_category}")

                                            # Get the category for the current row
                                            current_category = None

                                            # Try to get the category from percentage_based_categories
                                            if hasattr(market_odds_tab, 'percentage_based_categories') and i < len(market_odds_tab.rebased_data):
                                                current_idx = market_odds_tab.rebased_data[i][0]
                                                if current_idx in market_odds_tab.percentage_based_categories:
                                                    current_category = market_odds_tab.percentage_based_categories[current_idx]
                                                    print(f"Current category from percentage_based_categories: {current_category}")

                                            # If we couldn't get the category, determine it based on bullish/bearish status
                                            if current_category is None:
                                                # For this section, we don't have direct access to is_bullish for the row
                                                # So we'll use a simple heuristic based on the candle data
                                                current_candle_data = market_odds_tab.rebased_data[i]
                                                is_bullish = current_candle_data[4] >= 0  # Positive close value indicates bullish
                                                current_category = "1H" if is_bullish else "1L"
                                                print(f"Created default current category: {current_category}")

                                            # Check if the categories match
                                            if current_category != latest_category:
                                                should_update = False
                                                print(f"Skipping update for row {i} - category mismatch: row is {current_category}, latest is {latest_category}")

                                        # For Weekday Matching, only update rows that match the same weekday
                                        elif matching_mode == 'weekday':
                                            # Get the current date from the data
                                            current_date = None
                                            latest_date = None

                                            if hasattr(market_odds_tab, 'data') and i < len(market_odds_tab.data) and hasattr(market_odds_tab.data, 'index'):
                                                current_date = market_odds_tab.data.index[i]

                                                # Get the latest date (last row in the data)
                                                if len(market_odds_tab.data) > 0:
                                                    latest_date = market_odds_tab.data.index[-1]

                                            # If we have both dates, compare weekdays
                                            if current_date is not None and latest_date is not None:
                                                current_weekday = current_date.weekday()
                                                latest_weekday = latest_date.weekday()

                                                if current_weekday != latest_weekday:
                                                    should_update = False
                                                    print(f"Skipping update for row {i} - weekday mismatch: row is {current_weekday}, latest is {latest_weekday}")

                                    # Always store the updated value for this candle (regardless of matching mode)
                                    updated_values[i] = updated_value
                                    print(f"Storing updated value for row {i}: {updated_value}")

                                    # Note: should_update is used for other filtering logic but not for storing updated_values
                                else:
                                    # Fall back to next day if target day values not found
                                    if i < len(market_odds_tab.rebased_data) - 1:
                                        next_candle = market_odds_tab.rebased_data[i + 1]
                                        next_idx = next_candle[0]

                                        if next_idx < len(market_odds_tab.rebased_data):
                                            next_candle_data = market_odds_tab.rebased_data[next_idx]

                                            # Calculate the next day's pivot values (only high and low needed)
                                            next_pivot_high = pivot_price + (pivot_price / 100 * next_candle_data[2])
                                            next_pivot_low = pivot_price + (pivot_price / 100 * next_candle_data[3])

                                            # $ change high = next day cycle pivot high - current day cycle pivot close
                                            high_dollar_change = next_pivot_high - pivot_close
                                            # $ change low = next day cycle pivot low - current day cycle pivot close
                                            low_dollar_change = next_pivot_low - pivot_close

                                            print(f"Target day ({fwl_aggr} days ahead) values not found, using next day values")
                                        else:
                                            # If next_idx is out of range, use the current high/low - close
                                            high_dollar_change = pivot_high - pivot_close
                                            low_dollar_change = pivot_low - pivot_close
                                            print(f"Next day index out of range, using current: High={pivot_high:.2f}, Low={pivot_low:.2f}, Close={pivot_close:.2f}")
                                    else:
                                        # For the last candle, use the current high/low - close
                                        high_dollar_change = pivot_high - pivot_close
                                        low_dollar_change = pivot_low - pivot_close
                                        print(f"Last candle, using current: High={pivot_high:.2f}, Low={pivot_low:.2f}, Close={pivot_close:.2f}")

                                    # For fallback cases, use default TH:1 | TL:1 format
                                    updated_value = "TH: 1 | TL: 1"
                                    updated_values[i] = updated_value
                                    print(f"Fallback: Updating row {i} with {updated_value}")
                            else:
                                # If target_idx is out of range, fall back to next day
                                if i < len(market_odds_tab.rebased_data) - 1:
                                    next_candle = market_odds_tab.rebased_data[i + 1]
                                    next_idx = next_candle[0]

                                    if next_idx < len(market_odds_tab.rebased_data):
                                        next_candle_data = market_odds_tab.rebased_data[next_idx]

                                        # Calculate the next day's pivot values (only high and low needed)
                                        next_pivot_high = pivot_price + (pivot_price / 100 * next_candle_data[2])
                                        next_pivot_low = pivot_price + (pivot_price / 100 * next_candle_data[3])

                                        # $ change high = next day cycle pivot high - current day cycle pivot close
                                        high_dollar_change = next_pivot_high - pivot_close
                                        # $ change low = next day cycle pivot low - current day cycle pivot close
                                        low_dollar_change = next_pivot_low - pivot_close

                                        print(f"Target day ({fwl_aggr} days ahead) out of range, using next day values")
                                    else:
                                        # If next_idx is out of range, use the current high/low - close
                                        high_dollar_change = pivot_high - pivot_close
                                        low_dollar_change = pivot_low - pivot_close
                                        print(f"Next day index out of range, using current: High={pivot_high:.2f}, Low={pivot_low:.2f}, Close={pivot_close:.2f}")
                                else:
                                    # For the last candle, use the current high/low - close
                                    high_dollar_change = pivot_high - pivot_close
                                    low_dollar_change = pivot_low - pivot_close
                                    print(f"Last candle, using current: High={pivot_high:.2f}, Low={pivot_low:.2f}, Close={pivot_close:.2f}")

                                # For fallback cases, use default TH:1 | TL:1 format
                                updated_value = "TH: 1 | TL: 1"
                                updated_values[i] = updated_value
                                print(f"Fallback: Updating row {i} with {updated_value}")
                        else:
                            # Fall back to next day if target day is out of range
                            if i < len(market_odds_tab.rebased_data) - 1:
                                next_candle = market_odds_tab.rebased_data[i + 1]
                                next_idx = next_candle[0]

                                if next_idx < len(market_odds_tab.rebased_data):
                                    next_candle_data = market_odds_tab.rebased_data[next_idx]

                                    # Calculate the next day's pivot values (only high and low needed)
                                    next_pivot_high = pivot_price + (pivot_price / 100 * next_candle_data[2])
                                    next_pivot_low = pivot_price + (pivot_price / 100 * next_candle_data[3])

                                    # $ change high = next day cycle pivot high - current day cycle pivot close
                                    high_dollar_change = next_pivot_high - pivot_close
                                    # $ change low = next day cycle pivot low - current day cycle pivot close
                                    low_dollar_change = next_pivot_low - pivot_close

                                    print(f"Target day ({fwl_aggr} days ahead) out of range, using next day values")
                                else:
                                    # If next_idx is out of range, use the current high/low - close
                                    high_dollar_change = pivot_high - pivot_close
                                    low_dollar_change = pivot_low - pivot_close
                                    print(f"Next day index out of range, using current: High={pivot_high:.2f}, Low={pivot_low:.2f}, Close={pivot_close:.2f}")
                            else:
                                # For the last candle, use the current high/low - close
                                high_dollar_change = pivot_high - pivot_close
                                low_dollar_change = pivot_low - pivot_close
                                print(f"Last candle, using current: High={pivot_high:.2f}, Low={pivot_low:.2f}, Close={pivot_close:.2f}")

                            # For fallback cases, use default TH:1 | TL:1 format
                            updated_value = "TH: 1 | TL: 1"
                            updated_values[i] = updated_value
                            print(f"Fallback: Updating row {i} with {updated_value}")

                        # Get date information for logging
                        if hasattr(market_odds_tab, 'data') and t < len(market_odds_tab.data):
                            current_date = str(market_odds_tab.data.index[t]) if hasattr(market_odds_tab.data, 'index') else "Current day"

                            # Log the values being used
                            print(f"For {current_date}: Using calculated high/low values")
                            print(f"$ Change High = {high_dollar_change:.2f}")
                            print(f"$ Change Low = {low_dollar_change:.2f}")

                        print(f"Formula: high_val = CP * (CPH / CPC) = {CP:.2f} * ({CPH:.2f} / {CPC:.2f}) = {high_val:.2f}")
                        print(f"Formula: low_val = CP * (CPL / CPC) = {CP:.2f} * ({CPL:.2f} / {CPC:.2f}) = {low_val:.2f}")
                        # Calculate the percentage change for $ change high and $ change low
                        # % change high = ($ change high / current day close) * 100
                        # % change low = ($ change low / current day close) * 100
                        high_percent = 100 * high_dollar_change / pivot_close if pivot_close != 0 else 0
                        low_percent = 100 * low_dollar_change / pivot_close if pivot_close != 0 else 0

                        print(f"High $ Change = {high_dollar_change:.2f}, Low $ Change = {low_dollar_change:.2f}")
                        print(f"High % Change = {high_percent:.2f}%, Low % Change = {low_percent:.2f}%")

                        # Calculate projected high and low values using the new formula
                        # projected high = current price * (1 + %change high)
                        # projected low = current price * (1 + %change low)
                        high_val = CP * (1 + high_percent / 100)
                        low_val = CP * (1 + low_percent / 100)

                        # Round to 2 decimal places to avoid floating-point precision issues
                        high_val = round(high_val, 2)
                        low_val = round(low_val, 2)

                        print(f"Formula: projected high = CP * (1 + high_percent/100) = {CP:.2f} * (1 + {high_percent:.2f}/100) = {high_val:.2f}")
                        print(f"Formula: projected low = CP * (1 + low_percent/100) = {CP:.2f} * (1 + {low_percent:.2f}/100) = {low_val:.2f}")

                        print(f"Current Price: Final High: {high_val:.2f}, Final Low: {low_val:.2f}")

                        # For open and close, use the standard calculation
                        open_val = pivot_open
                        close_val = pivot_close

                        # Debug output
                        print(f"Current Price: Candle {t}: Current={current_price:.2f}, " +
                              f"Pivot High={pivot_high:.2f}, Pivot Low={pivot_low:.2f}, Pivot Close={pivot_close:.2f}, " +
                              f"New High={high_val:.2f}, New Low={low_val:.2f}")

                        # Get the updated value for this candle
                        updated_value = updated_values.get(i, "None")

                        # Round dollar changes to 2 decimal places to avoid floating-point precision issues
                        high_dollar_change = round(high_dollar_change, 2)
                        low_dollar_change = round(low_dollar_change, 2)

                        # Store the data with additional columns for $ change and % change
                        # We'll store them in the Open and Close positions temporarily, then rearrange later
                        rebased_data.append([t, high_dollar_change, high_val, low_val, low_dollar_change, updated_value])

            elif self.calculation_mode == "pivot_price":
                # Calculate values based on pivot price
                for candle in market_odds_tab.rebased_data:
                    t = candle[0]
                    open_val = pivot_price + (pivot_price / 100 * candle[1])  # pivot_price + (pivot_price / 100 * percentage)
                    high_val = pivot_price + (pivot_price / 100 * candle[2])
                    low_val = pivot_price + (pivot_price / 100 * candle[3])
                    close_val = pivot_price + (pivot_price / 100 * candle[4])

                    # Round to 2 decimal places to avoid floating-point precision issues
                    open_val = round(open_val, 2)
                    high_val = round(high_val, 2)
                    low_val = round(low_val, 2)
                    close_val = round(close_val, 2)

                    rebased_data.append([t, open_val, high_val, low_val, close_val])

            elif self.calculation_mode == "cycle_pivot":
                # For OHLC mode, use the real OHLC data without any formulas or rebasing
                print("OHLC: Using real OHLC data without any formulas or rebasing")

                # Check if we have access to the original data
                if hasattr(market_odds_tab, 'data') and market_odds_tab.data is not None and not market_odds_tab.data.empty:
                    # Use the original data directly
                    for i, (idx, row) in enumerate(market_odds_tab.data.iterrows()):
                        if i < len(market_odds_tab.rebased_data):
                            t = market_odds_tab.rebased_data[i][0]  # Get the index from rebased_data

                            # Use the actual OHLC values from the original data
                            open_val = row['Open']
                            high_val = row['High']
                            low_val = row['Low']
                            close_val = row['Close']

                            rebased_data.append([t, open_val, high_val, low_val, close_val])

                            # Determine if this candle is in a bullish or bearish cycle
                            # We'll still use the vector crossing information for coloring
                            is_bullish = True  # Default to bullish

                            if hasattr(market_odds_tab, 'below_vector') and hasattr(market_odds_tab, 'crossing_points'):
                                # Find the most recent crossing point before this index
                                sorted_crossings = sorted(market_odds_tab.crossing_points)
                                last_crossing = None
                                for crossing in sorted_crossings:
                                    if crossing <= t:
                                        last_crossing = crossing
                                    else:
                                        break

                                if last_crossing is not None:
                                    # A bullish cycle begins immediately when a candle closes above 0
                                    # A bearish cycle begins immediately when a candle closes below 0
                                    # So the cycle state is determined by the current candle's position relative to 0

                                    # Get the close value for this candle
                                    close_value = row['Close']

                                    # Determine if this is a bullish or bearish cycle based on the close value
                                    is_bullish = close_value >= 0
                                else:
                                    # No crossings yet, use the initial state
                                    is_bullish = not market_odds_tab.below_vector

                            # Set bullish/bearish cycle information
                            is_bullish_cycle[len(rebased_data) - 1] = is_bullish
                else:
                    # If original data is not available, use the rebased data as is
                    print("Cycle Pivot: Original data not available, using rebased data as is")
                    for candle in market_odds_tab.rebased_data:
                        rebased_data.append(candle)

            elif self.calculation_mode == "historical_pivots":
                # Display The Line prices before transitions
                print("The Line Prices: Displaying The Line prices before transitions")

                # Get the crossing points and below_vector status from market_odds_tab
                if hasattr(market_odds_tab, 'crossing_points') and hasattr(market_odds_tab, 'below_vector'):
                    crossing_points = market_odds_tab.crossing_points
                    current_is_below = market_odds_tab.below_vector

                    # Sort crossing points for processing
                    sorted_crossings = sorted(crossing_points)

                    # Create a list to store vector price data
                    vector_price_data = []

                    # First, identify all cycle segments (same as other tabs)
                    cycle_segments = []
                    current_is_bullish = not current_is_below
                    segment_start_idx = 0

                    # Create a list to hold indices for cycle identification
                    temp_indices = []
                    for crossing in sorted_crossings:
                        temp_indices.append(crossing)

                    # Add the last index of the data for the final segment
                    if hasattr(market_odds_tab, 'data') and market_odds_tab.data is not None and not market_odds_tab.data.empty:
                        last_idx = len(market_odds_tab.data) - 1
                        if last_idx not in temp_indices:
                            temp_indices.append(last_idx)

                    # Create a mapping from data index to row index
                    idx_to_row = {idx: i for i, idx in enumerate(temp_indices)}

                    # Add a segment for each cycle between crossings
                    for i, crossing in enumerate(sorted_crossings):
                        # Find the row index for this crossing
                        crossing_row = idx_to_row[crossing]

                        # For each cycle, the vector price is the last close of the previous cycle
                        # This vector price will be used for the NEXT cycle segment
                        vector_price = None
                        if hasattr(market_odds_tab, 'data') and crossing < len(market_odds_tab.data):
                            # For the first cycle, we don't have a previous cycle's close
                            if i == 0:
                                vector_price = None  # First cycle doesn't have a vector price
                                print(f"First cycle at {crossing}, no vector price available (no previous cycle)")
                            else:
                                # For subsequent cycles, use the last close of the previous cycle
                                # The last index of the previous cycle is one less than the current crossing
                                last_idx_of_prev_cycle = crossing - 1

                                if last_idx_of_prev_cycle >= 0 and last_idx_of_prev_cycle < len(market_odds_tab.data):
                                    # Use the close price of the last candle in the previous cycle
                                    vector_price = market_odds_tab.data['Close'].iloc[last_idx_of_prev_cycle]
                                    print(f"Using last close of previous cycle at index {last_idx_of_prev_cycle} for crossing at {crossing}")
                                else:
                                    # Fallback if the index is out of bounds
                                    vector_price = None
                                    print(f"Could not find last close of previous cycle for crossing at {crossing}")
                        else:
                            # Fallback to current price if data is not available
                            if hasattr(market_odds_tab, 'data') and not market_odds_tab.data.empty:
                                vector_price = market_odds_tab.data['Close'].iloc[-1]
                            else:
                                vector_price = pivot_price

                        # Get the timestamp for this crossing
                        timestamp = None
                        if hasattr(market_odds_tab, 'data') and crossing < len(market_odds_tab.data):
                            timestamp = market_odds_tab.data.index[crossing]

                        # Add the segment up to this crossing
                        # For bullish cycles, end at the candle just before the crossing (crossing - 1)
                        # For bearish cycles, end at the candle just before the crossing (crossing - 1)
                        # The first cycle is a special case
                        end_idx = crossing
                        if i > 0:  # Not the first cycle
                            if current_is_bullish:  # Current cycle is bullish
                                # Bullish cycle ends at the candle just before the crossing
                                end_idx = crossing - 1 if crossing > 0 else 0
                            else:  # Current cycle is bearish
                                # Bearish cycle ends at the candle just before the crossing
                                end_idx = crossing - 1 if crossing > 0 else 0

                        cycle_segments.append({
                            'start_idx': segment_start_idx,
                            'end_idx': end_idx,
                            'is_bullish': current_is_bullish,
                            'vector_price': vector_price,
                            'timestamp': timestamp
                        })

                        # Debug output for cycle segment
                        if vector_price is None:
                            print(f"Cycle Segment: Indices {segment_start_idx}-{end_idx}, " +
                                  f"Is bullish: {current_is_bullish}, Vector Price: N/A")
                        else:
                            print(f"Cycle Segment: Indices {segment_start_idx}-{end_idx}, " +
                                  f"Is bullish: {current_is_bullish}, Vector Price: {vector_price:.2f}")

                        # Update for next segment
                        # The next cycle starts at the crossing point
                        segment_start_idx = crossing
                        current_is_bullish = not current_is_bullish

                    # Add the final segment after the last crossing
                    final_idx = len(market_odds_tab.data) - 1 if hasattr(market_odds_tab, 'data') and market_odds_tab.data is not None and not market_odds_tab.data.empty else segment_start_idx

                    # Get the vector price for the final segment (last close of the previous cycle)
                    final_vector_price = None
                    if hasattr(market_odds_tab, 'data') and segment_start_idx < len(market_odds_tab.data):
                        # For the final segment, use the last close of the previous cycle
                        # The last index of the previous cycle is one less than the segment_start_idx
                        last_idx_of_prev_cycle = segment_start_idx - 1

                        if last_idx_of_prev_cycle >= 0 and last_idx_of_prev_cycle < len(market_odds_tab.data):
                            # Use the close price of the last candle in the previous cycle
                            final_vector_price = market_odds_tab.data['Close'].iloc[last_idx_of_prev_cycle]
                            print(f"Using last close of previous cycle at index {last_idx_of_prev_cycle} for final segment")
                        else:
                            # Fallback if the index is out of bounds or if this is the first segment
                            final_vector_price = None
                            print(f"Could not find last close of previous cycle for final segment")
                    else:
                        final_vector_price = current_price

                    # Get the timestamp for the final segment
                    final_timestamp = None
                    if hasattr(market_odds_tab, 'data') and segment_start_idx < len(market_odds_tab.data):
                        final_timestamp = market_odds_tab.data.index[segment_start_idx]

                    cycle_segments.append({
                        'start_idx': segment_start_idx,
                        'end_idx': final_idx,
                        'is_bullish': current_is_bullish,
                        'vector_price': final_vector_price,
                        'timestamp': final_timestamp
                    })

                    # Debug output for final cycle segment
                    if final_vector_price is None:
                        print(f"Final Cycle Segment: Indices {segment_start_idx}-{final_idx}, " +
                              f"Is bullish: {current_is_bullish}, Vector Price: N/A")
                    else:
                        print(f"Final Cycle Segment: Indices {segment_start_idx}-{final_idx}, " +
                              f"Is bullish: {current_is_bullish}, Vector Price: {final_vector_price:.2f}")

                    # Now create the vector price data from the cycle segments
                    for i, segment in enumerate(cycle_segments):
                        # Skip the last segment if it's just a placeholder
                        if i == len(cycle_segments) - 1 and segment['start_idx'] == segment['end_idx']:
                            continue

                        # Calculate the idx range
                        # For bullish cycles, end at the candle just before the first candle that closes below 0
                        # For bearish cycles, end at the candle just before the first candle that closes above 0
                        start_idx = segment['start_idx']
                        end_idx = segment['end_idx']

                        # Format the idx range
                        idx_range = f"{start_idx}-{end_idx}" if start_idx != end_idx else f"{start_idx}"

                        # Get the vector price for this cycle
                        # For the first cycle, there's no vector price
                        # For subsequent cycles, use the vector price from the previous cycle
                        vector_price = None
                        if i == 0:
                            # First cycle has no vector price
                            display_vector_price = "N/A"
                        else:
                            # Use the vector price from the previous cycle
                            prev_segment = cycle_segments[i-1]
                            if prev_segment['end_idx'] > 0 and prev_segment['end_idx'] < len(market_odds_tab.data):
                                # Use the close price of the last candle in the previous cycle
                                vector_price = market_odds_tab.data['Close'].iloc[prev_segment['end_idx'] - 1]
                                display_vector_price = vector_price
                            else:
                                display_vector_price = "N/A"

                        vector_price_data.append([
                            start_idx,  # Index
                            segment['timestamp'],  # Time
                            display_vector_price,  # Extrema Price (renamed from Pivot Price)
                            "Above Extrema" if segment['is_bullish'] else "Below Extrema",  # Cycle Position
                            idx_range  # Idx Range
                        ])

                        # Print debug info
                        if display_vector_price == "N/A":
                            print(f"No extrema price for idx range {idx_range}, Cycle: {'Above Extrema' if segment['is_bullish'] else 'Below Extrema'} (first cycle)")
                        else:
                            print(f"Extrema price {display_vector_price:.2f} for idx range {idx_range}, Cycle: {'Above Extrema' if segment['is_bullish'] else 'Below Extrema'}")

                    # Create DataFrame with vector price data
                    if vector_price_data:
                        data = pd.DataFrame(vector_price_data, columns=['Index', 'Time', 'Extrema Price', 'Next Cycle', 'Idx Range'])

                        # We'll set is_bullish_cycle later, right before removing the Next Cycle column

                        # Format the extrema price to 2 decimal places (if it's not already "N/A")
                        data['Extrema Price'] = data['Extrema Price'].apply(
                            lambda x: f"{x:.2f}" if isinstance(x, (int, float)) else x
                        )

                        # Add a Category column for compatibility with other views
                        # For vector prices tab, we want to use the same format as other tabs (e.g., "1H" for bullish, "1L" for bearish)
                        data['Category'] = ""

                        # Clear the is_bullish_cycle dictionary to ensure we start fresh
                        is_bullish_cycle.clear()

                        for i in range(len(data)):
                            # Determine if it's bullish or bearish
                            is_bullish = data.iloc[i]['Next Cycle'] == "Above Extrema"
                            # Set the category to "Bullish Cycle" or "Bearish Cycle"
                            if is_bullish:
                                data.loc[i, 'Category'] = "Bullish Cycle"
                            else:
                                data.loc[i, 'Category'] = "Bearish Cycle"

                            # Debug print to verify category
                            print(f"Row {i}: Category set to {data.loc[i, 'Category']}")

                        # Remove the Index and Next Cycle columns, and reorder the remaining columns
                        data = data[['Time', 'Idx Range', 'Extrema Price', 'Category']]

                        # For The Line Prices tab, we don't need to set the is_bullish_cycle dictionary
                        # because we're using the Category column directly for coloring in the OHLCTableModel
                        print(f"The Line Prices Tab: Using Category column directly for coloring {len(data)} rows")

                        # No need to rebase data as we're displaying the vector price information directly
                        rebased_data = []
                    else:
                        # If no vector price data, create an empty DataFrame
                        data = pd.DataFrame(columns=['Index', 'Time', 'Extrema Price', 'Next Cycle', 'Idx Range', 'Category'])
                        print("The Line Prices: No price data available")
                else:
                    # If no crossing points or below_vector, create an empty DataFrame
                    data = pd.DataFrame(columns=['Index', 'Time', 'Extrema Price', 'Next Cycle', 'Idx Range', 'Category'])
                    print("The Line Prices: No crossing points or below_vector available")

            # Create DataFrame with OHLC columns and an empty H/L column
            # Only create this DataFrame if we're not in historical_pivots mode
            # since we've already created the DataFrame for that mode
            if self.calculation_mode != "historical_pivots":
                if self.calculation_mode == "current_price":
                    # For current price mode, we've stored data differently:
                    # [Index, High $ Change, Projected High, Projected Low, Low $ Change, Updated]
                    data = pd.DataFrame(rebased_data, columns=['Index', '$ Change High', 'Projected High', 'Projected Low', '$ Change Low', 'Updated'])

                    # Calculate % Change columns
                    data['% Change High'] = 0.0
                    data['% Change Low'] = 0.0

                    for i in range(len(data)):
                        # Get the values
                        dollar_change_high = data.loc[i, '$ Change High']
                        dollar_change_low = data.loc[i, '$ Change Low']

                        # Get the cycle pivot close value for this row
                        # We need to find the corresponding candle in the original data
                        idx = data.loc[i, 'Index']
                        cycle_pivot_close = None

                        # Find the cycle pivot close value for this index
                        for j, candle in enumerate(market_odds_tab.rebased_data):
                            if candle[0] == idx:
                                # We found the candle, now get the cycle pivot close value
                                if hasattr(market_odds_tab, 'data') and j < len(market_odds_tab.data):
                                    # Get the close value from the original data
                                    cycle_pivot_close = market_odds_tab.data['Close'].iloc[j]
                                break

                        # If we couldn't find the cycle pivot close value, use a default value
                        if cycle_pivot_close is None:
                            cycle_pivot_close = pivot_price

                        # Calculate percentage changes (avoid division by zero)
                        # For High: ($Change/a given days close) x 100 = Change % High
                        # For Low: ($Change/a given days close) x 100 = Change % Low
                        if cycle_pivot_close != 0:
                            percent_change_high = (dollar_change_high / cycle_pivot_close) * 100
                            percent_change_low = (dollar_change_low / cycle_pivot_close) * 100
                            data.loc[i, '% Change High'] = percent_change_high
                            data.loc[i, '% Change Low'] = percent_change_low
                elif self.calculation_mode == "percentage_based":
                    # For percentage based mode, use the standard OHLC columns plus a peak/trough column
                    data = pd.DataFrame(rebased_data, columns=['Index', 'Open', 'High', 'Low', 'Close'])

                    # Add a peak/trough column that will be empty unless it's a peak or trough
                    data['Peak/Trough'] = ""

                    # Note: Peak/Trough identification is now handled in the cycle categorization logic
                    # Peaks are the highest high in a bullish cycle
                    # Troughs are the lowest low in a bearish cycle
                else:
                    # For other modes, use the standard OHLC columns
                    data = pd.DataFrame(rebased_data, columns=['Index', 'Open', 'High', 'Low', 'Close'])

            # Get the crossing points and below_vector status from market_odds_tab
            if hasattr(market_odds_tab, 'crossing_points') and hasattr(market_odds_tab, 'below_vector'):
                crossing_points = market_odds_tab.crossing_points
                current_is_below = market_odds_tab.below_vector

                # Group candles by cycle type for accurate counting

                # First, identify all cycle segments
                cycle_segments = []
                current_is_bullish = not current_is_below
                segment_start_idx = 0

                # Since we've removed the 'Index' column, we need to handle this differently
                # We'll use the row indices directly
                indices = list(range(len(data)))

                # Create a mapping from data index to row index (in this case, they're the same)
                idx_to_row = {idx: idx for idx in indices}

                # Sort crossing points for processing
                sorted_crossings = sorted(crossing_points)

                # Add a segment for each cycle between crossings
                for i, crossing in enumerate(sorted_crossings):
                    if crossing in indices:
                        # Find the row index for this crossing
                        crossing_row = idx_to_row[crossing]

                        # Get the pivot price for this segment from the cycle_pivot_prices dictionary
                        # We already calculated and stored these in the cycle_pivot mode
                        if self.calculation_mode == "cycle_pivot" and crossing_row in cycle_pivot_prices:
                            segment_pivot = cycle_pivot_prices[crossing_row]
                            print(f"Using already calculated pivot price {segment_pivot:.2f} for crossing at row {crossing_row}")
                        else:
                            # For other calculation modes, use the stored or calculated pivot price
                            if hasattr(market_odds_tab, 'crossing_pivots') and isinstance(market_odds_tab.crossing_pivots, dict) and crossing in market_odds_tab.crossing_pivots:
                                # Use the exact pivot price that was used when the crossing was detected
                                segment_pivot = market_odds_tab.crossing_pivots[crossing]
                                print(f"Using stored pivot price {segment_pivot:.2f} for crossing at index {crossing}")
                            else:
                                # Fallback to calculating the pivot price
                                # For the first segment, use the initial pivot price
                                if i == 0 and hasattr(market_odds_tab, 'visualization_pivot'):
                                    segment_pivot = market_odds_tab.visualization_pivot
                                else:
                                    # For subsequent segments, use the vector price at the crossing point
                                    # This is the same logic used in market_odds_tab to determine pivot prices
                                    if hasattr(market_odds_tab, 'data') and hasattr(market_odds_tab, 'current_preset') and market_odds_tab.current_preset == "W1/Daily" and crossing > 0:
                                        # Special case for W1/Daily preset
                                        if market_odds_tab.data['Open'].iloc[crossing] != market_odds_tab.data['Close'].iloc[crossing-1]:
                                            # There's a gap - use the previous close as the pivot
                                            segment_pivot = market_odds_tab.data['Close'].iloc[crossing-1]
                                        else:
                                            # No gap - use the open of the candle
                                            segment_pivot = market_odds_tab.data['Open'].iloc[crossing]
                                    else:
                                        # Vector calculation has been removed, use closing price instead
                                        segment_pivot = market_odds_tab.data['Close'].iloc[crossing]
                                print(f"Calculated pivot price {segment_pivot:.2f} for crossing at index {crossing} (no stored pivot available)")

                        # Add the segment up to this crossing
                        # For bullish cycles, end at the candle just before the crossing (crossing_row - 1)
                        # For bearish cycles, end at the candle just before the crossing (crossing_row - 1)
                        # The first cycle is a special case
                        end_row = crossing_row
                        if i > 0:  # Not the first cycle
                            if current_is_bullish:  # Current cycle is bullish
                                # Bullish cycle ends at the candle just before the crossing
                                end_row = crossing_row - 1 if crossing_row > 0 else 0
                            else:  # Current cycle is bearish
                                # Bearish cycle ends at the candle just before the crossing
                                end_row = crossing_row - 1 if crossing_row > 0 else 0

                        cycle_segments.append({
                            'start_row': segment_start_idx,
                            'end_row': end_row,
                            'is_bullish': current_is_bullish,
                            'pivot_price': segment_pivot
                        })

                        # Debug output for cycle segment
                        print(f"Cycle Segment: Rows {segment_start_idx}-{end_row}, " +
                              f"Is bullish: {current_is_bullish}, Pivot: {segment_pivot:.2f}")

                        # Store the pivot price for this segment (if not already stored)
                        if self.calculation_mode != "cycle_pivot":
                            for row in range(segment_start_idx, end_row + 1):
                                cycle_pivot_prices[row] = segment_pivot

                        # Update for next segment
                        # The next cycle starts at the crossing point
                        segment_start_idx = crossing_row
                        current_is_bullish = not current_is_bullish

                # Add the final segment after the last crossing
                # For the last segment, use the current pivot price
                # If we're in cycle_pivot mode, use the already calculated pivot price
                if self.calculation_mode == "cycle_pivot" and len(data) - 1 in cycle_pivot_prices:
                    final_pivot = cycle_pivot_prices[len(data) - 1]
                else:
                    final_pivot = pivot_price

                cycle_segments.append({
                    'start_row': segment_start_idx,
                    'end_row': len(data) - 1,
                    'is_bullish': current_is_bullish,
                    'pivot_price': final_pivot
                })

                # Debug output for final cycle segment
                print(f"Final Cycle Segment: Rows {segment_start_idx}-{len(data) - 1}, " +
                      f"Is bullish: {current_is_bullish}, Pivot: {final_pivot:.2f}")

                # Store the pivot price for the final segment (if not already stored)
                if self.calculation_mode != "cycle_pivot":
                    for row in range(segment_start_idx, len(data)):
                        cycle_pivot_prices[row] = final_pivot

                # Now assign positions within each segment
                for segment in cycle_segments:
                    for row in range(segment['start_row'], segment['end_row'] + 1):
                        position = row - segment['start_row'] + 1
                        is_bullish_cycle[row] = segment['is_bullish']
                        cycle_positions[row] = position

                        # Debug print
                        print(f"Row {row}, Segment {segment['start_row']}-{segment['end_row']}, " +
                              f"Is bullish: {segment['is_bullish']}, Position: {position}")

            # Add the actual date and time from the original data if we're not in historical_pivots mode
            # (historical_pivots mode already has the Time column)
            if self.calculation_mode != "historical_pivots" and hasattr(market_odds_tab, 'data') and market_odds_tab.data is not None and not market_odds_tab.data.empty:
                # Get the timestamps from the original data
                timestamps = market_odds_tab.data.index

                # Create a new Time column with the actual date and time
                time_values = []
                for i, row in data.iterrows():
                    idx = int(row['Index'])
                    if idx < len(timestamps):
                        time_values.append(timestamps[idx])
                    else:
                        time_values.append(pd.NaT)  # Not a Time value for out-of-bounds indices

                # Replace the Index column with the Time column
                data['Time'] = time_values
                data = data.drop('Index', axis=1)

            # Store the percentage-based categorization first
            percentage_based_categories = {}
            percentage_based_peaks_troughs = {}
            percentage_based_is_bullish = {}

            # Only calculate percentage-based categorization once
            if hasattr(market_odds_tab, 'rebased_data') and market_odds_tab.rebased_data is not None:
                # Create a temporary DataFrame for percentage-based calculations
                temp_data = pd.DataFrame(market_odds_tab.rebased_data, columns=['Index', 'Open', 'High', 'Low', 'Close'])

                # Add a peak/trough column
                temp_data['Peak/Trough'] = ""

                # Apply the percentage-based categorization logic
                bullish_counter = 0  # Counter for bullish cycle
                bearish_counter = 0  # Counter for bearish cycle
                last_was_bullish = None  # Track the previous cycle type

                # Variables to track cycle transitions
                current_cycle_start = 0  # Start index of current cycle

                # First pass: Categorize candles and track cycle transitions
                cycle_transitions = []  # Store indices where cycles change

                # Get skip_candles value to match the invisible candles system
                from parameter_registry import default_registry
                vector_length = default_registry.get_value('vector_length')
                skip_candles = vector_length + 1

                for i in range(len(temp_data)):
                    # Skip the invisible candles - they are loaded but not used in the system
                    # The category starts after skip_candles since those rows are NOT used anywhere
                    # These invisible candles will be used for distance calculations so it does not START AT 0
                    if i < skip_candles:
                        # Store empty category for invisible candles (they are invisible to the system)
                        idx = temp_data.iloc[i]['Index']
                        percentage_based_categories[idx] = ""
                        percentage_based_is_bullish[idx] = None
                        continue

                    # Get the close value for this candle
                    close_val = temp_data.iloc[i]['Close']
                    is_bullish = close_val >= 0  # Bullish if close >= 0

                    # Check if we're starting a new cycle
                    if last_was_bullish is None:
                        # First candle (after skipping invisible candles)
                        current_cycle_start = i
                        if is_bullish:
                            bullish_counter = 1
                            category = f"{bullish_counter}H"
                        else:
                            bearish_counter = 1
                            category = f"{bearish_counter}L"
                    elif is_bullish != last_was_bullish:
                        # Transition between cycles - store the transition point
                        cycle_transitions.append((current_cycle_start, i-1, last_was_bullish))
                        current_cycle_start = i

                        # Reset the appropriate counter
                        if is_bullish:
                            # Transitioning from bearish to bullish
                            bullish_counter = 1  # Reset bullish counter
                            category = f"{bullish_counter}H"
                        else:
                            # Transitioning from bullish to bearish
                            bearish_counter = 1  # Reset bearish counter
                            category = f"{bearish_counter}L"
                    else:
                        # Continuing the same cycle - increment the counter
                        if is_bullish:
                            bullish_counter += 1
                            category = f"{bullish_counter}H"
                        else:
                            bearish_counter += 1
                            category = f"{bearish_counter}L"

                    # Store the category and bullish status for this index
                    idx = temp_data.iloc[i]['Index']
                    percentage_based_categories[idx] = category
                    percentage_based_is_bullish[idx] = is_bullish

                    # Remember the current cycle type for the next iteration
                    last_was_bullish = is_bullish

                # Add the last cycle (only if we have processed any data after skipping invisible candles)
                if len(temp_data) > skip_candles and last_was_bullish is not None:
                    cycle_transitions.append((current_cycle_start, len(temp_data)-1, last_was_bullish))

                # Second pass: Mark peaks and troughs for each cycle
                for start_idx, end_idx, is_bullish in cycle_transitions:
                    # Skip cycles that start in the invisible candles
                    if start_idx < skip_candles:
                        continue

                    if is_bullish:
                        # Find the peak (highest high) in this bullish cycle
                        peak_idx = start_idx
                        peak_value = temp_data.iloc[start_idx]['High']

                        for i in range(start_idx, end_idx + 1):
                            if i >= skip_candles and temp_data.iloc[i]['High'] > peak_value:
                                peak_value = temp_data.iloc[i]['High']
                                peak_idx = i

                        # Mark the peak
                        idx = temp_data.iloc[peak_idx]['Index']
                        percentage_based_peaks_troughs[idx] = "peak"
                        print(f"Percentage-based: Marked peak at index {idx} with value {peak_value:.4f} in bullish cycle {start_idx}-{end_idx}")
                    else:
                        # Find the trough (lowest low) in this bearish cycle
                        trough_idx = start_idx
                        trough_value = temp_data.iloc[start_idx]['Low']

                        for i in range(start_idx, end_idx + 1):
                            if i >= skip_candles and temp_data.iloc[i]['Low'] < trough_value:
                                trough_value = temp_data.iloc[i]['Low']
                                trough_idx = i

                        # Mark the trough
                        idx = temp_data.iloc[trough_idx]['Index']
                        percentage_based_peaks_troughs[idx] = "trough"
                        print(f"Percentage-based: Marked trough at index {idx} with value {trough_value:.4f} in bearish cycle {start_idx}-{end_idx}")

                # Debug output
                print(f"Calculated percentage-based categories for {len(percentage_based_categories)} indices")

                # Store the percentage-based categories in the market_odds_tab for access by other components
                market_odds_tab.percentage_based_categories = percentage_based_categories
                market_odds_tab.percentage_based_peaks_troughs = percentage_based_peaks_troughs
                market_odds_tab.percentage_based_is_bullish = percentage_based_is_bullish
                print(f"Stored percentage_based_categories in market_odds_tab with {len(percentage_based_categories)} entries")

            # Add Category column with cycle position information if we're not in historical_pivots mode
            # (historical_pivots mode already has the Cycle Position column)
            if self.calculation_mode != "historical_pivots":
                data['Category'] = ""

                # Make sure we have a Peak/Trough column for all tabs
                if 'Peak/Trough' not in data.columns:
                    data['Peak/Trough'] = ""
                else:
                    # Clear any existing peak/trough markings
                    data['Peak/Trough'] = ""

                # Apply the percentage-based categorization to this tab
                for i in range(len(data)):
                    idx = None

                    # Get the original index for this row
                    if 'Index' in data.columns:
                        idx = data.iloc[i]['Index']
                    elif i < len(market_odds_tab.rebased_data):
                        # If Index column was already removed, use the row number to get the original index
                        idx = market_odds_tab.rebased_data[i][0]

                    if idx is not None and idx in percentage_based_categories:
                        # Copy the category from percentage-based tab
                        data.loc[i, 'Category'] = percentage_based_categories[idx]

                        # Copy the peak/trough marking if it exists
                        if idx in percentage_based_peaks_troughs:
                            data.loc[i, 'Peak/Trough'] = percentage_based_peaks_troughs[idx]

                        # Copy the bullish status for coloring
                        is_bullish_cycle[i] = percentage_based_is_bullish[idx]

                        print(f"{self.calculation_mode}: Copied category {data.loc[i, 'Category']} and peak/trough {data.loc[i, 'Peak/Trough']} from percentage-based tab for index {idx}")
                    else:
                        # If we don't have percentage-based data for this index, use a default
                        print(f"{self.calculation_mode}: No percentage-based data for index {idx}, using default")

                        # Default to using the close value for this tab
                        if 'Close' in data.columns:
                            close_val = data.iloc[i]['Close']
                            is_bullish = close_val >= 0
                            data.loc[i, 'Category'] = "1H" if is_bullish else "1L"
                            is_bullish_cycle[i] = is_bullish
                        else:
                            # If no Close column, default to bullish
                            data.loc[i, 'Category'] = "1H"
                            is_bullish_cycle[i] = True

                # Reorder columns based on calculation mode
                if self.calculation_mode == "current_price":
                    # For current price mode, order columns as: index | category | $ change high | % change high | $ change low | % change low | projected high | projected low | updated | peak/trough

                    # Apply the matching mode filter to the Updated column
                    if matching_mode:
                        # Get the latest category for H/L matching
                        if matching_mode == 'hl':
                            latest_category = None

                            # Try to get the latest category from the data
                            if len(data) > 0 and 'Category' in data.columns:
                                latest_category = data.iloc[-1]['Category']
                                print(f"Latest category from data: {latest_category}")

                            # If we couldn't get the category, use a default
                            if latest_category is None:
                                # Determine if the last row is bullish or bearish
                                latest_is_bullish = False  # Default to bearish

                                if hasattr(market_odds_tab, 'below_vector'):
                                    # Use the below_vector status to determine if we're in a bullish or bearish cycle
                                    latest_is_bullish = not market_odds_tab.below_vector
                                    print(f"Latest bullish/bearish status from below_vector: {'bullish' if latest_is_bullish else 'bearish'}")
                                elif hasattr(market_odds_tab, 'data') and 'Close' in market_odds_tab.data.columns:
                                    # Fallback to using the last close value
                                    last_close = market_odds_tab.data['Close'].iloc[-1]
                                    latest_is_bullish = last_close >= 0
                                    print(f"Latest bullish/bearish status from last close value: {'bullish' if latest_is_bullish else 'bearish'}")

                                # Create a default category based on bullish/bearish status
                                latest_category = "1H" if latest_is_bullish else "1L"
                                print(f"Created default latest category: {latest_category}")

                            # Apply the filter to the Updated column
                            for i in range(len(data)):
                                current_category = data.iloc[i]['Category']
                                if current_category != latest_category:
                                    # If the category doesn't match, clear the Updated value
                                    data.loc[i, 'Updated'] = "None"
                                    print(f"Cleared Updated value for row {i} - category mismatch: row is {current_category}, latest is {latest_category}")

                        # For Weekday Matching, filter by weekday
                        elif matching_mode == 'weekday':
                            # Get the latest date
                            latest_date = None
                            if hasattr(market_odds_tab, 'data') and len(market_odds_tab.data) > 0 and hasattr(market_odds_tab.data, 'index'):
                                latest_date = market_odds_tab.data.index[-1]

                            if latest_date is not None:
                                latest_weekday = latest_date.weekday()

                                # Apply the filter to the Updated column
                                for i in range(len(data)):
                                    # Get the date for this row
                                    if 'Time' in data.columns and pd.notna(data.iloc[i]['Time']):
                                        current_date = data.iloc[i]['Time']
                                        current_weekday = current_date.weekday()

                                        if current_weekday != latest_weekday:
                                            # If the weekday doesn't match, clear the Updated value
                                            data.loc[i, 'Updated'] = "None"
                                            print(f"Cleared Updated value for row {i} - weekday mismatch: row is {current_weekday}, latest is {latest_weekday}")

                    data = data[['Time', 'Category', '$ Change High', '% Change High', '$ Change Low', '% Change Low', 'Projected High', 'Projected Low', 'Updated', 'Peak/Trough']]
                elif self.calculation_mode == "percentage_based":
                    # For percentage based mode, include the Peak/Trough column
                    data = data[['Time', 'Open', 'High', 'Low', 'Close', 'Peak/Trough', 'Category']]
                else:
                    # For other modes, use the standard order: Time, Open, High, Low, Close, Peak/Trough, Category
                    data = data[['Time', 'Open', 'High', 'Low', 'Close', 'Peak/Trough', 'Category']]

            # Update the model with the new data and cycle information
            # Print debug info about the is_bullish_cycle dictionary
            print(f"is_bullish_cycle dictionary has {len(is_bullish_cycle)} entries")
            for i in range(min(5, len(is_bullish_cycle))):  # Print first 5 entries for debugging
                print(f"is_bullish_cycle[{i}] = {is_bullish_cycle[i]}")

            self.table_model.setData(data, is_bullish_cycle)

            # For current_price mode, we don't need to hide any columns
            # since we've already set up the DataFrame with the correct columns
            if self.calculation_mode != "current_price":
                # Make sure all columns are visible for other modes
                self.table_view.setColumnHidden(1, False)  # Show Open column
                self.table_view.setColumnHidden(4, False)  # Show Close column
        else:
            self.status_label.setText("Error: No rebased data available in Market Odds tab")
            return

        # Update status label
        symbol = market_odds_tab.symbol_input.text().strip().upper() if hasattr(market_odds_tab, 'symbol_input') else "Unknown"
        timeframe = market_odds_tab.timeframe_combo.currentText() if hasattr(market_odds_tab, 'timeframe_combo') else "Unknown"

        # Add calculation mode to status label
        mode_display = {
            "percentage_based": "Percentage Based",
            "current_price": "Current Price (Uses Real OHLC Data from OHLC Tab)",
            "pivot_price": "Extrema Price",
            "cycle_pivot": "OHLC (Real OHLC Data)",
            "historical_pivots": "The Line Prices Tab with Extrema Prices"
        }.get(self.calculation_mode, "Unknown")

        self.status_label.setText(f"Loaded {len(data) if 'data' in locals() else 0} rows of {symbol} {timeframe} data [{mode_display}]")

        # No need to resize columns as they're set to stretch evenly

        print(f"Refreshed rebased data table with {len(data) if 'data' in locals() else 0} rows")

    def on_calculation_mode_changed(self, checked):
        """Handle calculation mode change

        Args:
            checked: Whether the button is checked
        """
        if not checked:
            # Skip the unchecked signal
            return

        # Determine which button was toggled on
        if self.percentage_based_btn.isChecked():
            self.calculation_mode = "percentage_based"
        elif self.current_price_btn.isChecked():
            self.calculation_mode = "current_price"
        elif self.pivot_price_btn.isChecked():
            self.calculation_mode = "pivot_price"
        elif self.cycle_pivot_btn.isChecked():
            self.calculation_mode = "cycle_pivot"
        elif self.historical_pivots_btn.isChecked():
            self.calculation_mode = "historical_pivots"
            print("The Line Prices mode selected")

        print(f"Calculation mode changed to: {self.calculation_mode}")

        # Refresh the data with the new calculation mode
        self.refresh_data()

    def showEvent(self, event):
        """Handle show event to refresh data when tab is shown."""
        super().showEvent(event)
        # Refresh data when tab is shown
        self.refresh_data()
