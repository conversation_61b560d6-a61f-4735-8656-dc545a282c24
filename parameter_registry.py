"""
Parameter Registry System for Online Learning

This module provides a centralized registry for all tunable parameters in the system,
allowing for comprehensive online learning and optimization of all filter parameters.
"""

import numpy as np
import pandas as pd
from collections import defaultdict
import logging
import json
import os
from datetime import datetime
from PyQt6 import QtCore

# Note: Main logging configuration is in main.py
# This is just a fallback in case this module is used standalone
logger = logging.getLogger('ParameterRegistry')
logger.setLevel(logging.ERROR)  # Minimal verbosity, only errors
if not logger.handlers:
    handler = logging.StreamHandler()
    formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    handler.setFormatter(formatter)
    logger.addHandler(handler)

class Parameter:
    """
    Represents a tunable parameter in the system.
    """
    def __init__(self, name, value, min_value=None, max_value=None, step=None,
                 category=None, description=None, is_continuous=True,
                 possible_values=None, learning_rate=0.01):
        """
        Initialize a parameter.

        Args:
            name: Parameter name
            value: Current parameter value
            min_value: Minimum allowed value (for continuous parameters)
            max_value: Maximum allowed value (for continuous parameters)
            step: Step size for parameter updates (for continuous parameters)
            category: Category this parameter belongs to (e.g., 'RSI', 'Volatility')
            description: Human-readable description of the parameter
            is_continuous: Whether this is a continuous parameter (vs. categorical)
            possible_values: List of possible values (for categorical parameters)
            learning_rate: Learning rate for parameter updates
        """
        self.name = name
        self.value = value
        self.min_value = min_value
        self.max_value = max_value
        self.step = step
        self.category = category
        self.description = description
        self.is_continuous = is_continuous
        self.possible_values = possible_values
        self.learning_rate = learning_rate

        # Performance tracking
        self.performance_history = []
        self.update_history = []
        self.exploration_noise = 0.1  # Initial exploration noise

        # Validate parameter
        self._validate()

    def _validate(self):
        """Validate parameter configuration."""
        if self.is_continuous:
            if self.min_value is not None and self.max_value is not None:
                if self.value < self.min_value or self.value > self.max_value:
                    logger.warning(f"Parameter {self.name} value {self.value} outside range [{self.min_value}, {self.max_value}]")
                    self.value = max(self.min_value, min(self.max_value, self.value))
        else:
            if self.possible_values is not None:
                if self.value not in self.possible_values:
                    logger.warning(f"Parameter {self.name} value {self.value} not in possible values {self.possible_values}")
                    self.value = self.possible_values[0] if self.possible_values else None

    def update(self, performance, direction=None, exploration=True):
        """
        Update parameter value based on performance feedback.

        Args:
            performance: Performance score (0-1, higher is better)
            direction: Direction to adjust parameter (1 for increase, -1 for decrease, None for auto)
            exploration: Whether to add exploration noise

        Returns:
            The new parameter value
        """
        # Record performance
        self.performance_history.append((datetime.now(), self.value, performance))

        # Determine update direction if not specified
        if direction is None:
            # Use last 3 performance points if available
            if len(self.performance_history) >= 3:
                recent_history = self.performance_history[-3:]
                # If performance is improving, continue in the same direction
                if recent_history[-1][2] > recent_history[-2][2] > recent_history[-3][2]:
                    # Continue in the same direction as the last update
                    if len(self.update_history) > 0:
                        direction = self.update_history[-1][1]
                    else:
                        direction = 1  # Default to increasing
                # If performance is decreasing, reverse direction
                elif recent_history[-1][2] < recent_history[-2][2]:
                    # Reverse the direction of the last update
                    if len(self.update_history) > 0:
                        direction = -self.update_history[-1][1]
                    else:
                        direction = -1  # Default to decreasing
                else:
                    # No clear trend, use random direction
                    direction = np.random.choice([-1, 1])
            else:
                # Not enough history, use random direction
                direction = np.random.choice([-1, 1])

        # Update parameter value
        old_value = self.value

        if self.is_continuous:
            # Calculate step size based on performance
            # Higher performance = smaller steps (fine-tuning)
            # Lower performance = larger steps (exploration)
            adaptive_step = self.step * (1.0 - 0.5 * performance)

            # Add exploration noise if enabled
            if exploration:
                noise = np.random.normal(0, self.exploration_noise * adaptive_step)
            else:
                noise = 0

            # Update value
            delta = direction * adaptive_step + noise
            self.value += delta

            # Ensure value is within bounds
            if self.min_value is not None:
                self.value = max(self.min_value, self.value)
            if self.max_value is not None:
                self.value = min(self.max_value, self.value)

        else:
            # For categorical parameters, select a different value
            if self.possible_values and len(self.possible_values) > 1:
                current_index = self.possible_values.index(self.value)
                if direction > 0:
                    new_index = (current_index + 1) % len(self.possible_values)
                else:
                    new_index = (current_index - 1) % len(self.possible_values)
                self.value = self.possible_values[new_index]

        # Record update
        self.update_history.append((datetime.now(), direction, old_value, self.value))

        # Adjust exploration noise based on performance
        # Higher performance = less exploration
        self.exploration_noise = max(0.01, 0.1 * (1.0 - performance))

        return self.value

    def reset(self):
        """Reset parameter to initial value."""
        if self.update_history:
            self.value = self.update_history[0][2]  # Original value
        self.exploration_noise = 0.1  # Reset exploration noise

    def to_dict(self):
        """Convert parameter to dictionary for serialization."""
        return {
            'name': self.name,
            'value': self.value,
            'min_value': self.min_value,
            'max_value': self.max_value,
            'step': self.step,
            'category': self.category,
            'description': self.description,
            'is_continuous': self.is_continuous,
            'possible_values': self.possible_values,
            'learning_rate': self.learning_rate,
            'exploration_noise': self.exploration_noise
        }

    @classmethod
    def from_dict(cls, data):
        """Create parameter from dictionary."""
        param = cls(
            name=data['name'],
            value=data['value'],
            min_value=data['min_value'],
            max_value=data['max_value'],
            step=data['step'],
            category=data['category'],
            description=data['description'],
            is_continuous=data['is_continuous'],
            possible_values=data['possible_values'],
            learning_rate=data['learning_rate']
        )
        param.exploration_noise = data.get('exploration_noise', 0.1)
        return param


class ParameterRegistry(QtCore.QObject):
    """
    Central registry for all tunable parameters in the system.
    """
    # Define signals for parameter changes
    parameter_changed = QtCore.pyqtSignal(str, object)  # name, new_value
    ui_parameter_changed = QtCore.pyqtSignal(str, object)  # name, new_value

    def __init__(self):
        """Initialize the parameter registry."""
        super().__init__()
        self.parameters = {}
        self.categories = defaultdict(list)
        self.performance_history = []
        self.last_update_time = None
        self.update_count = 0

    def register_parameter(self, parameter):
        """
        Register a parameter with the registry.

        Args:
            parameter: Parameter object to register

        Returns:
            The registered parameter
        """
        self.parameters[parameter.name] = parameter
        if parameter.category:
            self.categories[parameter.category].append(parameter.name)
        return parameter

    def get_parameter(self, name):
        """
        Get a parameter by name.

        Args:
            name: Parameter name

        Returns:
            Parameter object or None if not found
        """
        return self.parameters.get(name)

    def get_value(self, name):
        """
        Get a parameter's current value.

        Args:
            name: Parameter name

        Returns:
            Parameter value or None if not found
        """
        param = self.get_parameter(name)
        return param.value if param else None

    def set_value(self, name, value):
        """
        Set a parameter's value.

        Args:
            name: Parameter name
            value: New parameter value

        Returns:
            True if successful, False otherwise
        """
        param = self.get_parameter(name)
        if param:
            old_value = param.value
            param.value = value
            param._validate()
            param.update_history.append((datetime.now(), 0, old_value, param.value))

            # Emit the appropriate signal
            self.parameter_changed.emit(name, param.value)

            # If this is a UI parameter, also emit the UI-specific signal
            if param.category == 'UI':
                self.ui_parameter_changed.emit(name, param.value)

            return True
        return False

    def update_parameter(self, name, performance, direction=None, exploration=True):
        """
        Update a parameter based on performance feedback.

        Args:
            name: Parameter name
            performance: Performance score (0-1, higher is better)
            direction: Direction to adjust parameter (1 for increase, -1 for decrease, None for auto)
            exploration: Whether to add exploration noise

        Returns:
            The new parameter value or None if parameter not found
        """
        param = self.get_parameter(name)
        if param:
            return param.update(performance, direction, exploration)
        return None

    def update_category(self, category, performance, exploration=True):
        """
        Update all parameters in a category based on performance feedback.

        Args:
            category: Category name
            performance: Performance score (0-1, higher is better)
            exploration: Whether to add exploration noise

        Returns:
            Dictionary of parameter names and their new values
        """
        results = {}
        for name in self.categories.get(category, []):
            results[name] = self.update_parameter(name, performance, None, exploration)
        return results

    def update_all(self, performance, exploration=True):
        """
        Update all parameters based on performance feedback.

        Args:
            performance: Performance score (0-1, higher is better)
            exploration: Whether to add exploration noise

        Returns:
            Dictionary of parameter names and their new values
        """
        results = {}
        for name in self.parameters:
            results[name] = self.update_parameter(name, performance, None, exploration)

        # Record overall performance
        self.performance_history.append((datetime.now(), performance))
        self.last_update_time = datetime.now()
        self.update_count += 1

        return results

    def get_parameters_by_category(self, category):
        """
        Get all parameters in a category.

        Args:
            category: Category name

        Returns:
            Dictionary of parameter names and their objects
        """
        return {name: self.parameters[name] for name in self.categories.get(category, [])}

    def save(self, filename='parameter_registry.json'):
        """
        Save the parameter registry to a file.

        Args:
            filename: File to save to

        Returns:
            True if successful, False otherwise
        """
        try:
            data = {
                'parameters': {name: param.to_dict() for name, param in self.parameters.items()},
                'update_count': self.update_count,
                'last_update_time': self.last_update_time.isoformat() if self.last_update_time else None
            }
            with open(filename, 'w') as f:
                json.dump(data, f, indent=2)
            return True
        except Exception as e:
            logger.error(f"Error saving parameter registry: {str(e)}")
            return False

    @classmethod
    def load(cls, filename='parameter_registry.json'):
        """
        Load the parameter registry from a file.

        Args:
            filename: File to load from

        Returns:
            ParameterRegistry object or None if loading failed
        """
        try:
            if not os.path.exists(filename):
                logger.warning(f"Parameter registry file {filename} not found")
                return None

            with open(filename, 'r') as f:
                data = json.load(f)

            registry = cls()
            # Load all parameters from the data
            for _, param_data in data.get('parameters', {}).items():
                registry.register_parameter(Parameter.from_dict(param_data))

            registry.update_count = data.get('update_count', 0)
            if data.get('last_update_time'):
                registry.last_update_time = datetime.fromisoformat(data['last_update_time'])

            return registry
        except Exception as e:
            logger.error(f"Error loading parameter registry: {str(e)}")
            return None

    def get_performance_stats(self):
        """
        Get performance statistics.

        Returns:
            Dictionary of performance statistics
        """
        if not self.performance_history:
            return {
                'count': 0,
                'mean': 0,
                'min': 0,
                'max': 0,
                'recent': 0
            }

        performances = [p[1] for p in self.performance_history]
        return {
            'count': len(performances),
            'mean': np.mean(performances),
            'min': np.min(performances),
            'max': np.max(performances),
            'recent': performances[-1] if performances else 0
        }

    def get_parameter_performance(self, name):
        """
        Get performance statistics for a specific parameter.

        Args:
            name: Parameter name

        Returns:
            Dictionary of parameter performance statistics or None if parameter not found
        """
        param = self.get_parameter(name)
        if not param or not param.performance_history:
            return None

        performances = [p[2] for p in param.performance_history]
        values = [p[1] for p in param.performance_history]

        # Find the value with the best performance
        best_idx = np.argmax(performances)
        best_value = values[best_idx]
        best_performance = performances[best_idx]

        return {
            'count': len(performances),
            'mean_performance': np.mean(performances),
            'best_value': best_value,
            'best_performance': best_performance,
            'current_value': param.value,
            'current_performance': performances[-1] if performances else 0
        }

    def reset_exploration(self):
        """Reset exploration noise for all parameters."""
        for param in self.parameters.values():
            param.exploration_noise = 0.1

    def create_default_registry():
        """
        Create a default parameter registry with common parameters.

        Returns:
            ParameterRegistry with default parameters
        """
        registry = ParameterRegistry()

        # RSI parameters
        registry.register_parameter(Parameter(
            name='rsi_period',
            value=14,
            min_value=5,
            max_value=30,
            step=1,
            category='RSI',
            description='RSI calculation period',
            is_continuous=True
        ))

        registry.register_parameter(Parameter(
            name='rsi_overbought',
            value=70,
            min_value=60,
            max_value=90,
            step=1,
            category='RSI',
            description='RSI overbought threshold',
            is_continuous=True
        ))

        registry.register_parameter(Parameter(
            name='rsi_oversold',
            value=30,
            min_value=10,
            max_value=40,
            step=1,
            category='RSI',
            description='RSI oversold threshold',
            is_continuous=True
        ))

        # Volatility parameters
        registry.register_parameter(Parameter(
            name='atr_period',
            value=14,
            min_value=5,
            max_value=30,
            step=1,
            category='Volatility',
            description='ATR calculation period',
            is_continuous=True
        ))

        registry.register_parameter(Parameter(
            name='volatility_threshold_low',
            value=10,
            min_value=5,
            max_value=20,
            step=0.5,
            category='Volatility',
            description='Low volatility threshold (%)',
            is_continuous=True
        ))

        registry.register_parameter(Parameter(
            name='volatility_threshold_high',
            value=30,
            min_value=20,
            max_value=50,
            step=0.5,
            category='Volatility',
            description='High volatility threshold (%)',
            is_continuous=True
        ))

        # Confirmation parameters
        registry.register_parameter(Parameter(
            name='base_confirmation_period',
            value=3,
            min_value=1,
            max_value=10,
            step=1,
            category='Confirmation',
            description='Base number of candles for signal confirmation',
            is_continuous=True
        ))

        registry.register_parameter(Parameter(
            name='min_magnitude',
            value=0.05,
            min_value=0.01,
            max_value=0.5,
            step=0.01,
            category='Confirmation',
            description='Minimum price movement magnitude for valid signal (%)',
            is_continuous=True
        ))

        # Classifier parameters
        registry.register_parameter(Parameter(
            name='lookback_window',
            value=5,
            min_value=3,
            max_value=20,
            step=1,
            category='Classifier',
            description='Lookback window for feature extraction',
            is_continuous=True
        ))

        registry.register_parameter(Parameter(
            name='lookahead_window',
            value=10,
            min_value=5,
            max_value=30,
            step=1,
            category='Classifier',
            description='Lookahead window for outcome determination',
            is_continuous=True
        ))

        registry.register_parameter(Parameter(
            name='reversal_threshold',
            value=0.5,
            min_value=0.1,
            max_value=2.0,
            step=0.1,
            category='Classifier',
            description='Price movement threshold for reversal classification (%)',
            is_continuous=True
        ))

        # Confidence parameters
        registry.register_parameter(Parameter(
            name='confidence_threshold',
            value=0.7,
            min_value=0.5,
            max_value=0.95,
            step=0.01,
            category='Confidence',
            description='Minimum confidence threshold for signal acceptance',
            is_continuous=True
        ))

        # Adaptive learning parameters
        registry.register_parameter(Parameter(
            name='drift_threshold',
            value=0.15,
            min_value=0.05,
            max_value=0.3,
            step=0.01,
            category='AdaptiveLearning',
            description='Accuracy drop threshold for concept drift detection',
            is_continuous=True
        ))

        registry.register_parameter(Parameter(
            name='learning_rate',
            value=0.01,
            min_value=0.001,
            max_value=0.1,
            step=0.001,
            category='AdaptiveLearning',
            description='Learning rate for model updates',
            is_continuous=True
        ))

        # Component weights
        registry.register_parameter(Parameter(
            name='classifier_weight',
            value=0.4,
            min_value=0.1,
            max_value=0.8,
            step=0.05,
            category='Weights',
            description='Weight for classifier component',
            is_continuous=True
        ))

        registry.register_parameter(Parameter(
            name='pivot_weight',
            value=0.3,
            min_value=0.1,
            max_value=0.8,
            step=0.05,
            category='Weights',
            description='Weight for pivot component',
            is_continuous=True
        ))

        registry.register_parameter(Parameter(
            name='rsi_weight',
            value=0.15,
            min_value=0.05,
            max_value=0.5,
            step=0.05,
            category='Weights',
            description='Weight for RSI component',
            is_continuous=True
        ))

        registry.register_parameter(Parameter(
            name='volatility_weight',
            value=0.1,
            min_value=0.05,
            max_value=0.5,
            step=0.05,
            category='Weights',
            description='Weight for volatility component',
            is_continuous=True
        ))

        registry.register_parameter(Parameter(
            name='magnitude_weight',
            value=0.05,
            min_value=0.01,
            max_value=0.3,
            step=0.01,
            category='Weights',
            description='Weight for magnitude component',
            is_continuous=True
        ))

        # Universal UI parameters
        registry.register_parameter(Parameter(
            name='timeframe',
            value='15m',
            category='UI',
            description='Chart timeframe',
            is_continuous=False,
            possible_values=['1m', '2m', '5m', '15m', '30m', '60m', '1h', '1d', '1wk', '1mo']
        ))

        registry.register_parameter(Parameter(
            name='vector_length',
            value=20,
            min_value=1,
            max_value=1000,
            step=1,
            category='UI',
            description='Vector length for calculations',
            is_continuous=True
        ))

        registry.register_parameter(Parameter(
            name='days_to_load',
            value=10,
            min_value=1,
            max_value=10000,  # Set a very high maximum (effectively no limit)
            step=1,
            category='UI',
            description='Number of days of data to load',
            is_continuous=True
        ))

        registry.register_parameter(Parameter(
            name='symbol',
            value='SPY',
            category='UI',
            description='Current symbol being analyzed',
            is_continuous=False
        ))

        # Loading screen configuration
        registry.register_parameter(Parameter(
            name='use_background_loading',
            value=True,
            category='UI',
            description='Use background loading instead of popup windows',
            is_continuous=False
        ))

        # Dialog suppression configuration
        registry.register_parameter(Parameter(
            name='suppress_dialogs',
            value=True,
            category='UI',
            description='Suppress popup dialogs during background operations',
            is_continuous=False
        ))

        registry.register_parameter(Parameter(
            name='suppress_info_dialogs',
            value=True,
            category='UI',
            description='Suppress information dialogs',
            is_continuous=False
        ))

        registry.register_parameter(Parameter(
            name='suppress_warning_dialogs',
            value=True,
            category='UI',
            description='Suppress warning dialogs',
            is_continuous=False
        ))

        registry.register_parameter(Parameter(
            name='suppress_error_dialogs',
            value=False,
            category='UI',
            description='Suppress error dialogs (keep critical errors visible)',
            is_continuous=False
        ))

        return registry


# Create a global instance for easy access
default_registry = ParameterRegistry.create_default_registry()
